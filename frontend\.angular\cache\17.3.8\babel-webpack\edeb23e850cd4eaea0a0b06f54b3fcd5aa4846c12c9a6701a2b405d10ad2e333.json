{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, startWith, switchMap, take } from 'rxjs/operators';\nimport { ClassroomType } from 'src/app/core/models/classroom.model';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/classroom.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/user.service\";\nimport * as i5 from \"src/app/core/services/calendar.service\";\nimport * as i6 from \"src/app/core/services/layout.service\";\nimport * as i7 from \"src/app/core/services/lesson.service\";\nimport * as i8 from \"src/app/core/services/toast.service\";\nimport * as i9 from \"@angular/router\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"../../../shared/block-viewer/block-viewer.component\";\nimport * as i13 from \"../../../shared/prime/prime/calendar-agenda/calendar-agenda.component\";\nimport * as i14 from \"primeng/button\";\nimport * as i15 from \"primeng/api\";\nimport * as i16 from \"primeng/scrollpanel\";\nimport * as i17 from \"primeng/dropdown\";\nconst _c0 = [\"bookingWrapper\"];\nconst _c1 = [\"scrollPanel\"];\nconst _c2 = a0 => ({\n  \"height\": a0\n});\nconst _c3 = () => ({\n  width: \"100%\"\n});\nconst _c4 = (a0, a1) => ({\n  \"availability\": a0,\n  \"no-availability\": a1\n});\nconst _c5 = a0 => ({\n  \"pointer-events-none\": a0\n});\nfunction BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 47);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.classroomService.getClassroomTypeSmallIconPath(ctx_r2.studentSelectedClassroom), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template, 1, 1, \"img\", 45);\n    i0.ɵɵelement(2, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.classroomService.getStudentNamesWithHTML(ctx_r2.studentSelectedClassroom), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template, 3, 2, \"div\", 43);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.studentSelectedClassroom);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 47);\n  }\n  if (rf & 2) {\n    const classroom_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.classroomService.getClassroomTypeSmallIconPath(classroom_r4), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template, 1, 1, \"img\", 45);\n    i0.ɵɵelement(2, \"div\", 46)(3, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48);\n    i0.ɵɵelement(5, \"img\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.classroomService.getStudentNamesWithHTML(classroom_r4, true), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.convertHoursToMinutesWithSuffix(ctx_r2.classroomService.getAccumulatedHoursLeft(classroom_r4.packages), true), \" available\");\n  }\n}\nfunction BookingSystemComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdown\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.studentSelectedClassroom, $event) || (ctx_r2.studentSelectedClassroom = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClassroomChange($event));\n    });\n    i0.ɵɵtemplate(2, BookingSystemComponent_ng_container_15_ng_template_2_Template, 1, 1, \"ng-template\", 41)(3, BookingSystemComponent_ng_container_15_ng_template_3_Template, 7, 3, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.availableClassrooms);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.studentSelectedClassroom);\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.selectedTeachersLabel.language, \" - \", ctx_r2.selectedTeachersLabel.teacherName, \"\");\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template, 3, 2, \"div\", 43);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedTeachersLabel);\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"img\", 53);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵelement(6, \"img\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const teacher_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/images/dashboard/calendar/\", teacher_r6.icon, \".svg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", teacher_r6.language, \" - \", teacher_r6.teacherName, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.convertHoursToMinutesWithSuffix(teacher_r6.hoursLeft, true), \" available\");\n  }\n}\nfunction BookingSystemComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdown\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedTeachersLabel, $event) || (ctx_r2.selectedTeachersLabel = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTeacherChange($event));\n    });\n    i0.ɵɵtemplate(2, BookingSystemComponent_ng_container_16_ng_template_2_Template, 1, 1, \"ng-template\", 41)(3, BookingSystemComponent_ng_container_16_ng_template_3_Template, 8, 5, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.teachersLabels);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedTeachersLabel);\n  }\n}\nfunction BookingSystemComponent_div_39_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r2.selectedDay, \"EEEE, dd/MM\"));\n  }\n}\nfunction BookingSystemComponent_div_39_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.selectedTimeSlot);\n  }\n}\nfunction BookingSystemComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 54)(2, \"div\", 55)(3, \"div\", 56);\n    i0.ɵɵtext(4, \"You have chosen a lesson for\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 57)(6, \"div\", 58)(7, \"div\", 59);\n    i0.ɵɵelement(8, \"i\", 60);\n    i0.ɵɵtemplate(9, BookingSystemComponent_div_39_span_9_Template, 3, 4, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵelement(11, \"i\", 61);\n    i0.ɵɵtemplate(12, BookingSystemComponent_div_39_span_12_Template, 2, 1, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\");\n    i0.ɵɵelement(14, \"i\", 62);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(17, \"div\", 63)(18, \"div\", 64)(19, \"div\", 65);\n    i0.ɵɵelement(20, \"img\", 66);\n    i0.ɵɵtext(21, \"Add to Google Calendar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 67);\n    i0.ɵɵelement(23, \"img\", 66);\n    i0.ɵɵtext(24, \"Add to Outlook Calendar \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 68)(26, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_div_39_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onLessonRequested());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDay);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedTimeSlot);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedDuration.viewValue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"loading\", ctx_r2.buttonLoading)(\"label\", ctx_r2.isRescheduling ? \"RESCHEDULE\" : \"REQUEST\");\n  }\n}\nfunction BookingSystemComponent_li_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(2, 2, day_r8, \"EEE\"), \" \", day_r8.getDate(), \"\");\n  }\n}\nfunction BookingSystemComponent_ng_container_57_a_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 75);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_ng_container_57_a_9_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.generalService.navigateToBuyPackage());\n    });\n    i0.ɵɵtext(1, \" Purchase Package \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingSystemComponent_ng_container_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵelement(3, \"img\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 73);\n    i0.ɵɵtext(5, \" You have \");\n    i0.ɵɵelementStart(6, \"b\");\n    i0.ɵɵtext(7, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" hours available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BookingSystemComponent_ng_container_57_a_9_Template, 2, 0, \"a\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isStudent);\n  }\n}\nfunction BookingSystemComponent_li_59_ul_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 76)(2, \"div\", 77)(3, \"label\", 78);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_li_59_ul_1_ng_container_1_Template_label_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const hour_r11 = i0.ɵɵnextContext().$implicit;\n      const day_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTimeSlotSelected(day_r12, hour_r11));\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const hour_r11 = i0.ɵɵnextContext().$implicit;\n    const day_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c4, ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11), !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11) && ctx_r2.authService.isTeacher));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c5, !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11)));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"checked\", ctx_r2.isSelectedDayAndTime(day_r12, hour_r11) && ctx_r2.radioChecked);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"radio\", day_r12, \"\", hour_r11, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(hour_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate2(\"id\", \"radio\", day_r12, \"\", hour_r11, \"\");\n    i0.ɵɵpropertyInterpolate(\"value\", hour_r11);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11))(\"checked\", ctx_r2.isSelectedDayAndTime(day_r12, hour_r11) && ctx_r2.radioChecked ? true : null);\n  }\n}\nfunction BookingSystemComponent_li_59_ul_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, BookingSystemComponent_li_59_ul_1_ng_container_1_Template, 6, 19, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r11 = ctx.$implicit;\n    const day_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11) || ctx_r2.authService.isTeacher);\n  }\n}\nfunction BookingSystemComponent_li_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, BookingSystemComponent_li_59_ul_1_Template, 2, 1, \"ul\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableHours);\n  }\n}\nexport class BookingSystemComponent {\n  constructor(classroomService, generalService, authService, userService, calendarService, layoutService, lessonService, toastService, route, router, cdRef, location) {\n    this.classroomService = classroomService;\n    this.generalService = generalService;\n    this.authService = authService;\n    this.userService = userService;\n    this.calendarService = calendarService;\n    this.layoutService = layoutService;\n    this.lessonService = lessonService;\n    this.toastService = toastService;\n    this.route = route;\n    this.router = router;\n    this.cdRef = cdRef;\n    this.location = location;\n    this.bookingWrapper = {};\n    this.scrollPanel = {};\n    this.studentSelectedClassroom = {};\n    this.startDate = new Date();\n    this.weekDays = [];\n    this.hours = [];\n    this.schedule = {};\n    this.availableHours = [];\n    this.subs = new SubSink();\n    this.classrooms = [];\n    this.classroomsWithRecurringLessons = [];\n    this.hasClassrooms = true;\n    this.availableTeachers = [];\n    this.lessonEvents = [];\n    this.selectedTeacher = null;\n    this.filteredLessonEvents = [];\n    this.user = {};\n    this.teachersLabels = [];\n    this.selectedTeachersLabel = null;\n    this.selectedHour = null;\n    this.duration = [];\n    this.availableDuration = [];\n    this.selectedDuration = null;\n    this.cdr = {};\n    this.containerHeight = '';\n    this.availableClassrooms = [];\n    this.UserRoles = UserRole;\n    this.radioChecked = false;\n    this.calendarAgendaUserId = '';\n    this.extraData = {};\n    this.extraClassroomData = {};\n    this.isRescheduling = false;\n    this.isPending = false;\n    this.mobileView = false;\n    this.daysRange = 7;\n    this.height = 0;\n    this.noClassroomsFound = false;\n    this.buttonLoading = false;\n    this.startDate = new Date();\n  }\n  ngOnInit() {\n    const queryParams = this.route.snapshot.queryParams;\n    const lessonString = queryParams['lesson'];\n    const classroomString = queryParams['classroom'];\n    const hasRescheduleString = queryParams['reschedule'];\n    const answeredString = queryParams['answered'];\n    this.mobileView = this.isMobileScreenSize();\n    if (hasRescheduleString) {\n      this.isRescheduling = true;\n    }\n    if (answeredString) {\n      this.isPending = true;\n    }\n    if (lessonString) {\n      const lesson = JSON.parse(lessonString);\n      console.log(lesson);\n      this.extraData = {\n        \"lessonId\": lesson.id,\n        \"lessonStatus\": lesson.status,\n        \"classroomId\": lesson.classroomId,\n        \"status\": lesson.status,\n        \"startingDate\": lesson.startingDate,\n        \"duration\": lesson.duration,\n        \"isRecccuring\": lesson.isRecccuring\n      };\n    }\n    if (classroomString) {\n      const classroom = JSON.parse(classroomString);\n      console.log(classroom);\n      this.extraClassroomData = {\n        \"classroomId\": classroom.id,\n        \"status\": classroom.status,\n        \"classroomType\": classroom.type\n      };\n    }\n    this.duration = [{\n      value: 15,\n      viewValue: '15m'\n    }, {\n      value: 30,\n      viewValue: '30m'\n    }, {\n      value: 45,\n      viewValue: '45m'\n    }, {\n      value: 60,\n      viewValue: '1h'\n    }, {\n      value: 75,\n      viewValue: '1h 15m'\n    }, {\n      value: 90,\n      viewValue: '1h 30m'\n    }, {\n      value: 105,\n      viewValue: '1h 45m'\n    }, {\n      value: 120,\n      viewValue: '2h'\n    }, {\n      value: 135,\n      viewValue: '2h 15m'\n    }, {\n      value: 150,\n      viewValue: '2h 30m'\n    }, {\n      value: 165,\n      viewValue: '2h 45m'\n    }, {\n      value: 180,\n      viewValue: '3h'\n    }];\n    this.availableDuration = [...this.duration];\n    this.selectedDuration = this.duration[3];\n    this.calculateWeekDays(new Date());\n    this.calculateHours();\n    this.initSchedule();\n    this.user = this.authService.getLoggedInUser();\n    this.prepareInitialData();\n  }\n  isMobileScreenSize() {\n    const screenWidth = window.innerWidth;\n    return screenWidth < 768;\n  }\n  isTabletScreenSize() {\n    const screenWidth = window.innerWidth;\n    return screenWidth < 1024; // Change the width here to the desired tablet width\n  }\n  ngOnChanges() {\n    this.mobileView = this.isMobileScreenSize();\n    console.log(this.mobileView);\n  }\n  ngAfterViewInit() {\n    this.subs.sink = this.generalService.deviceKind.pipe(take(2), switchMap(res => {\n      console.log(res);\n      if (res.is576 || res.is992) {\n        return of(0);\n      } else {\n        return this.layoutService.sideMenuHeight;\n      }\n    }), startWith(0)).subscribe(height => {\n      if (height !== 0) {\n        this.setDaysRange(7);\n        this.height = height;\n        this.bookingWrapper.nativeElement.style.height = height + 30 + 'px';\n      } else {\n        this.setDaysRange(3);\n        this.bookingWrapper.nativeElement.style.height = '100%';\n      }\n      this.cdRef.detectChanges(); // Manually trigger change detection\n    });\n    // this.subs.sink = this.layoutService.sideMenuHeight.subscribe((res) => {\n    //   this.bookingWrapper.nativeElement.style.height = (res + 'px');\n    // });\n    this.scrollToMiddle();\n  }\n  ngAfterContentInit() {}\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  setDaysRange(days) {\n    this.daysRange = days;\n  }\n  getDaysRange() {\n    return this.daysRange;\n  }\n  scrollToMiddle() {\n    setTimeout(() => {\n      const scrollContent = this.scrollPanel.containerViewChild.nativeElement.getElementsByClassName('p-scrollpanel-content')[0];\n      const middleScrollPosition = scrollContent.scrollHeight / 3;\n      scrollContent.scrollTop = middleScrollPosition;\n    }, 1000);\n  }\n  onDurationSelected(event) {\n    this.selectedTimeSlot = '';\n    this.radioChecked = false;\n    console.log(this.selectedDay);\n    // Add your own logic here to handle the selected duration\n  }\n  onTimeSlotSelected(day, hour) {\n    this.selectedDay = moment(day, \"ddd MMM DD YYYY\").toDate();\n    this.selectedTimeSlot = hour;\n    this.displayAvailableTimeSlots();\n    const selectedDateTime = moment(day, \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ\");\n    const formattedDateTime = selectedDateTime.format(\"ddd MMM DD YYYY \" + hour);\n    // TODO : implement uncheck all radios functionality\n    // if (this.radioChecked && this.timeSlotRadio.checked) {\n    //   if (this.timeSlotRadio) {\n    //     this.timeSlotRadio.checked = false;\n    //   }\n    //   this.resetSelectedTimeSlot();\n    //   return;\n    // }\n    if (this.user.role === UserRole.TEACHER) {\n      this.radioChecked = true;\n    } else {\n      if (!this.isDateTimeInThePast(new Date(formattedDateTime))) {\n        this.radioChecked = true;\n      } else {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Date is in the past. Please choose another date.'\n        });\n        this.radioChecked = false;\n      }\n    }\n  }\n  resetSelectedTimeSlot() {\n    this.selectedDay = new Date();\n    this.selectedTimeSlot = '';\n    this.displayAvailableTimeSlots();\n    this.radioChecked = false;\n  }\n  isSelectedDayAndTime(day, hour) {\n    const d = new Date(day);\n    if (this.hasExtraData()) {\n      return d.getDate() + '' + d.getMonth() === this.selectedDay.getDate() + '' + this.selectedDay.getMonth() && hour === this.selectedTimeSlot;\n    } else {\n      return day === this.selectedDay && hour === this.selectedTimeSlot;\n    }\n  }\n  /**\n   * The function `getTeacherAvailability` retrieves the calendar availability of a teacher based on\n   * their ASP user ID.\n   * @param {any} teacher - The `teacher` parameter is an object that represents a teacher. It contains\n   * information about the teacher, such as their ID (`aspUserId`).\n   * @returns an Observable of type RetrieveCalendarAvailabilityRequestData or null.\n   */\n  getTeacherAvailability(teacher) {\n    if (!teacher || this.generalService.isNullishObject(teacher)) {\n      return of(null);\n    }\n    const data = {\n      ...this.getDatesRange(),\n      userId: teacher.aspUserId\n    };\n    return this.getAvailabilityFromRange(data);\n  }\n  getAvailabilityFromRange(requestData) {\n    console.log(requestData);\n    return this.calendarService.retrieveCalendarAvailability(requestData).pipe(map(res => {\n      // const daysOff = res.daysOff;\n      // // Set business hours and select constraint\n      this.businessHours = res.calendarAvailability;\n      this.availableHours = this.displayAvailableTimeSlots();\n      // // Create events for each day off\n      // this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\n      return null;\n    }));\n  }\n  filterLessonsByTeacherId(lessons, teacherId) {\n    return lessons.filter(lesson => lesson.teacher.id === teacherId);\n  }\n  onTeacherChange(event) {\n    // Reset selected time slot and radio checked status\n    this.selectedTimeSlot = '';\n    this.radioChecked = false;\n    // Find the selected teacher based on the teacherId\n    this.selectedTeacher = this.availableTeachers.find(item => item.id === event.teacherId);\n    // Find the classroom based on the selected teacher's classroom label\n    const classroomId = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n    console.log(classroomId);\n    // Handle the available duration for the classroom type\n    this.handleClassroomTypeAvailableDuration(classroomId);\n    // Get the teacher's availability and update the filtered lesson events\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n      this.filteredLessonEvents = this.lessonEvents;\n      console.log('filteredLessonEvents', this.filteredLessonEvents);\n    }));\n    // Set the calendar agenda user ID to the selected teacher's ASP user ID\n    this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n  }\n  /**\n   * Handles the change event when a classroom is selected.\n   * It sets the selected classroom and updates the calendar agenda user ID if a teacher is associated with the classroom.\n   * It also handles the available duration based on the classroom type.\n   *\n   * @param event - The selected classroom object.\n   */\n  onClassroomChange(event) {\n    console.log(event);\n    this.studentSelectedClassroom = event;\n    console.log(event);\n    if (this.studentSelectedClassroom.teacher) {\n      this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId;\n    }\n    this.handleClassroomTypeAvailableDuration(event);\n  }\n  getTeacherLabel(teacher) {\n    return ` ${teacher.lastName} ${teacher.firstName}`;\n  }\n  /**\n   * Updates the selected classroom when a user chooses a classroom from the UI.\n   * @param event - The change event triggered by selecting a classroom.\n   */\n  /**\n   * Calculates the dates of the current week, starting from the given start date.\n   * @param startDate - The start date for the current week.\n   */\n  calculateWeekDays(startDate) {\n    const today = moment(startDate).toDate();\n    const firstDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n    const firstDayCopy = new Date(firstDay);\n    this.weekDays = Array.from({\n      length: this.daysRange\n    }, (_, i) => {\n      const d = new Date(firstDayCopy);\n      d.setDate(d.getDate() + i);\n      return d;\n    });\n  }\n  /**\n   * Returns the date range for the current week.\n   * @returns {Object} An object with \"from\" and \"to\" properties representing the start and end dates of the week.\n   */\n  getDatesRange() {\n    // Convert date strings to Date objects\n    const dates = this.weekDays.map(dateString => new Date(dateString));\n    // Get the range of dates from and to\n    const fromDate = dates[0];\n    const toDate = dates[dates.length - 1];\n    // console.log(fromDate);\n    // Log the range of dates\n    return {\n      fromDate: moment(fromDate, 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z',\n      toDate: moment(toDate, 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z'\n    };\n  }\n  findMonthFromDateRange() {\n    const data = this.getDatesRange();\n    const fromDate = moment(data.fromDate);\n    const toDate = moment(data.toDate);\n    const fromMonth = fromDate.format('MMMM');\n    const toMonth = toDate.format('MMMM');\n    const fromYear = fromDate.format('YYYY');\n    const toYear = toDate.format('YYYY');\n    if (fromMonth === toMonth) {\n      return `${fromMonth} ${fromYear}`;\n    } else {\n      return `${fromMonth} - ${toMonth} ${toYear}`;\n    }\n  }\n  displayAvailableTimeSlots() {\n    const weekDayAbbreviations = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\n    const availableHours = [];\n    const startHour = 0;\n    const endHour = 23;\n    const minuteIncrements = ['00', '15', '30', '45'];\n    for (let hour = startHour; hour <= endHour; hour++) {\n      const hourString = hour.toString().padStart(2, '0');\n      for (const minute of minuteIncrements) {\n        const timeString = `${hourString}:${minute}`;\n        let isAvailable = true;\n        // Check availability for each day\n        // for (const day of this.weekDays) {\n        //   const dayAbbr = weekDayAbbreviations[day.getDay()];\n        //   if (this.isTimeSlotAvailableOnDay(day.toDateString(), timeString)) {\n        //     isAvailable = true;\n        //   }\n        // }\n        if (isAvailable) {\n          availableHours.push(timeString);\n        }\n      }\n    }\n    return availableHours;\n  }\n  /**\n   * Calculates the available hours for scheduling.\n   */\n  calculateHours() {\n    const startHour = 0;\n    const endHour = 23;\n    const quarterHours = ['00', '15', '30', '45'];\n    this.hours = [];\n    for (let hour = startHour; hour <= endHour; hour++) {\n      const hourString = hour.toString().padStart(2, '0');\n      for (const minute of quarterHours) {\n        const timeString = `${hourString}:${minute}`;\n        this.hours.push(timeString);\n      }\n    }\n    for (let hour = 0; hour < startHour; hour++) {\n      const hourString = hour.toString().padStart(2, '0');\n      for (const minute of quarterHours) {\n        const timeString = `${hourString}:${minute}`;\n        this.hours.push(timeString);\n      }\n    }\n  }\n  convertHoursToMinutes(hours) {\n    return hours * 60;\n  }\n  removeValuesGreaterThanHoursLeft(hours) {\n    const hoursLeft = hours; // Hours left in Classroom for the selected teacher\n    const minutesLeft = this.convertHoursToMinutes(hoursLeft);\n    const maxDuration = this.convertHoursToMinutes(3); // Maximum duration of 3 hours\n    console.log(hoursLeft);\n    this.availableDuration = this.duration.filter(item => item.value <= minutesLeft && item.value <= maxDuration);\n  }\n  /**\n   * The function initializes a schedule by setting time slots from the minimum hour and minute to the\n   * end hour and minute in 15-minute intervals.\n   */\n  initSchedule() {\n    const startMinute = 0;\n    const endMinute = 45;\n    let minHour = 23;\n    let minMinute = 59;\n    for (const day in this.schedule) {\n      const dayObj = this.schedule[day];\n      for (const hour in dayObj) {\n        if (dayObj[hour]) {\n          const [hourStr, minuteStr] = hour.split(':');\n          const hourNum = parseInt(hourStr);\n          const minuteNum = parseInt(minuteStr);\n          if (hourNum < minHour || hourNum === minHour && minuteNum < minMinute) {\n            minHour = hourNum;\n            minMinute = minuteNum;\n          }\n        }\n      }\n    }\n    const startHour = minHour;\n    const endHour = 23;\n    for (let i = startHour; i <= endHour; i++) {\n      const hour = i.toString().padStart(2, '0');\n      for (let j = 0; j < 4; j++) {\n        let minute = j * 15 + startMinute;\n        if (minute >= 60) {\n          minute = 45;\n        }\n        if (i === endHour && minute > endMinute || i === startHour && minute < minMinute) {\n          continue;\n        }\n        const timeString = `${hour}:${minute.toString().padStart(2, '0')}`;\n        for (const day in this.schedule) {\n          this.schedule[day][timeString] = true;\n        }\n      }\n    }\n  }\n  /**\n   * Toggles the availability of a time slot on a specific day.\n   * @param day - The date object representing the day to toggle.\n   * @param hour - The hour string representing the time slot to toggle.\n   */\n  toggleSchedule(day, hour) {\n    const dateString = day.toDateString();\n    const isAvailable = !this.schedule[dateString]?.[hour];\n    this.schedule[dateString] = this.schedule[dateString] || {};\n    this.schedule[dateString][hour] = isAvailable;\n  }\n  /**\n   * Moves the calendar view back one week and recalculates the dates of the new week.\n   */\n  goToPrevWeek() {\n    this.startDate.setDate(this.startDate.getDate() - this.daysRange);\n    this.startDate = new Date(this.startDate);\n    console.log('prev', this.startDate);\n    this.calculateWeekDays(this.startDate);\n    this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n      console.log(res);\n    });\n  }\n  /**\n   * Moves the calendar view forward one week and recalculates the dates of the new week.\n   */\n  goToNextWeek() {\n    this.startDate.setDate(this.startDate.getDate() + this.daysRange);\n    this.startDate = new Date(this.startDate);\n    this.calculateWeekDays(this.startDate);\n    this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {});\n  }\n  /**\n   * Gets the availability status of a specific time slot on a specific day.\n   * @param dayString - The string representation of the date to check.\n   * @param hour - The hour string representing the time slot to check.\n   * @returns A boolean indicating whether the time slot is available or not.\n   */\n  isTimeSlotAvailableOnDay(dayString, hour) {\n    const day = moment(dayString, \"ddd MMM DD YYYY HH:mm:ss ZZ\");\n    const dayFormatted = day.format(\"DD/MM/YYYY\");\n    const dayAbbr = day.format(\"ddd\").toLowerCase();\n    if (this.businessHours) {\n      const dayObj = this.businessHours.find(a => a.date === dayFormatted);\n      // This code checks if the role of the user is equal to the 'TEACHER' role.\n      if (this.user.role === UserRole.TEACHER) {\n        return true;\n      }\n      if (dayObj && dayObj.availability.length > 0) {\n        const availableHours = dayObj.availability;\n        let hourIndex = this.hours.indexOf(hour);\n        const durationInMinutes = this.selectedDuration.value;\n        for (let i = 0; i < availableHours.length; i++) {\n          const range = availableHours[i];\n          let start = Math.max(this.hours.indexOf(range.from), hourIndex);\n          const end = range.to === \"00:00\" ? this.hours.length - 1 : this.hours.indexOf(range.to);\n          if (start >= 0 && end >= 0 && start <= hourIndex && hourIndex <= end) {\n            const availableDuration = (end - start + (range.to === \"00:00\" ? 2 : 1)) * 14;\n            if (availableDuration >= durationInMinutes) {\n              return true;\n            }\n          }\n        }\n      }\n    }\n    return false;\n  }\n  /**\n  * Creates a new lesson based on user input and sends it to backend.\n  *\n  * @returns void\n  */\n  onLessonRequested() {\n    // Check if a timeslot is selected\n    if (this.selectedTimeSlot === '') {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Please select a timeslot'\n      });\n      return;\n    }\n    // Get the output date\n    const outputDate = this.getOutputDate();\n    // Get the current time\n    const currentTime = moment().startOf('hour');\n    // Get the UTC date\n    const utcDate = moment(this.selectedDay);\n    // Format the date\n    const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss ZZ');\n    if (!this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Please select a correct timeslot'\n      });\n      return;\n    }\n    // Prevent button spam\n    if (this.buttonLoading) {\n      return;\n    }\n    // Set the button loading state to true\n    this.buttonLoading = true;\n    // Set the default lesson status\n    let status = LessonStatus.REQUESTED;\n    // Check if the user is a teacher and has extra classroom data\n    if (this.user.role === UserRole.TEACHER) {\n      if (this.hasExtraClassroomData()) {\n        if (this.extraClassroomData.status.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\n          status = LessonStatus.ARRANGED_TRIAL;\n        } else {\n          status = LessonStatus.ARRANGED;\n        }\n      } else {\n        const matchingClassroom = this.availableClassrooms.find(classroom => {\n          return classroom.id === this.studentSelectedClassroom?.id;\n        });\n        if (matchingClassroom.type.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\n          status = LessonStatus.ARRANGED_TRIAL;\n        } else {\n          status = LessonStatus.ARRANGED;\n        }\n      }\n    }\n    let classroomId;\n    // Check if the user is a student\n    if (this.user.role === UserRole.STUDENT) {\n      const matchingClassroom = this.availableClassrooms.find(classroom => {\n        return classroom.teacher.id === this.selectedTeachersLabel.teacherId && classroom.language === this.selectedTeachersLabel.language;\n      });\n      [classroomId] = matchingClassroom ? [matchingClassroom.id] : [null];\n    } else {\n      [classroomId] = [this.studentSelectedClassroom?.id];\n    }\n    // Create the lesson object\n    const lesson = {\n      classroomId,\n      status,\n      startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot),\n      duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\n      isRecccuring: false\n    };\n    // Check if it is a rescheduling\n    if (this.isRescheduling) {\n      let lessonUpdate = {\n        id: this.extraData.lessonId,\n        classroomId,\n        status: this.isPending ? LessonStatus.ARRANGED : this.extraData.status,\n        startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot),\n        duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\n        isRecccuring: false,\n        answered: this.isPending ? \"requested\" : \"accepted\"\n      };\n      console.log(lessonUpdate);\n      this.subs.sink = this.lessonService.update(lessonUpdate).subscribe(res => {\n        console.log(res);\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Lesson Rescheduled for ' + moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm')\n        });\n        this.location.back();\n        // this.router.navigate(['/dashboard/calendar']);\n      }, error => {\n        this.buttonLoading = false;\n      });\n    } else {\n      const extraTimeString = moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm');\n      this.subs.sink = this.lessonService.create(lesson).subscribe(res => {\n        console.log(res);\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: status === LessonStatus.ARRANGED ? 'Lesson Created for ' + extraTimeString : 'Lesson Requested for ' + extraTimeString\n        });\n        this.location.back();\n      }, error => {\n        this.buttonLoading = false;\n      });\n    }\n  }\n  prepareInitialData() {\n    this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).pipe(switchMap(classrooms => {\n      // sort classrooms first\n      classrooms = this.classroomService.sortClassroomsByFirstName(classrooms);\n      return this.addTeacherLabelToArrays(classrooms);\n      //TODO: remove when database is updated - START\n      // const modifiedArray = Object.values(this.teachersLabels.reduce((acc, { teacherId, hoursLeft, ...rest }) => {\n      //   if (acc[teacherId]) {\n      //     acc[teacherId].hoursLeft += hoursLeft;\n      //   } else {\n      //     acc[teacherId] = { teacherId, hoursLeft, ...rest };\n      //   }\n      //   return acc;\n      // }, {}));\n      // this.teachersLabels = modifiedArray;\n      //TODO: remove when database is updated - END\n      // const uniqueTeachers = [...new Set(this.availableTeachers.map(t => t.id))].map(id => this.availableTeachers.find(t => t.id === id));\n      // this.availableTeachers = uniqueTeachers;\n      if (this.user.role === UserRole.TEACHER) {\n        this.studentSelectedClassroom = this.availableClassrooms[0];\n        this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId;\n      } else {\n        this.selectedTeacher = this.availableTeachers[0];\n        this.selectedTeachersLabel = this.teachersLabels[0];\n        const classroomId = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n        this.removeValuesGreaterThanHoursLeft(classroomId.hoursLeft);\n        this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n      }\n      return this.getTeacherAvailability(this.availableTeachers[0]);\n    })).subscribe(res => {\n      console.log(res);\n      // this.filteredLessonEvents = this.lessonEvents;\n    });\n  }\n  /**\n   * Formats a date object to a string in the format 'dddd DD/MM'.\n   * @param {Date} date - The date object to format.\n   * @returns {string} The formatted date string.\n   */\n  formatDateWithDay(date) {\n    const dateString = date.toDateString();\n    const momentDate = moment(dateString);\n    return momentDate.format('dddd DD/MM');\n  }\n  onAgendaDayClicked(event) {\n    console.log(event);\n    this.calculateWeekDays(event);\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n      this.filteredLessonEvents = this.lessonEvents;\n      console.log('filteredLessonEvents', this.filteredLessonEvents);\n    }));\n  }\n  /**\n   * The function adds teacher labels to arrays of classrooms and performs various initialization tasks.\n   * @param {Classroom[]} classrooms - An array of Classroom objects.\n   * @returns the result of the `getTeacherAvailability` function with the first element of the\n   * `availableTeachers` array as the argument.\n   */\n  addTeacherLabelToArrays(classrooms) {\n    if (this.hasExtraData()) {\n      const classroom = this.getClassroomById(classrooms);\n      if (!classroom) {\n        console.log(`No teacher label found for classroom`);\n        return undefined;\n      }\n      this.addTeacherLabel(classroom);\n      this.addAvailableClassroom(classroom);\n      console.log(classroom);\n      this.addAvailableTeacher(classroom.teacher);\n    } else {\n      const events = this.getAvailableClassroomEvents(classrooms);\n      console.log(`No teacher label found for classroom`, events);\n      this.lessonEvents.push(...events);\n    }\n    this.setInitialValuesForUserRole();\n    this.initializeStartDateAndTime();\n    this.initializeWeekDays();\n    if (this.authService.isTeacher) {\n      if (!this.studentSelectedClassroom) {\n        this.noClassroomsFound = true;\n        return of(null);\n      }\n    }\n    this.initializeCalendarAgendaUserId();\n    return this.getTeacherAvailability(this.availableTeachers[0]);\n  }\n  /**\n   * The function `getClassroomById` takes an array of `Classroom` objects and returns the `Classroom`\n   * object with a matching `id` property, or `undefined` if no match is found.\n   * @param {Classroom[]} classrooms - An array of Classroom objects.\n   * @returns a Classroom object or undefined.\n   */\n  getClassroomById(classrooms) {\n    const classroomId = this.extraData.classroomId;\n    return classrooms.find(classroom => classroom.id === classroomId);\n  }\n  /**\n   * The addTeacherLabel function adds a teacher label to the teachersLabels array for a given classroom.\n   * @param {Classroom} classroom - The classroom parameter is an object that represents a classroom.\n   */\n  addTeacherLabel(classroom) {\n    const icon = this.getTeacherLabelIcon(classroom);\n    const teachersLabel = this.createTeachersLabel(classroom, icon);\n    this.teachersLabels.push(teachersLabel);\n  }\n  /**\n   * The function adds a classroom to the list of available classrooms.\n   * @param {Classroom} classroom - The parameter \"classroom\" is of type \"Classroom\".\n   */\n  addAvailableClassroom(classroom) {\n    this.availableClassrooms.push(classroom);\n  }\n  /**\n   * The function adds a teacher to the list of available teachers.\n   * @param {User} teacher - The \"teacher\" parameter is of type \"User\".\n   */\n  addAvailableTeacher(teacher) {\n    this.availableTeachers.push(teacher);\n  }\n  /**\n   * The function sets initial values for user role based on certain conditions and handles classroom\n   * selection.\n   */\n  setInitialValuesForUserRole() {\n    if (this.isRescheduling) {\n      this.selectedDuration = this.duration.find(duration => duration.value === this.extraData.duration * 60);\n    } else {\n      this.selectedDuration = this.duration[3];\n    }\n    if (this.hasExtraClassroomData()) {\n      this.studentSelectedClassroom = this.availableClassrooms.find(item => item.id === this.extraClassroomData.classroomId);\n      if (this.studentSelectedClassroom) {\n        this.removeValuesGreaterThanHoursLeft(this.classroomService.getAccumulatedHoursLeft(this.studentSelectedClassroom.packages));\n      }\n      if (!this.studentSelectedClassroom) {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Not enough hours for previously selected classroom. Please select another.'\n        });\n        this.handleSelectedClassroomForUserRole();\n      } else {\n        this.selectedTeacher = this.studentSelectedClassroom.teacher;\n        this.selectedTeachersLabel = this.teachersLabels.find(item => item.classroom === this.studentSelectedClassroom.id);\n      }\n    } else {\n      if (this.user.role === UserRole.TEACHER) {\n        this.studentSelectedClassroom = this.availableClassrooms[0];\n        this.selectedTeacher = this.user;\n      } else {\n        this.handleSelectedClassroomForUserRole();\n      }\n    }\n  }\n  /**\n   * The function handles the selection of a classroom based on the user's role and performs additional\n   * operations based on the selected classroom.\n   */\n  handleSelectedClassroomForUserRole() {\n    if (this.user.role === UserRole.TEACHER) {\n      this.studentSelectedClassroom = this.availableClassrooms[0];\n      this.selectedTeacher = this.user;\n    } else {\n      console.log(this.availableTeachers);\n      this.selectedTeacher = this.availableTeachers[0];\n      this.selectedTeachersLabel = this.teachersLabels[0];\n      const classroom = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n      if (classroom) {\n        if (!this.isRescheduling) {\n          console.log(classroom);\n          this.handleClassroomTypeAvailableDuration(classroom);\n          this.availableDuration = this.duration.filter(duration => {\n            return this.generalService.convertMinutesToHours(duration.value) <= this.classroomService.getAccumulatedHoursLeft(classroom.packages);\n          });\n          // this.removeValuesGreaterThanHoursLeft(classroom.packages[0].hoursLeft);\n        }\n      }\n    }\n  }\n  /**\n   * The function `handleClassroomTypeAvailableDuration` sets the available duration options for a\n   * classroom based on its type and removes any options that exceed the remaining hours.\n   * @param {Classroom} classroom - The \"classroom\" parameter is an object of type Classroom.\n   */\n  handleClassroomTypeAvailableDuration(classroom) {\n    if (this.classroomService.isTrialClassroom(classroom)) {\n      const trialDuration = this.duration[3];\n      this.availableDuration = [trialDuration];\n      this.selectedDuration = trialDuration;\n    } else {\n      this.availableDuration = [...this.duration];\n      this.selectedDuration = this.duration[3];\n      const accumulatedHoursLeft = this.classroomService.getAccumulatedHoursLeft(classroom.packages);\n      this.removeValuesGreaterThanHoursLeft(accumulatedHoursLeft);\n    }\n  }\n  onCurrentMonthChanged(monthNumber) {\n    const outputDate = this.getOutputDate();\n    console.log(outputDate);\n    // this.selectedDay = undefined;\n    // const currentTime = moment().startOf('hour');\n    // const time = currentTime.format('HH:00');\n    // const utcDate = moment(outputDate);\n    // // Fri Jul 14 2023 00:00:00 GMT+0300 (Eastern European Summer Time)\n    // const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)');\n    // if (this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\n    //   this.onTimeSlotSelected(outputDate, time);\n    // } else {\n    //   this.selectedTimeSlot = '';\n    //   this.radioChecked = false;\n    // }\n  }\n  initializeStartDateAndTime() {\n    const outputDate = this.getOutputDate();\n    this.startDate = new Date(outputDate);\n    const inputDate = this.extraData.startingDate;\n    if (!this.isRescheduling) {\n      // (TODO) init an hour to help the ui \n      // const currentTime = moment().add(2, 'hour').startOf('hour');\n      // const time = currentTime.format('HH:00');\n      // this.onTimeSlotSelected(outputDate, time);\n    } else {\n      const selectedTime = moment(inputDate).format('HH:mm');\n      this.onTimeSlotSelected(outputDate, selectedTime);\n    }\n  }\n  /**\n   * The function initializes the week days by calculating them based on the output date.\n   */\n  initializeWeekDays() {\n    const outputDate = this.getOutputDate();\n    this.calculateWeekDays(outputDate);\n  }\n  /**\n   * The function takes in an array of classrooms and returns an array of lesson events for the available\n   * classrooms.\n   * @param {Classroom[]} classrooms - An array of Classroom objects.\n   * @returns an array of Lesson objects.\n   */\n  getAvailableClassroomEvents(classrooms) {\n    return this.classroomService.filterAvailableClassrooms(classrooms).flatMap(classroom => {\n      const teachersLabel = this.createTeachersLabelForEvent(classroom);\n      this.teachersLabels.push(teachersLabel);\n      this.availableClassrooms.push(classroom);\n      this.availableTeachers.push(classroom.teacher);\n      return classroom.lessons.map(lesson => this.createLessonEvent(lesson, classroom));\n    });\n  }\n  /**\n   * The function creates a label for teachers based on the given classroom information.\n   * @param {Classroom} classroom - The \"classroom\" parameter is an object of type \"Classroom\".\n   * @returns an object with the following properties:\n   */\n  createTeachersLabelForEvent(classroom) {\n    let icon = 'student';\n    if (classroom.type && classroom.type.includes('Trial')) {\n      icon = 'blue-plane';\n    }\n    if (classroom.type && classroom.type.includes('Gift')) {\n      icon = 'gift-box';\n    }\n    return {\n      language: classroom.language,\n      teacherName: classroom.teacher && classroom.teacher.firstName,\n      icon,\n      hoursLeft: classroom.packages && classroom.packages.length > 0 ? this.classroomService.getAccumulatedHoursLeft(classroom.packages) : classroom.hoursLeft,\n      teacherId: classroom.teacher && classroom.teacher.id,\n      classroom: classroom.id,\n      classroomType: classroom.type\n    };\n  }\n  /**\n   * The function creates a lesson event object with specific properties based on the given lesson and\n   * classroom data.\n   * @param {any} lesson - The lesson parameter is an object that represents a lesson. It contains\n   * properties such as startingDate (the date and time the lesson starts), duration (the duration of the\n   * lesson in hours), and id (a unique identifier for the lesson).\n   * @param {Classroom} classroom - The `classroom` parameter is an object that represents a classroom.\n   * It likely contains information such as the classroom name, teacher, students, and other relevant\n   * details.\n   * @returns an object with the following properties:\n   * - id: the id of the lesson\n   * - title: the short full name of the teacher\n   * - start: the starting date and time of the lesson (with the time set to 12:00:00)\n   * - end: the ending date and time of the lesson (calculated based on the starting date and the\n   * duration of the lesson\n   */\n  createLessonEvent(lesson, classroom) {\n    const start = new Date(lesson.startingDate);\n    const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\n    const {\n      teacher\n    } = classroom;\n    const title = this.generalService.getShortFullName(teacher);\n    return {\n      id: lesson.id,\n      title,\n      start: lesson.startingDate.slice(0, 11) + '12:00:00',\n      end,\n      allDay: false,\n      backgroundColor: '#378006',\n      borderColor: '#378006',\n      textColor: '#ffffff',\n      extendedProps: {\n        type: 'lesson',\n        lesson,\n        classRoom: classroom\n      },\n      editable: false\n    };\n  }\n  /**\n   * The function initializes the calendar agenda user ID based on the user's role and selected classroom\n   * or teacher.\n   */\n  initializeCalendarAgendaUserId() {\n    if (this.authService.isTeacher) {\n      console.log(this.studentSelectedClassroom);\n      if (!this.studentSelectedClassroom) {\n        this.showToastMessage('info', '', 'No available classrooms for booking');\n      }\n      this.calendarAgendaUserId = this.getTeacherAspUserId();\n    } else if (this.authService.isStudent && this.selectedTeacher) {\n      this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n    } else {\n      this.showToastMessage('info', '', 'No available classrooms for booking or enrolled classrooms have no available hours.');\n      this.goBack();\n    }\n  }\n  showToastMessage(severity, summary, detail) {\n    this.toastService.setShowToastmessage({\n      severity: severity,\n      summary: summary,\n      detail: detail\n    });\n  }\n  getTeacherAspUserId() {\n    return this.studentSelectedClassroom.teacher?.aspUserId;\n  }\n  goBack() {\n    this.location.back();\n  }\n  getTeacherLabelIcon(teacherLabel) {\n    let icon = 'student';\n    if (teacherLabel.status.includes('Trial')) {\n      icon = 'blue-plane';\n    } else if (teacherLabel.status.includes('Gift')) {\n      icon = 'gift-box';\n    }\n    return icon;\n  }\n  createTeachersLabel(teacherLabel, icon) {\n    const {\n      language,\n      teacher,\n      id,\n      hoursLeft\n    } = teacherLabel;\n    console.log(teacherLabel);\n    return {\n      language,\n      teacherName: teacher.firstName,\n      icon,\n      hoursLeft,\n      teacherId: teacher.id,\n      classroom: id\n    };\n  }\n  getOutputDate() {\n    const outputFormat = \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)\";\n    const inputDate = this.extraData.startingDate;\n    const outputDateStr = moment.utc(inputDate).startOf('day').local().format(outputFormat);\n    const outputDate = new Date(Date.parse(outputDateStr));\n    outputDate.setHours(0, 0, 0, 0);\n    return outputDate;\n  }\n  hasExtraData() {\n    return typeof this.extraData !== 'undefined' && Object.keys(this.extraData).length > 0;\n  }\n  hasExtraClassroomData() {\n    return !!this.extraClassroomData && Object.keys(this.extraClassroomData).length > 0;\n  }\n  /**\n   * Check if the selected date and time is in the past.\n   * @param {Date} selectedDateTime - The selected date and time.\n   * @returns {boolean} True if the selected date and time is in the past, false otherwise.\n   */\n  isDateTimeInThePast(selectedDateTime) {\n    const currentDateTime = moment();\n    const selectedDateTimeMoment = moment(selectedDateTime);\n    return selectedDateTimeMoment.isBefore(currentDateTime);\n  }\n  static #_ = this.ɵfac = function BookingSystemComponent_Factory(t) {\n    return new (t || BookingSystemComponent)(i0.ɵɵdirectiveInject(i1.ClassroomService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.CalendarService), i0.ɵɵdirectiveInject(i6.LayoutService), i0.ɵɵdirectiveInject(i7.LessonService), i0.ɵɵdirectiveInject(i8.ToastService), i0.ɵɵdirectiveInject(i9.ActivatedRoute), i0.ɵɵdirectiveInject(i9.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i10.Location));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BookingSystemComponent,\n    selectors: [[\"app-booking-system\"]],\n    viewQuery: function BookingSystemComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bookingWrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollPanel = _t.first);\n      }\n    },\n    hostBindings: function BookingSystemComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function BookingSystemComponent_resize_HostBindingHandler() {\n          return ctx.isTabletScreenSize();\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      studentSelectedClassroom: \"studentSelectedClassroom\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 60,\n    vars: 23,\n    consts: [[\"bookingWrapper\", \"\"], [\"scrollPanel\", \"\"], [\"header\", \"Booking System\", \"headerBackgroundImage\", \"/assets/images/dashboard/calendar-banner.svg\", \"blockClass\", \"border-radius-bottom-10\", \"containerClass\", \"bg-white p-2 border-round-lg\", 2, \"height\", \"inherit\", 3, \"headerClass\", \"backButtonLabel\", \"headerTextClass\"], [1, \"surface-section\", \"lg:px-1\", \"lg:h-full\"], [1, \"grid\", \"h-full\", \"flex-column\", \"lg:flex-row\", \"relative\", \"overflow-y-scroll\"], [1, \"calendar-nav\", \"hidden\", \"lg:block\"], [\"type\", \"button\", 1, \"prev\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right-calendar-grad.svg\"], [\"type\", \"button\", 1, \"next\", 3, \"click\"], [1, \"col\", \"lg:col-fixed\", \"lg:pr-0\", \"lg:m-3\", \"lg:m-0\", \"left-col\"], [1, \"flex\", \"align-items-center\", \"justify-content-start\", \"flex-wrap\", \"gap-3\", \"mb-3\"], [\"for\", \"Teacher\", 1, \"col-fix\", \"flex\", \"gap-1\", \"font-sm\", 2, \"width\", \"70px\"], [\"src\", \"/assets/icons/student.svg\"], [4, \"ngIf\"], [\"for\", \"Duration\", 1, \"col-fix\", \"flex\", \"gap-1\", \"font-sm\", 2, \"width\", \"70px\"], [\"src\", \"/assets/icons/hourglass.svg\"], [\"optionLabel\", \"viewValue\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", 3, \"ngStyle\"], [1, \"mt-5\"], [\"title\", \"\", 3, \"dayClicked\", \"currentMonthChanged\", \"currentSelectedDay\", \"reverseGradient\", \"showAvailability\", \"userId\"], [1, \"grid\", \"font-2xs\", \"justify-content-between\"], [1, \"col-fixed\", \"col-circle\", \"pr-0\", \"pb-0\", \"gap-1\"], [1, \"high-bg\", \"circle\"], [1, \"low-bg\", \"circle\"], [1, \"col-fixed\", \"col-circle\", \"pb-0\", \"gap-1\"], [1, \"no-bg\", \"circle\"], [1, \"col\", \"h-full\", \"p-2\", \"md:pr-4\", \"right-col\"], [1, \"calendar-nav\", \"relative\", \"block\", \"lg:hidden\"], [\"type\", \"button\", 1, \"prev\", \"z-5\", 3, \"click\"], [\"type\", \"button\", 1, \"next\", \"z-5\", 3, \"click\"], [1, \"appointment-popup\", \"h-full\", \"pl-1\"], [\"id\", \"calendar\", 1, \"appointment-calendar\", \"h-full\"], [1, \"calendar-wrapper\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"mb-1\"], [1, \"calendar-week\", \"relative\", \"flex\", \"justify-content-center\"], [1, \"p-0\", \"mx-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"calendar-hours\"], [\"styleClass\", \"custombar1 h-30rem lg:h-full\"], [1, \"calendar-inside\", \"pb-2\"], [\"styleClass\", \"max-w-10rem\", \"panelStyleClass\", \"bordered-panel\", \"placeholder\", \"Select Classroom\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [\"class\", \"flex align-items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"height\", \"12\", 3, \"src\", 4, \"ngIf\"], [1, \"font-xs\", 3, \"innerHTML\"], [\"height\", \"12\", 3, \"src\"], [1, \"flex\", \"gap-1\", \"font-xs\"], [\"src\", \"/assets/images/dashboard/calendar/check-circled.svg\", 2, \"width\", \"12px\"], [\"optionLabel\", \"teacherName\", \"styleClass\", \"max-w-10rem\", \"placeholder\", \"Select Teacher\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [1, \"capitalize\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"font-xs\"], [2, \"width\", \"18px\", 3, \"src\"], [1, \"border-round-xl\", \"block-gradient-reverse\", \"mt-3\", \"mb-0\"], [1, \"card-header\", \"text-center\"], [1, \"card-title\", \"font-xs\", \"py-2\"], [1, \"card-body\", \"px-2\"], [1, \"flex\", \"justify-content-center\", \"font-sm\", \"gap-1\", \"text-primary\"], [1, \"\"], [1, \"pi\", \"pi-calendar\", \"font-sm\", \"mr-1\"], [1, \"pi\", \"pi-clock\", \"font-sm\", \"mr-1\"], [1, \"pi\", \"pi-hourglass\", \"font-sm\", \"mr-1\"], [3, \"hidden\"], [1, \"grid\", \"justify-content-evenly\", \"font-2xs\", \"mt-2\"], [1, \"col-fixed\", \"flex\", \"align-items-center\"], [\"src\", \"/assets/icons/add-to-calendar.svg\", \"width\", \"15\", 1, \"mr-1\"], [1, \"col-fixed\", \"align-items-center\", \"pr-0\"], [1, \"flex\", \"justify-content-center\", \"mt-2\"], [\"styleClass\", \" px-2 \", \"icon\", \"pi pi-calendar\", \"iconPos\", \"left\", \"pButton\", \"\", \"type\", \"button\", 1, \"font-base\", \"mt-2\", \"border-round-lg\", \"request\", 3, \"click\", \"loading\", \"label\"], [1, \"flex\", \"w-full\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"text-center\", \"w-full\", \"pt-5\"], [\"src\", \"/assets/icons/fast-time-clock-ui-icon.png\", 1, \"max-w-14rem\"], [1, \"text-primary\", \"font-lg\"], [\"type\", \"button\", \"class\", \"font-lg gray-border-button border-round-3xl gap-2 px-3 py-2\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"font-lg\", \"gray-border-button\", \"border-round-3xl\", \"gap-2\", \"px-3\", \"py-2\", 3, \"click\"], [1, \"relative\", \"font-sm\", 3, \"ngClass\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", 3, \"ngClass\"], [1, \"hour-label\", 3, \"click\", \"for\"], [\"type\", \"radio\", \"name\", \"timeslot\", 3, \"disabled\", \"id\", \"value\", \"checked\"]],\n    template: function BookingSystemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", null, 0)(2, \"app-block-viewer\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_6_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goToPrevWeek());\n        });\n        i0.ɵɵelement(7, \"img\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_8_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goToNextWeek());\n        });\n        i0.ɵɵelement(9, \"img\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"label\", 11);\n        i0.ɵɵelement(13, \"img\", 12);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(15, BookingSystemComponent_ng_container_15_Template, 4, 2, \"ng-container\", 13)(16, BookingSystemComponent_ng_container_16_Template, 4, 2, \"ng-container\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 10)(18, \"label\", 14);\n        i0.ɵɵelement(19, \"img\", 15);\n        i0.ɵɵtext(20, \" Duration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"p-dropdown\", 16);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_Template_p_dropdown_ngModelChange_21_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.selectedDuration, $event) || (ctx.selectedDuration = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵlistener(\"onChange\", function BookingSystemComponent_Template_p_dropdown_onChange_21_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDurationSelected($event));\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 17)(23, \"div\")(24, \"div\", 18)(25, \"app-calendar-agenda\", 19);\n        i0.ɵɵlistener(\"dayClicked\", function BookingSystemComponent_Template_app_calendar_agenda_dayClicked_25_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onAgendaDayClicked($event));\n        })(\"currentMonthChanged\", function BookingSystemComponent_Template_app_calendar_agenda_currentMonthChanged_25_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onCurrentMonthChanged($event));\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21);\n        i0.ɵɵelement(28, \"div\", 22);\n        i0.ɵɵelementStart(29, \"div\");\n        i0.ɵɵtext(30, \"High Availability\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"div\", 21);\n        i0.ɵɵelement(32, \"div\", 23);\n        i0.ɵɵelementStart(33, \"div\");\n        i0.ɵɵtext(34, \"Low Availability\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"div\", 24);\n        i0.ɵɵelement(36, \"div\", 25);\n        i0.ɵɵelementStart(37, \"div\");\n        i0.ɵɵtext(38, \"No Availability\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(39, BookingSystemComponent_div_39_Template, 27, 6, \"div\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 26)(41, \"div\", 27)(42, \"button\", 28);\n        i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_42_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goToPrevWeek());\n        });\n        i0.ɵɵelement(43, \"img\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"button\", 29);\n        i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_44_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goToNextWeek());\n        });\n        i0.ɵɵelement(45, \"img\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(46, \"div\", 30)(47, \"div\", 31)(48, \"div\", 32)(49, \"div\", 33);\n        i0.ɵɵtext(50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"div\", 34)(52, \"ul\", 35);\n        i0.ɵɵtemplate(53, BookingSystemComponent_li_53_Template, 3, 5, \"li\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"div\", 37)(55, \"p-scrollPanel\", 38, 1);\n        i0.ɵɵtemplate(57, BookingSystemComponent_ng_container_57_Template, 10, 1, \"ng-container\", 13);\n        i0.ɵɵelementStart(58, \"ul\", 39);\n        i0.ɵɵtemplate(59, BookingSystemComponent_li_59_Template, 2, 1, \"li\", 36);\n        i0.ɵɵelementEnd()()()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-2\")(\"backButtonLabel\", \"Back\")(\"headerTextClass\", \"font-xl font-semibold justify-content-center\");\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.role == ctx.UserRoles.TEACHER ? \"Classroom\" : \"Teacher\", \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.user.role == ctx.UserRoles.TEACHER);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.user.role == ctx.UserRoles.STUDENT);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"options\", ctx.availableDuration);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedDuration);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c2, !ctx.isTabletScreenSize() ? ctx.height - 150 + \"px\" : \"auto\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"currentSelectedDay\", ctx.selectedDay)(\"reverseGradient\", true)(\"showAvailability\", true)(\"userId\", ctx.calendarAgendaUserId);\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngIf\", ctx.radioChecked);\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate1(\" \", ctx.findMonthFromDateRange(), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.weekDays);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c3));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.noClassroomsFound);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.weekDays);\n      }\n    },\n    dependencies: [i11.NgControlStatus, i11.NgModel, i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgStyle, i12.BlockViewerComponent, i13.CalendarAgendaComponent, i14.ButtonDirective, i15.PrimeTemplate, i16.ScrollPanel, i17.Dropdown, i10.DatePipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.hour-label[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 0.12vw + 0.67rem, 0.88rem);\\n}\\n.hour-label.checked[_ngcontent-%COMP%] {\\n  color: #2FB9D3;\\n  font-weight: bolder;\\n}\\n\\n[_nghost-%COMP%]     .block-section {\\n  height: inherit;\\n}\\n[_nghost-%COMP%]     .block-content {\\n  height: calc(100% - 52px);\\n}\\n[_nghost-%COMP%]     .block-content .bg-white {\\n  height: 100%;\\n  padding-bottom: 0 !important;\\n}\\n[_nghost-%COMP%]     .p-scrollpanel-content {\\n  padding: 0;\\n}\\n[_nghost-%COMP%]     .request.p-button {\\n  background: linear-gradient(#657aef 0%, #1a266c 100%);\\n  width: auto;\\n  justify-content: center;\\n}\\n[_nghost-%COMP%]     .request.p-button .p-button-label {\\n  flex: unset;\\n}\\n[_nghost-%COMP%]     p-dropdown .p-dropdown {\\n  width: 100%;\\n  background-image: linear-gradient(to bottom, #ffffff, rgba(180, 180, 250, 0.2));\\n  border: none;\\n}\\n[_nghost-%COMP%]     p-dropdown .p-dropdown .p-inputtext {\\n  padding: 0.3rem 1rem;\\n  font-size: 0.85rem;\\n  font-family: \\\"Proxima Nova Regular\\\";\\n}\\n[_nghost-%COMP%]     p-dropdown .p-dropdown-panel {\\n  box-shadow: none;\\n}\\n[_nghost-%COMP%]     p-dropdown .p-dropdown-panel .p-dropdown-items {\\n  background-image: linear-gradient(to bottom, rgba(180, 180, 250, 0.2), rgba(132, 146, 247, 0.2), rgba(56, 115, 244, 0.2));\\n  color: #2E3D90;\\n}\\n[_nghost-%COMP%]     p-dropdown .p-dropdown:not(.p-disabled).p-focus {\\n  border: 0;\\n  box-shadow: none;\\n}\\n[_nghost-%COMP%]     .reversed-gradient .cal-header {\\n  width: 100%;\\n}\\n[_nghost-%COMP%]     .table-container {\\n  overflow-x: hidden !important;\\n}\\n[_nghost-%COMP%]     .card {\\n  border: 0;\\n}\\n[_nghost-%COMP%]     .card .p-datepicker {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n[_nghost-%COMP%]     .card .p-datepicker table th {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n  border: 0;\\n}\\n[_nghost-%COMP%]     .card .p-datepicker table td {\\n  height: 40px;\\n}\\n[_nghost-%COMP%]     .card .p-datepicker table thead th {\\n  color: #CACBD9;\\n}\\n.left-col[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n\\n.right-col[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n\\n.no-availability[_ngcontent-%COMP%] {\\n  color: #CACBD9;\\n}\\n\\n.availability[_ngcontent-%COMP%] {\\n  color: #2E3D90;\\n}\\n\\n.block-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  border-top-left-radius: 16px;\\n  border-top-right-radius: 16px;\\n  min-height: 60px;\\n  box-shadow: 0px 3px 8px rgba(179, 179, 179, 0.96);\\n  border-radius: 16px;\\n}\\n.block-header[_ngcontent-%COMP%]:before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background-image: url(\\\"/assets/images/lessons/calendar-availability-gradient-bg.png\\\");\\n  background-size: inherit;\\n  background-repeat: no-repeat;\\n  background-position: center;\\n  width: 100%;\\n  height: 100%;\\n  left: 0;\\n  top: 0;\\n  z-index: 1;\\n  border-radius: 16px;\\n}\\n\\n.appointment-popup[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  z-index: 1;\\n  overflow: hidden;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #f3f3f3;\\n  align-items: center;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%]    > span[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #2d3152;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%]    > span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  opacity: 0.5;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: inherit;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%] {\\n  border-bottom: 0 !important;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  padding: 0 3px 0px 0;\\n  list-style: none;\\n  display: flex;\\n  width: 100%;\\n  margin: 0;\\n  background-image: linear-gradient(359deg, rgba(0, 44, 207, 0.1882352941), transparent);\\n  border-radius: 14px;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.5rem 0;\\n  text-align: center;\\n  color: #575757;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%] {\\n  padding: 0 0 0 1rem;\\n  overflow: auto;\\n  height: 100%;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 1rem;\\n  padding-top: 6px;\\n  padding-bottom: 6px;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li.no-availability[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  cursor: initial;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li.availability[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:hover {\\n  color: #2fb9d3;\\n  cursor: pointer;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%] {\\n  appearance: none;\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n  -ms-appearance: none;\\n  -o-appearance: none;\\n  width: 20px;\\n  height: 20px;\\n  margin-right: 5px;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:before {\\n  background-color: white;\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 50%;\\n  transition: background-color 0.2s ease;\\n  cursor: pointer;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:checked:before {\\n  background-color: #2FB9D3;\\n  border: 1px solid #2FB9D3;\\n  position: absolute;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:checked:after {\\n  content: \\\"\\\";\\n  display: block;\\n  position: relative;\\n  width: 6px;\\n  height: 10px;\\n  border-bottom: 2px solid #fff;\\n  border-right: 2px solid #fff;\\n  transform: rotate(45deg);\\n  top: 2px;\\n  left: 4px;\\n}\\n.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-top: 1px solid #f3f3f3;\\n  margin: 0 -2rem;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .appointment-popup[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n    max-width: 100%;\\n    top: 0;\\n    border-radius: 0;\\n    overflow: auto;\\n    height: calc(100% - 6.4rem);\\n    min-height: auto;\\n    max-height: 100%;\\n  }\\n  .appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  .appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:before {\\n    padding: 0 1rem;\\n    font-size: 1.2rem;\\n  }\\n  .appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%]    > span[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%] {\\n    padding: 0;\\n    margin: 0;\\n  }\\n  .appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > ul[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%] {\\n    padding: 1rem 0 0.75rem 0;\\n    font-size: 1.2rem;\\n  }\\n  .appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-buttons[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n}\\n.round[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.round[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border: 1px solid #ccc;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  height: 14px;\\n  left: 0;\\n  position: absolute;\\n  top: 3px;\\n  width: 14px;\\n}\\n\\n.round[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:after {\\n  border: 2px solid #fff;\\n  border-top: none;\\n  border-right: none;\\n  content: \\\"\\\";\\n  height: 6px;\\n  left: 1px;\\n  opacity: 0;\\n  position: absolute;\\n  top: 2px;\\n  transform: rotate(-45deg);\\n  width: 10px;\\n}\\n\\n.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n}\\n\\n.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + label[_ngcontent-%COMP%] {\\n  background-color: #2FB9D3;\\n  border-color: #2FB9D3;\\n}\\n\\n.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + label[_ngcontent-%COMP%]:after {\\n  opacity: 1;\\n}\\n\\n.high-bg[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(to bottom, #64cfdd, #48c4e0, #2cb8e2, #18abe4, #219de3, #1591e2, #1e85e0, #3177dc, #2a69da, #2b5ad6, #334ad0, #3f37c9);\\n}\\n\\n.low-bg[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(to bottom, #9baaff, #7c8aec, #5e6ad9, #3f4bc4, #152caf);\\n}\\n\\n.no-bg[_ngcontent-%COMP%] {\\n  background: #DFE8FC;\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  height: 0.75rem;\\n  width: 0.75rem;\\n  border-radius: 50%;\\n}\\n\\n.col-circle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.55rem !important;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 10px !important;\\n  height: 10px !important;\\n  display: none;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: transparent !important;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent !important;\\n}\\n\\n.calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  padding: 0;\\n  font-size: 0;\\n  cursor: pointer;\\n  transform: translate(-50%, 1%);\\n}\\n.calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:before {\\n  font-size: 2rem;\\n  padding: 0 1.5rem;\\n  display: block;\\n}\\n.calendar-nav[_ngcontent-%COMP%]   button.prev[_ngcontent-%COMP%] {\\n  transform: rotate(-180deg);\\n  position: absolute;\\n  top: 2rem;\\n  left: calc(1% - 5px);\\n}\\n@media only screen and (min-width: 992px) {\\n  .calendar-nav[_ngcontent-%COMP%]   button.prev[_ngcontent-%COMP%] {\\n    top: 2.5rem;\\n    left: calc(36% - 8px);\\n  }\\n}\\n.calendar-nav[_ngcontent-%COMP%]   button.next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 2rem;\\n  right: calc(1% - 15px);\\n}\\n@media only screen and (min-width: 992px) {\\n  .calendar-nav[_ngcontent-%COMP%]   button.next[_ngcontent-%COMP%] {\\n    top: 2.5rem;\\n    right: -8px;\\n  }\\n}\\n\\n.left-col[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .left-col[_ngcontent-%COMP%] {\\n    width: 32%;\\n  }\\n}\\n\\n.right-col[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .right-col[_ngcontent-%COMP%] {\\n    width: 63%;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .bordered-panel .p-dropdown-items-wrapper {\\n  border-radius: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["of", "map", "startWith", "switchMap", "take", "ClassroomType", "SubSink", "moment", "LessonStatus", "UserRole", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "classroomService", "getClassroomTypeSmallIconPath", "studentSelectedClassroom", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtemplate", "BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template", "ɵɵelementEnd", "ɵɵadvance", "authService", "<PERSON><PERSON><PERSON>er", "getStudentNamesWithHTML", "ɵɵsanitizeHtml", "BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template", "classroom_r4", "BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template", "ɵɵtext", "ɵɵtextInterpolate1", "generalService", "convertHoursToMinutesWithSuffix", "getAccumulatedHoursLeft", "packages", "ɵɵelementContainerStart", "ɵɵtwoWayListener", "BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "onClassroomChange", "BookingSystemComponent_ng_container_15_ng_template_2_Template", "BookingSystemComponent_ng_container_15_ng_template_3_Template", "availableClassrooms", "ɵɵtwoWayProperty", "ɵɵtextInterpolate2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "language", "<PERSON><PERSON><PERSON>", "BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template", "ɵɵpropertyInterpolate1", "teacher_r6", "icon", "hoursLeft", "BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener", "_r5", "onTeacher<PERSON><PERSON>e", "BookingSystemComponent_ng_container_16_ng_template_2_Template", "BookingSystemComponent_ng_container_16_ng_template_3_Template", "teachers<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "ɵɵpipeBind2", "selected<PERSON>ay", "selectedTimeSlot", "BookingSystemComponent_div_39_span_9_Template", "BookingSystemComponent_div_39_span_12_Template", "BookingSystemComponent_div_39_Template_button_click_26_listener", "_r7", "onLessonRequested", "selectedDuration", "viewValue", "buttonLoading", "isRescheduling", "day_r8", "getDate", "BookingSystemComponent_ng_container_57_a_9_Template_a_click_0_listener", "_r9", "navigateToBuyPackage", "BookingSystemComponent_ng_container_57_a_9_Template", "isStudent", "BookingSystemComponent_li_59_ul_1_ng_container_1_Template_label_click_3_listener", "_r10", "hour_r11", "$implicit", "day_r12", "onTimeSlotSelected", "ɵɵpureFunction2", "_c4", "isTimeSlotAvailableOnDay", "ɵɵpureFunction1", "_c5", "ɵɵclassProp", "isSelectedDayAndTime", "radioChecked", "ɵɵpropertyInterpolate2", "ɵɵpropertyInterpolate", "BookingSystemComponent_li_59_ul_1_ng_container_1_Template", "BookingSystemComponent_li_59_ul_1_Template", "availableHours", "BookingSystemComponent", "constructor", "userService", "calendarService", "layoutService", "lessonService", "toastService", "route", "router", "cdRef", "location", "bookingWrapper", "scrollPanel", "startDate", "Date", "weekDays", "hours", "schedule", "subs", "classrooms", "classroomsWithRecurringLessons", "hasClassrooms", "availableTeachers", "lessonEvents", "<PERSON><PERSON><PERSON><PERSON>", "filteredLessonEvents", "user", "selected<PERSON>our", "duration", "availableDuration", "cdr", "containerHeight", "UserRoles", "calendarAgendaUserId", "extraData", "extraClassroomData", "isPending", "mobileView", "<PERSON><PERSON><PERSON><PERSON>", "height", "noClassroomsFound", "ngOnInit", "queryParams", "snapshot", "lessonString", "classroomString", "hasRescheduleString", "answeredString", "isMobileScreenSize", "lesson", "JSON", "parse", "console", "log", "id", "status", "classroomId", "startingDate", "isRecccuring", "classroom", "type", "value", "calculateWeekDays", "calculateHours", "initSchedule", "getLoggedInUser", "prepareInitialData", "screenWidth", "window", "innerWidth", "isTabletScreenSize", "ngOnChanges", "ngAfterViewInit", "sink", "deviceKind", "pipe", "res", "is576", "is992", "sideMenuHeight", "subscribe", "setDaysRange", "nativeElement", "style", "detectChanges", "scrollToMiddle", "ngAfterContentInit", "ngOnDestroy", "unsubscribe", "days", "getDaysRange", "setTimeout", "scrollContent", "containerViewChild", "getElementsByClassName", "middleScrollPosition", "scrollHeight", "scrollTop", "onDurationSelected", "event", "day", "hour", "toDate", "displayAvailableTimeSlots", "selectedDateTime", "formattedDateTime", "format", "role", "TEACHER", "isDateTimeInThePast", "setShowToastmessage", "severity", "summary", "detail", "resetSelectedTimeSlot", "d", "hasExtraData", "getMonth", "getTeacherAvailability", "teacher", "isNullishObject", "data", "getDatesRange", "userId", "aspUserId", "getAvailabilityFromRange", "requestData", "retrieveCalendarAvailability", "businessHours", "calendarAvailability", "filterLessonsByTeacherId", "lessons", "teacherId", "filter", "find", "item", "handleClassroomTypeAvailableDuration", "add", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName", "firstName", "today", "firstDay", "getFullYear", "firstDayCopy", "Array", "from", "length", "_", "i", "setDate", "dates", "dateString", "fromDate", "findMonthFromDateRange", "fromMonth", "toMonth", "fromYear", "toYear", "weekDayAbbreviations", "startHour", "endHour", "minuteIncrements", "hourString", "toString", "padStart", "minute", "timeString", "isAvailable", "push", "quarterHours", "convertHoursToMinutes", "removeValuesGreaterThanHoursLeft", "minutesLeft", "maxDuration", "startMinute", "endMinute", "minHour", "minMinute", "<PERSON><PERSON><PERSON><PERSON>", "hourStr", "minuteStr", "split", "hourNum", "parseInt", "minute<PERSON>um", "j", "toggleSchedule", "toDateString", "goToPrevWeek", "goToNextWeek", "dayString", "dayFormatted", "dayAbbr", "toLowerCase", "a", "date", "availability", "hourIndex", "indexOf", "durationInMinutes", "range", "start", "Math", "max", "end", "to", "outputDate", "getOutputDate", "currentTime", "startOf", "utcDate", "formattedDate", "REQUESTED", "hasExtraClassroomData", "includes", "TRIAL", "ARRANGED_TRIAL", "ARRANGED", "matchingClassroom", "STUDENT", "formatSelectedDateTimeForLesson", "convertMinutesToHours", "lessonUpdate", "lessonId", "answered", "update", "parseZone", "back", "error", "extraTimeString", "create", "getLMSUserClassrooms", "sortClassroomsByFirstName", "addTeacherLabelToArrays", "formatDateWithDay", "momentDate", "onAgendaDayClicked", "getClassroomById", "undefined", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addAvailableClassroom", "addAvailableTeacher", "events", "getAvailableClassroomEvents", "setInitialValuesForUserRole", "initializeStartDateAndTime", "initializeWeekDays", "initializeCalendarAgendaUserId", "getTeacherLabelIcon", "teachers<PERSON>abel", "createTeachersLabel", "handleSelectedClassroomForUserRole", "isTrialClassroom", "trialDuration", "accumulatedHoursLeft", "onCurrentMonthChanged", "monthNumber", "inputDate", "selectedTime", "filterAvailableClassrooms", "flatMap", "createTeachersLabelForEvent", "createLessonEvent", "classroomType", "getTime", "title", "getShortFullName", "slice", "allDay", "backgroundColor", "borderColor", "textColor", "extendedProps", "classRoom", "editable", "showToastMessage", "getTeacherAspUserId", "goBack", "<PERSON><PERSON><PERSON><PERSON>", "outputFormat", "outputDateStr", "utc", "local", "setHours", "Object", "keys", "currentDateTime", "selectedDateTimeMoment", "isBefore", "ɵɵdirectiveInject", "i1", "ClassroomService", "i2", "GeneralService", "i3", "AuthService", "i4", "UserService", "i5", "CalendarService", "i6", "LayoutService", "i7", "LessonService", "i8", "ToastService", "i9", "ActivatedRoute", "Router", "ChangeDetectorRef", "i10", "Location", "_2", "selectors", "viewQuery", "BookingSystemComponent_Query", "rf", "ctx", "BookingSystemComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "BookingSystemComponent_Template_button_click_6_listener", "_r1", "BookingSystemComponent_Template_button_click_8_listener", "BookingSystemComponent_ng_container_15_Template", "BookingSystemComponent_ng_container_16_Template", "BookingSystemComponent_Template_p_dropdown_ngModelChange_21_listener", "BookingSystemComponent_Template_p_dropdown_onChange_21_listener", "BookingSystemComponent_Template_app_calendar_agenda_dayClicked_25_listener", "BookingSystemComponent_Template_app_calendar_agenda_currentMonthChanged_25_listener", "BookingSystemComponent_div_39_Template", "BookingSystemComponent_Template_button_click_42_listener", "BookingSystemComponent_Template_button_click_44_listener", "BookingSystemComponent_li_53_Template", "BookingSystemComponent_ng_container_57_Template", "BookingSystemComponent_li_59_Template", "_c2", "ɵɵstyleMap", "ɵɵpureFunction0", "_c3"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\booking-system\\booking-system.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\booking-system\\booking-system.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { Observable, of } from 'rxjs';\r\nimport { map, startWith, switchMap, take } from 'rxjs/operators';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport * as moment from 'moment';\r\nimport { Lesson, LessonPostRequest, LessonStatus, LessonUpdateRequest } from 'src/app/core/models/lesson.model';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { ScrollPanel } from 'primeng/scrollpanel';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { RetrieveCalendarAvailabilityRequestData } from 'src/app/core/models/calendar.model';\r\n\r\ninterface HourlySchedule {\r\n  [hour: string]: boolean;\r\n}\r\n\r\ninterface DailySchedule {\r\n  [day: string]: HourlySchedule;\r\n}\r\n\r\ninterface AvailabilitySchedule {\r\n  [key: string]: any;\r\n  mon: HourRange[];\r\n  tue: HourRange[];\r\n  wed: HourRange[];\r\n  thu: HourRange[];\r\n  fri: HourRange[];\r\n  sat: HourRange[];\r\n  sun: HourRange[];\r\n  mondayActive: boolean;\r\n  tuesdayActive: boolean;\r\n  wednesdayActive: boolean;\r\n  thursdayActive: boolean;\r\n  fridayActive: boolean;\r\n  saturdayActive: boolean;\r\n  sundayActive: boolean;\r\n}\r\n\r\ninterface HourRange {\r\n  from: string;\r\n  to: string;\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'app-booking-system',\r\n  templateUrl: './booking-system.component.html',\r\n  styleUrls: ['./booking-system.component.scss'],\r\n\r\n})\r\nexport class BookingSystemComponent implements OnInit {\r\n  @ViewChild('bookingWrapper', { static: true }) bookingWrapper: ElementRef = {} as ElementRef;\r\n  @ViewChild('scrollPanel', { static: true }) scrollPanel: ScrollPanel = {} as ScrollPanel;\r\n  @Input() studentSelectedClassroom: Classroom = {} as Classroom;\r\n  startDate: Date = new Date();\r\n  weekDays: Date[] = [];\r\n  hours: string[] = [];\r\n  schedule: { [day: string]: { [hour: string]: boolean } } = {};\r\n  availableHours: string[] = [];\r\n  private subs = new SubSink();\r\n  public classrooms: Classroom[] = [];\r\n  public classroomsWithRecurringLessons: Classroom[] = [];\r\n  public hasClassrooms = true;\r\n  availableTeachers: any[] = [];\r\n  lessonEvents: any[] = [];\r\n  selectedTeacher: any = null;\r\n  filteredLessonEvents: any[] = [];\r\n  businessHours: any;\r\n  dayOffEvents: any;\r\n  user: User = {} as User;\r\n  teachersLabels: any[] = [];\r\n  selectedTeachersLabel: any = null;\r\n  selectedHour: any = null;\r\n  duration: any[] = [];\r\n  availableDuration: any[] = [];\r\n  selectedDuration: any = null;\r\n  cdr: ChangeDetectorRef = {} as ChangeDetectorRef;\r\n  containerHeight = '';\r\n  selectedTimeSlot!: string;\r\n  selectedDay!: Date | undefined;\r\n  availableClassrooms: any[] = [];\r\n  UserRoles = UserRole;\r\n  radioChecked = false;\r\n  calendarAgendaUserId = '';\r\n  extraData: any = {};\r\n  extraClassroomData: any = {};\r\n  isRescheduling = false;\r\n  isPending = false;\r\n  mobileView = false;\r\n  daysRange = 7;\r\n  height = 0;\r\n  noClassroomsFound = false;\r\n  buttonLoading = false;\r\n  constructor(\r\n    public classroomService: ClassroomService,\r\n    public generalService: GeneralService,\r\n    private authService: AuthService,\r\n    private userService: UserService,\r\n    private calendarService: CalendarService,\r\n    private layoutService: LayoutService,\r\n    private lessonService: LessonService,\r\n    public toastService: ToastService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdRef: ChangeDetectorRef,\r\n    private location: Location,\r\n  ) {\r\n    this.startDate = new Date();\r\n  }\r\n  ngOnInit() {\r\n    const queryParams = this.route.snapshot.queryParams;\r\n    const lessonString = queryParams['lesson'];\r\n    const classroomString = queryParams['classroom'];\r\n    const hasRescheduleString = queryParams['reschedule'];\r\n    const answeredString = queryParams['answered'];\r\n    this.mobileView = this.isMobileScreenSize();\r\n\r\n    if (hasRescheduleString) {\r\n      this.isRescheduling = true;\r\n    }\r\n    if (answeredString) {\r\n      this.isPending = true;\r\n    }\r\n    if (lessonString) {\r\n      const lesson = JSON.parse(lessonString);\r\n      console.log(lesson);\r\n      this.extraData = {\r\n        \"lessonId\": lesson.id,\r\n        \"lessonStatus\": lesson.status,\r\n        \"classroomId\": lesson.classroomId,\r\n        \"status\": lesson.status,\r\n        \"startingDate\": lesson.startingDate,\r\n        \"duration\": lesson.duration,\r\n        \"isRecccuring\": lesson.isRecccuring,\r\n      }\r\n    }\r\n  \r\n    if (classroomString) {\r\n      const classroom = JSON.parse(classroomString);\r\n      console.log(classroom);\r\n      this.extraClassroomData = {\r\n        \"classroomId\": classroom.id,\r\n        \"status\": classroom.status,\r\n        \"classroomType\": classroom.type,\r\n      }\r\n    }\r\n\r\n    this.duration = [\r\n      { value: 15, viewValue: '15m'},\r\n      { value: 30, viewValue: '30m' },\r\n      { value: 45, viewValue: '45m' },\r\n      { value: 60, viewValue: '1h' },\r\n      { value: 75, viewValue: '1h 15m' },\r\n      { value: 90, viewValue: '1h 30m' },\r\n      { value: 105, viewValue: '1h 45m' },\r\n      { value: 120, viewValue: '2h' },\r\n      { value: 135, viewValue: '2h 15m' },\r\n      { value: 150, viewValue: '2h 30m' },\r\n      { value: 165, viewValue: '2h 45m' },\r\n      { value: 180, viewValue: '3h' }\r\n    ]\r\n    this.availableDuration = [...this.duration];\r\n    this.selectedDuration = this.duration[3];\r\n    this.calculateWeekDays(new Date());\r\n    this.calculateHours();\r\n    this.initSchedule();\r\n    \r\n    this.user = this.authService.getLoggedInUser();\r\n\r\n    this.prepareInitialData();\r\n  }\r\n  @HostListener('window:resize')\r\n  isMobileScreenSize() {\r\n    const screenWidth = window.innerWidth;\r\n    return screenWidth < 768;\r\n  }\r\n\r\n  @HostListener('window:resize')\r\n  isTabletScreenSize() {\r\n    const screenWidth = window.innerWidth;\r\n    return screenWidth < 1024; // Change the width here to the desired tablet width\r\n  }\r\n\r\n  ngOnChanges() {\r\n    \r\n    this.mobileView = this.isMobileScreenSize();\r\n    console.log(this.mobileView);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n        \r\n    this.subs.sink = this.generalService.deviceKind.pipe(\r\n      take(2),\r\n      switchMap(res => {\r\n        console.log(res);\r\n        if (res.is576 || res.is992) {\r\n          return of(0);\r\n        } else {\r\n          return this.layoutService.sideMenuHeight;\r\n        }\r\n      }),\r\n      startWith(0),\r\n    ).subscribe(height => {\r\n      if (height !== 0) {\r\n        this.setDaysRange(7);\r\n      this.height = height;\r\n        this.bookingWrapper.nativeElement.style.height = (height + 30 + 'px');\r\n      } else {\r\n        this.setDaysRange(3);\r\n        this.bookingWrapper.nativeElement.style.height = ('100%');\r\n      }\r\n      this.cdRef.detectChanges(); // Manually trigger change detection\r\n    });\r\n    // this.subs.sink = this.layoutService.sideMenuHeight.subscribe((res) => {\r\n    //   this.bookingWrapper.nativeElement.style.height = (res + 'px');\r\n    // });\r\n    this.scrollToMiddle();\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  setDaysRange(days: number) {\r\n    this.daysRange = days;\r\n  }\r\n\r\n  getDaysRange() {\r\n    return this.daysRange;\r\n  }\r\n\r\n  scrollToMiddle() {\r\n    setTimeout(() => {\r\n      const scrollContent = this.scrollPanel.containerViewChild!.nativeElement.getElementsByClassName('p-scrollpanel-content')[0];\r\n      const middleScrollPosition = scrollContent.scrollHeight / 3;\r\n      scrollContent.scrollTop = middleScrollPosition;\r\n    }, 1000);\r\n  }\r\n\r\n  onDurationSelected(event: any) {\r\n    this.selectedTimeSlot = '';\r\n    this.radioChecked = false;\r\n    console.log(this.selectedDay);\r\n    // Add your own logic here to handle the selected duration\r\n  }\r\n\r\n  onTimeSlotSelected(day: Date, hour: string) {\r\n\r\n    this.selectedDay = moment(day, \"ddd MMM DD YYYY\").toDate();\r\n    this.selectedTimeSlot = hour;\r\n    this.displayAvailableTimeSlots();\r\n    const selectedDateTime = moment(day, \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ\");\r\n    const formattedDateTime = selectedDateTime.format(\"ddd MMM DD YYYY \" + hour);\r\n\r\n    // TODO : implement uncheck all radios functionality\r\n    // if (this.radioChecked && this.timeSlotRadio.checked) {\r\n    //   if (this.timeSlotRadio) {\r\n    //     this.timeSlotRadio.checked = false;\r\n    //   }\r\n    //   this.resetSelectedTimeSlot();\r\n    //   return;\r\n    // }\r\n\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      this.radioChecked = true;\r\n    } else {\r\n      if (!this.isDateTimeInThePast(new Date(formattedDateTime))) {\r\n        this.radioChecked = true;\r\n      } else {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'info',\r\n          summary: '',\r\n          detail: 'Date is in the past. Please choose another date.'\r\n        });\r\n        this.radioChecked = false;\r\n      }\r\n    }\r\n  }\r\n\r\n  resetSelectedTimeSlot() {\r\n    this.selectedDay = new Date();\r\n    this.selectedTimeSlot = '';\r\n    this.displayAvailableTimeSlots();\r\n    this.radioChecked = false;\r\n  }\r\n\r\n  isSelectedDayAndTime(day: Date, hour: string): boolean {\r\n    const d = new Date(day);\r\n    if (this.hasExtraData()) {\r\n      return (d.getDate() + '' + d.getMonth()) === (this.selectedDay!.getDate() + '' + this.selectedDay!.getMonth()) && hour === this.selectedTimeSlot;\r\n    } else {\r\n      return day === this.selectedDay && hour === this.selectedTimeSlot;\r\n    }\r\n  }\r\n\r\n/**\r\n * The function `getTeacherAvailability` retrieves the calendar availability of a teacher based on\r\n * their ASP user ID.\r\n * @param {any} teacher - The `teacher` parameter is an object that represents a teacher. It contains\r\n * information about the teacher, such as their ID (`aspUserId`).\r\n * @returns an Observable of type RetrieveCalendarAvailabilityRequestData or null.\r\n */\r\n  private getTeacherAvailability(teacher: any): Observable<RetrieveCalendarAvailabilityRequestData | null> {\r\n    if (!teacher || this.generalService.isNullishObject(teacher)) {\r\n      return of(null);\r\n    }\r\n    const data = {\r\n      ...this.getDatesRange(),\r\n      userId: teacher.aspUserId\r\n    }\r\n    return this.getAvailabilityFromRange(data);\r\n\r\n  }\r\n\r\n  private getAvailabilityFromRange(requestData: RetrieveCalendarAvailabilityRequestData) {\r\n    console.log(requestData);\r\n    return this.calendarService.retrieveCalendarAvailability(requestData).pipe(\r\n      map((res: any) => {\r\n        // const daysOff = res.daysOff;\r\n\r\n        // // Set business hours and select constraint\r\n        this.businessHours = (res.calendarAvailability);\r\n\r\n        this.availableHours = this.displayAvailableTimeSlots();\r\n        \r\n        // // Create events for each day off\r\n        // this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\r\n\r\n        return null;\r\n      })\r\n    );\r\n  }\r\n\r\n  filterLessonsByTeacherId(lessons: Lesson[], teacherId: number): Lesson[] {\r\n    return lessons.filter((lesson: any) => lesson.teacher.id === teacherId);\r\n  }\r\n\r\n  onTeacherChange(event: { teacherId: number }) {\r\n// Reset selected time slot and radio checked status\r\n    this.selectedTimeSlot = '';\r\n    this.radioChecked = false;\r\n\r\n  // Find the selected teacher based on the teacherId\r\n    this.selectedTeacher = this.availableTeachers.find((item) => item.id === event.teacherId);\r\n\r\n  // Find the classroom based on the selected teacher's classroom label\r\n    const classroomId = this.availableClassrooms.find((item) => item.id === this.selectedTeachersLabel.classroom);\r\n    console.log(classroomId);\r\n    \r\n// Handle the available duration for the classroom type\r\n    this.handleClassroomTypeAvailableDuration(classroomId);\r\n    \r\n  // Get the teacher's availability and update the filtered lesson events\r\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe((res) => {\r\n      this.filteredLessonEvents = this.lessonEvents;\r\n      console.log('filteredLessonEvents', this.filteredLessonEvents);\r\n    }));\r\n\r\n  // Set the calendar agenda user ID to the selected teacher's ASP user ID\r\n    this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\r\n  }\r\n\r\n  /**\r\n   * Handles the change event when a classroom is selected.\r\n   * It sets the selected classroom and updates the calendar agenda user ID if a teacher is associated with the classroom.\r\n   * It also handles the available duration based on the classroom type.\r\n   * \r\n   * @param event - The selected classroom object.\r\n   */\r\n  onClassroomChange(event: Classroom) {\r\n    console.log(event);\r\n    this.studentSelectedClassroom = event;\r\n    console.log(event);\r\n    if (this.studentSelectedClassroom.teacher) {\r\n      this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId!;\r\n    }\r\n    this.handleClassroomTypeAvailableDuration(event);\r\n  }\r\n\r\n  getTeacherLabel(teacher: User): string {\r\n    return ` ${teacher.lastName} ${teacher.firstName}`;\r\n  }\r\n  /**\r\n   * Updates the selected classroom when a user chooses a classroom from the UI.\r\n   * @param event - The change event triggered by selecting a classroom.\r\n   */\r\n\r\n  /**\r\n   * Calculates the dates of the current week, starting from the given start date.\r\n   * @param startDate - The start date for the current week.\r\n   */\r\n  calculateWeekDays(startDate: Date) {\r\n    const today = moment(startDate).toDate();\r\n    const firstDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\r\n    const firstDayCopy = new Date(firstDay);\r\n    this.weekDays = Array.from({ length: this.daysRange }, (_, i) => {\r\n      const d = new Date(firstDayCopy);\r\n      d.setDate(d.getDate() + i);\r\n      return d;\r\n    });\r\n\r\n  }\r\n\r\n  /**\r\n   * Returns the date range for the current week.\r\n   * @returns {Object} An object with \"from\" and \"to\" properties representing the start and end dates of the week.\r\n   */\r\n  getDatesRange() {\r\n    // Convert date strings to Date objects\r\n    const dates = this.weekDays.map(dateString => new Date(dateString));\r\n\r\n    // Get the range of dates from and to\r\n    const fromDate = dates[0];\r\n    const toDate = dates[dates.length - 1];\r\n\r\n    // console.log(fromDate);\r\n    // Log the range of dates\r\n    return {\r\n      fromDate: moment((fromDate), 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z',\r\n      toDate: moment((toDate), 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z'\r\n    }\r\n    \r\n  }\r\n\r\n  findMonthFromDateRange() {\r\n    const data = this.getDatesRange();\r\n  \r\n    const fromDate = moment(data.fromDate);\r\n    const toDate = moment(data.toDate);\r\n  \r\n    const fromMonth = fromDate.format('MMMM');\r\n    const toMonth = toDate.format('MMMM');\r\n    const fromYear = fromDate.format('YYYY');\r\n    const toYear = toDate.format('YYYY');\r\n  \r\n    if (fromMonth === toMonth) {\r\n      return `${fromMonth} ${fromYear}`;\r\n    } else {\r\n      return `${fromMonth} - ${toMonth} ${toYear}`;\r\n    }\r\n  }\r\n\r\n  displayAvailableTimeSlots() {\r\n    const weekDayAbbreviations = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\r\n    const availableHours: string[] = [];\r\n    const startHour = 0;\r\n    const endHour = 23;\r\n    const minuteIncrements = ['00', '15', '30', '45'];\r\n\r\n    for (let hour = startHour; hour <= endHour; hour++) {\r\n      const hourString = hour.toString().padStart(2, '0');\r\n      for (const minute of minuteIncrements) {\r\n        const timeString = `${hourString}:${minute}`;\r\n        let isAvailable = true;\r\n\r\n        // Check availability for each day\r\n        // for (const day of this.weekDays) {\r\n        //   const dayAbbr = weekDayAbbreviations[day.getDay()];\r\n        //   if (this.isTimeSlotAvailableOnDay(day.toDateString(), timeString)) {\r\n        //     isAvailable = true;\r\n        //   }\r\n        // }\r\n\r\n        if (isAvailable) {\r\n          availableHours.push(timeString);\r\n        }\r\n      }\r\n    }\r\n\r\n    return availableHours;\r\n  }\r\n\r\n/**\r\n * Calculates the available hours for scheduling.\r\n */\r\ncalculateHours() {\r\n  const startHour = 0;\r\n  const endHour = 23;\r\n  const quarterHours = ['00', '15', '30', '45'];\r\n  this.hours = [];\r\n\r\n  for (let hour = startHour; hour <= endHour; hour++) {\r\n    const hourString = hour.toString().padStart(2, '0');\r\n\r\n    for (const minute of quarterHours) {\r\n      const timeString = `${hourString}:${minute}`;\r\n      this.hours.push(timeString);\r\n    }\r\n  }\r\n\r\n  for (let hour = 0; hour < startHour; hour++) {\r\n    const hourString = hour.toString().padStart(2, '0');\r\n\r\n    for (const minute of quarterHours) {\r\n      const timeString = `${hourString}:${minute}`;\r\n      this.hours.push(timeString);\r\n    }\r\n  }\r\n}\r\n\r\nconvertHoursToMinutes(hours: number): number {\r\n  return hours * 60;\r\n}\r\nremoveValuesGreaterThanHoursLeft(hours: number) {\r\n  const hoursLeft = hours; // Hours left in Classroom for the selected teacher\r\n  const minutesLeft = this.convertHoursToMinutes(hoursLeft); \r\n  const maxDuration = this.convertHoursToMinutes(3); // Maximum duration of 3 hours\r\n\r\n  console.log(hoursLeft);\r\n  this.availableDuration = this.duration.filter((item) => item.value <= minutesLeft && item.value <= maxDuration);\r\n\r\n}\r\n/**\r\n * The function initializes a schedule by setting time slots from the minimum hour and minute to the\r\n * end hour and minute in 15-minute intervals.\r\n */\r\ninitSchedule() \r\n{\r\n  const startMinute = 0;\r\n  const endMinute = 45;\r\n  let minHour = 23;\r\n  let minMinute = 59;\r\n  for (const day in this.schedule) {\r\n    const dayObj = this.schedule[day];\r\n    for (const hour in dayObj) {\r\n      if (dayObj[hour]) {\r\n        const [hourStr, minuteStr] = hour.split(':');\r\n        const hourNum = parseInt(hourStr);\r\n        const minuteNum = parseInt(minuteStr);\r\n        if (hourNum < minHour || (hourNum === minHour && minuteNum < minMinute)) {\r\n          minHour = hourNum;\r\n          minMinute = minuteNum;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  const startHour = minHour;\r\n  const endHour = 23;\r\n  for (let i = startHour; i <= endHour; i++) {\r\n    const hour = i.toString().padStart(2, '0');\r\n    for (let j = 0; j < 4; j++) {\r\n      let minute = j * 15 + startMinute;\r\n      if (minute >= 60) {\r\n        minute = 45;\r\n      }\r\n      if ((i === endHour && minute > endMinute) || (i === startHour && minute < minMinute)) {\r\n        continue;\r\n      }\r\n      const timeString = `${hour}:${minute.toString().padStart(2, '0')}`;\r\n      for (const day in this.schedule) {\r\n        this.schedule[day][timeString] = true;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n  /**\r\n   * Toggles the availability of a time slot on a specific day.\r\n   * @param day - The date object representing the day to toggle.\r\n   * @param hour - The hour string representing the time slot to toggle.\r\n   */\r\n  toggleSchedule(day: Date, hour: string) {\r\n    const dateString = day.toDateString();\r\n    const isAvailable = !this.schedule[dateString]?.[hour];\r\n    this.schedule[dateString] = this.schedule[dateString] || {};\r\n    this.schedule[dateString][hour] = isAvailable;\r\n  }\r\n\r\n  /**\r\n   * Moves the calendar view back one week and recalculates the dates of the new week.\r\n   */\r\n  goToPrevWeek() {\r\n    this.startDate.setDate(this.startDate.getDate() - this.daysRange);\r\n    this.startDate = new Date(this.startDate);\r\n    console.log('prev', this.startDate);\r\n    this.calculateWeekDays(this.startDate);\r\n    this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe((res)=> {\r\n\r\n      console.log(res);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Moves the calendar view forward one week and recalculates the dates of the new week.\r\n   */\r\n  goToNextWeek() {\r\n    this.startDate.setDate(this.startDate.getDate() + this.daysRange);\r\n    this.startDate = new Date(this.startDate);\r\n    this.calculateWeekDays(this.startDate);\r\n    this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe((res)=> {\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the availability status of a specific time slot on a specific day.\r\n   * @param dayString - The string representation of the date to check.\r\n   * @param hour - The hour string representing the time slot to check.\r\n   * @returns A boolean indicating whether the time slot is available or not.\r\n   */\r\n  isTimeSlotAvailableOnDay(dayString: string, hour: string): boolean {\r\n    const day = moment(dayString, \"ddd MMM DD YYYY HH:mm:ss ZZ\");\r\n    const dayFormatted = day.format(\"DD/MM/YYYY\");\r\n    const dayAbbr = day.format(\"ddd\").toLowerCase();\r\n  \r\n    if (this.businessHours) {\r\n\r\n\r\n    const dayObj = this.businessHours.find((a: any) => a.date === dayFormatted);\r\n\r\n    // This code checks if the role of the user is equal to the 'TEACHER' role.\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      return true;\r\n    }\r\n    if (dayObj && dayObj.availability.length > 0) {\r\n      const availableHours: Record<string, any> = dayObj.availability;\r\n      let hourIndex = this.hours.indexOf(hour);\r\n      const durationInMinutes = this.selectedDuration.value;\r\n      for (let i = 0; i < availableHours.length; i++) {\r\n        const range = availableHours[i];\r\n        let start = Math.max(this.hours.indexOf(range.from), hourIndex);\r\n        const end = range.to === \"00:00\" ? this.hours.length - 1 : this.hours.indexOf(range.to);\r\n  \r\n        if (start >= 0 && end >= 0 && start <= hourIndex && hourIndex <= end) {\r\n          const availableDuration = (end - start + (range.to === \"00:00\" ? 2 : 1)) * 14;\r\n          if (availableDuration >= durationInMinutes) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n    return false;\r\n  }\r\n\r\n /**\r\n * Creates a new lesson based on user input and sends it to backend.\r\n *\r\n * @returns void\r\n */\r\n  onLessonRequested(): void {\r\n// Check if a timeslot is selected\r\n    if (this.selectedTimeSlot === '') {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please select a timeslot'\r\n      });\r\n      return;\r\n    }\r\n\r\n  // Get the output date\r\n    const outputDate = this.getOutputDate();\r\n\r\n  // Get the current time\r\n    const currentTime = moment().startOf('hour');\r\n\r\n// Get the UTC date\r\n    const utcDate = moment(this.selectedDay);\r\n    \r\n  // Format the date\r\n    const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss ZZ');\r\n\r\n    if (!this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please select a correct timeslot'\r\n      });\r\n      return;\r\n        }\r\n    \r\n  // Prevent button spam\r\n    if (this.buttonLoading) {\r\n      return;\r\n    }\r\n\r\n  // Set the button loading state to true\r\n    this.buttonLoading = true;\r\n\r\n  // Set the default lesson status\r\n    let status = LessonStatus.REQUESTED;\r\n\r\n// Check if the user is a teacher and has extra classroom data\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      if (this.hasExtraClassroomData()) {\r\n        if (this.extraClassroomData.status.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\r\n          status = LessonStatus.ARRANGED_TRIAL;\r\n        } else {\r\n          status = LessonStatus.ARRANGED;\r\n        }\r\n      } else {\r\n        const matchingClassroom = this.availableClassrooms.find((classroom: any) => {\r\n          return classroom.id === this.studentSelectedClassroom?.id;\r\n        });\r\n        if (matchingClassroom.type.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\r\n          status = LessonStatus.ARRANGED_TRIAL;\r\n        } else {\r\n          status = LessonStatus.ARRANGED;\r\n        }\r\n      }\r\n    }\r\n\r\n    let classroomId;\r\n\r\n  // Check if the user is a student\r\n    if (this.user.role === UserRole.STUDENT) {\r\n      const matchingClassroom = this.availableClassrooms.find((classroom: Classroom) => {\r\n        return classroom.teacher!.id === this.selectedTeachersLabel.teacherId && classroom.language === this.selectedTeachersLabel.language;\r\n      });\r\n      [classroomId] = matchingClassroom ? [matchingClassroom.id] : [null];\r\n    } else {\r\n      [classroomId] = [this.studentSelectedClassroom?.id];\r\n    }\r\n\r\n  // Create the lesson object\r\n    const lesson: LessonPostRequest = {\r\n      classroomId,\r\n      status,\r\n      startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot),\r\n      duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\r\n      isRecccuring: false,\r\n    };\r\n\r\n  // Check if it is a rescheduling\r\n    if (this.isRescheduling) {\r\n      let lessonUpdate: LessonUpdateRequest = {\r\n        id: this.extraData.lessonId,\r\n        classroomId,\r\n        status: this.isPending ? LessonStatus.ARRANGED : this.extraData.status,\r\n        startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot),\r\n        duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\r\n        isRecccuring: false,\r\n        answered: this.isPending ? \"requested\" : \"accepted\",\r\n      };\r\n      console.log(lessonUpdate);\r\n    this.subs.sink = this.lessonService.update(lessonUpdate).subscribe(res => {\r\n      console.log(res);\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Lesson Rescheduled for ' + moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm')\r\n      });\r\n      \r\n      this.location.back();\r\n      // this.router.navigate(['/dashboard/calendar']);\r\n    }, error => {\r\n      this.buttonLoading = false;\r\n    });\r\n    } else {\r\n      const extraTimeString = moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm');\r\n      this.subs.sink = this.lessonService.create(lesson).subscribe(res => {\r\n        console.log(res);\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: status === LessonStatus.ARRANGED ? 'Lesson Created for ' + extraTimeString : 'Lesson Requested for ' + extraTimeString\r\n        });\r\n        this.location.back();\r\n      }, error => {\r\n        this.buttonLoading = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  private prepareInitialData() {\r\n    this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).pipe(\r\n      switchMap((classrooms: any) => {\r\n\r\n        // sort classrooms first\r\n        classrooms = this.classroomService.sortClassroomsByFirstName(classrooms);\r\n        return this.addTeacherLabelToArrays(classrooms);\r\n\r\n\r\n      //TODO: remove when database is updated - START\r\n      // const modifiedArray = Object.values(this.teachersLabels.reduce((acc, { teacherId, hoursLeft, ...rest }) => {\r\n      //   if (acc[teacherId]) {\r\n      //     acc[teacherId].hoursLeft += hoursLeft;\r\n      //   } else {\r\n      //     acc[teacherId] = { teacherId, hoursLeft, ...rest };\r\n      //   }\r\n      //   return acc;\r\n      // }, {}));\r\n      // this.teachersLabels = modifiedArray;\r\n      //TODO: remove when database is updated - END\r\n\r\n      // const uniqueTeachers = [...new Set(this.availableTeachers.map(t => t.id))].map(id => this.availableTeachers.find(t => t.id === id));\r\n      // this.availableTeachers = uniqueTeachers;\r\n      if (this.user.role === UserRole.TEACHER) {\r\n        this.studentSelectedClassroom = this.availableClassrooms[0];\r\n        this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId!;\r\n      }\r\n      else {\r\n        this.selectedTeacher = this.availableTeachers[0];\r\n        this.selectedTeachersLabel = this.teachersLabels[0];\r\n        const classroomId = this.availableClassrooms .find((item) => item.id === this.selectedTeachersLabel.classroom);\r\n        this.removeValuesGreaterThanHoursLeft(classroomId.hoursLeft);\r\n        this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\r\n      }\r\n      return this.getTeacherAvailability(this.availableTeachers[0]);\r\n      })\r\n    ).subscribe((res: any) => {\r\n      console.log(res);\r\n      // this.filteredLessonEvents = this.lessonEvents;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Formats a date object to a string in the format 'dddd DD/MM'.\r\n   * @param {Date} date - The date object to format.\r\n   * @returns {string} The formatted date string.\r\n   */\r\n  formatDateWithDay(date: Date): string {\r\n    const dateString = date.toDateString();\r\n    const momentDate = moment(dateString);\r\n    return momentDate.format('dddd DD/MM');\r\n  }\r\n\r\n  onAgendaDayClicked(event: any) {\r\n    console.log(event);\r\n    this.calculateWeekDays(event);\r\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe((res) => {\r\n      this.filteredLessonEvents = this.lessonEvents;\r\n      console.log('filteredLessonEvents', this.filteredLessonEvents);\r\n    }));\r\n  }\r\n\r\n/**\r\n * The function adds teacher labels to arrays of classrooms and performs various initialization tasks.\r\n * @param {Classroom[]} classrooms - An array of Classroom objects.\r\n * @returns the result of the `getTeacherAvailability` function with the first element of the\r\n * `availableTeachers` array as the argument.\r\n */\r\n  private addTeacherLabelToArrays(classrooms: Classroom[]): any | undefined {\r\n    if (this.hasExtraData()) {\r\n      const classroom = this.getClassroomById(classrooms);\r\n      if (!classroom) {\r\n        console.log(`No teacher label found for classroom`);\r\n        return undefined;\r\n      }\r\n  \r\n      this.addTeacherLabel(classroom);\r\n      this.addAvailableClassroom(classroom);\r\n      console.log(classroom);\r\n      this.addAvailableTeacher(classroom.teacher!);\r\n  \r\n    } else {\r\n      const events = this.getAvailableClassroomEvents(classrooms);\r\n      console.log(`No teacher label found for classroom`, events);\r\n      this.lessonEvents.push(...events);\r\n    }\r\n    this.setInitialValuesForUserRole();\r\n    this.initializeStartDateAndTime();\r\n    this.initializeWeekDays();\r\n  \r\n    \r\n    if (this.authService.isTeacher) {\r\n      if (!this.studentSelectedClassroom) {\r\n        this.noClassroomsFound = true;\r\n        return of(null);\r\n      }\r\n    }\r\n    this.initializeCalendarAgendaUserId();\r\n  \r\n    return this.getTeacherAvailability(this.availableTeachers[0]);\r\n  }\r\n  \r\n/**\r\n * The function `getClassroomById` takes an array of `Classroom` objects and returns the `Classroom`\r\n * object with a matching `id` property, or `undefined` if no match is found.\r\n * @param {Classroom[]} classrooms - An array of Classroom objects.\r\n * @returns a Classroom object or undefined.\r\n */\r\n  private getClassroomById(classrooms: Classroom[]): Classroom | undefined {\r\n    const classroomId = this.extraData.classroomId;\r\n    return classrooms.find((classroom: Classroom) => classroom.id === classroomId);\r\n  }\r\n  \r\n/**\r\n * The addTeacherLabel function adds a teacher label to the teachersLabels array for a given classroom.\r\n * @param {Classroom} classroom - The classroom parameter is an object that represents a classroom.\r\n */\r\n  private addTeacherLabel(classroom: Classroom): void {\r\n    const icon = this.getTeacherLabelIcon(classroom);\r\n    const teachersLabel = this.createTeachersLabel(classroom, icon);\r\n    this.teachersLabels.push(teachersLabel);\r\n  }\r\n  \r\n/**\r\n * The function adds a classroom to the list of available classrooms.\r\n * @param {Classroom} classroom - The parameter \"classroom\" is of type \"Classroom\".\r\n */\r\n  private addAvailableClassroom(classroom: Classroom): void {\r\n    this.availableClassrooms.push(classroom);\r\n  }\r\n  \r\n/**\r\n * The function adds a teacher to the list of available teachers.\r\n * @param {User} teacher - The \"teacher\" parameter is of type \"User\".\r\n */\r\n  private addAvailableTeacher(teacher: User): void {\r\n    this.availableTeachers.push(teacher);\r\n  }\r\n  \r\n/**\r\n * The function sets initial values for user role based on certain conditions and handles classroom\r\n * selection.\r\n */\r\n  private setInitialValuesForUserRole(): void {\r\n    if (this.isRescheduling) {\r\n      this.selectedDuration = this.duration.find(duration => duration.value === this.extraData.duration * 60);\r\n    } else {\r\n      this.selectedDuration = this.duration[3];\r\n    }\r\n    if (this.hasExtraClassroomData()) {\r\n      this.studentSelectedClassroom = this.availableClassrooms.find((item) => item.id === this.extraClassroomData.classroomId);\r\n\r\n\r\n      if (this.studentSelectedClassroom) {\r\n        this.removeValuesGreaterThanHoursLeft(this.classroomService.getAccumulatedHoursLeft(this.studentSelectedClassroom.packages!));\r\n      }\r\n      if (!this.studentSelectedClassroom) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'info',\r\n          summary: '',\r\n          detail: 'Not enough hours for previously selected classroom. Please select another.'\r\n        });\r\n        \r\n        this.handleSelectedClassroomForUserRole();\r\n      } else {\r\n        this.selectedTeacher = this.studentSelectedClassroom.teacher;\r\n        this.selectedTeachersLabel = this.teachersLabels.find((item) => item.classroom === this.studentSelectedClassroom.id);\r\n      }\r\n    } else {\r\n      if (this.user.role === UserRole.TEACHER) {\r\n        this.studentSelectedClassroom = this.availableClassrooms[0];\r\n\r\n        this.selectedTeacher = this.user;\r\n      } else {\r\n        this.handleSelectedClassroomForUserRole();\r\n      }\r\n    }\r\n  }\r\n\r\n/**\r\n * The function handles the selection of a classroom based on the user's role and performs additional\r\n * operations based on the selected classroom.\r\n */\r\n  handleSelectedClassroomForUserRole() {\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      this.studentSelectedClassroom = this.availableClassrooms[0];\r\n      this.selectedTeacher = this.user;\r\n    } else {\r\n      console.log(this.availableTeachers);\r\n      this.selectedTeacher = this.availableTeachers[0];\r\n      this.selectedTeachersLabel = this.teachersLabels[0];\r\n      const classroom = this.availableClassrooms.find((item) => item.id === this.selectedTeachersLabel.classroom);\r\n\r\n      if (classroom) {\r\n        if(!this.isRescheduling) {\r\n          console.log(classroom);\r\n          this.handleClassroomTypeAvailableDuration(classroom);\r\n          this.availableDuration = this.duration.filter((duration) => {\r\n            return this.generalService.convertMinutesToHours(duration.value) <= this.classroomService.getAccumulatedHoursLeft(classroom.packages!);\r\n          });\r\n          // this.removeValuesGreaterThanHoursLeft(classroom.packages[0].hoursLeft);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n/**\r\n * The function `handleClassroomTypeAvailableDuration` sets the available duration options for a\r\n * classroom based on its type and removes any options that exceed the remaining hours.\r\n * @param {Classroom} classroom - The \"classroom\" parameter is an object of type Classroom.\r\n */\r\n  handleClassroomTypeAvailableDuration(classroom: Classroom) {\r\n    if (this.classroomService.isTrialClassroom(classroom)) {\r\n      const trialDuration = this.duration[3];\r\n      this.availableDuration = [trialDuration];\r\n      this.selectedDuration = trialDuration;\r\n    } else {\r\n      this.availableDuration = [...this.duration];\r\n      this.selectedDuration = this.duration[3];\r\n      const accumulatedHoursLeft = this.classroomService.getAccumulatedHoursLeft(classroom.packages!);\r\n      this.removeValuesGreaterThanHoursLeft(accumulatedHoursLeft);\r\n    }\r\n  }\r\n\r\n  onCurrentMonthChanged(monthNumber: any) {\r\n    const outputDate = this.getOutputDate();\r\n    console.log(outputDate);\r\n    // this.selectedDay = undefined;\r\n    // const currentTime = moment().startOf('hour');\r\n    // const time = currentTime.format('HH:00');\r\n\r\n    // const utcDate = moment(outputDate);\r\n    // // Fri Jul 14 2023 00:00:00 GMT+0300 (Eastern European Summer Time)\r\n    // const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)');\r\n\r\n    // if (this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\r\n    //   this.onTimeSlotSelected(outputDate, time);\r\n    // } else {\r\n    //   this.selectedTimeSlot = '';\r\n    //   this.radioChecked = false;\r\n    // }\r\n  }\r\n  \r\n  private initializeStartDateAndTime(): void {\r\n    const outputDate = this.getOutputDate();\r\n    this.startDate = new Date(outputDate);\r\n    const inputDate = this.extraData.startingDate;\r\n\r\n    if (!this.isRescheduling) {\r\n      // (TODO) init an hour to help the ui \r\n      // const currentTime = moment().add(2, 'hour').startOf('hour');\r\n      // const time = currentTime.format('HH:00');\r\n      // this.onTimeSlotSelected(outputDate, time);\r\n    } else {\r\n      const selectedTime = moment(inputDate).format('HH:mm');\r\n      this.onTimeSlotSelected(outputDate, selectedTime);\r\n    }\r\n\r\n  }\r\n  \r\n/**\r\n * The function initializes the week days by calculating them based on the output date.\r\n */\r\n  private initializeWeekDays(): void {\r\n    const outputDate = this.getOutputDate();\r\n    this.calculateWeekDays(outputDate);\r\n  }\r\n  \r\n/**\r\n * The function takes in an array of classrooms and returns an array of lesson events for the available\r\n * classrooms.\r\n * @param {Classroom[]} classrooms - An array of Classroom objects.\r\n * @returns an array of Lesson objects.\r\n */\r\n  private getAvailableClassroomEvents(classrooms: Classroom[]): Lesson[] {\r\n    return this.classroomService.filterAvailableClassrooms(classrooms)\r\n      .flatMap((classroom: any) => {\r\n        const teachersLabel = this.createTeachersLabelForEvent(classroom);\r\n        this.teachersLabels.push(teachersLabel);\r\n        this.availableClassrooms.push(classroom);\r\n        this.availableTeachers.push(classroom.teacher);\r\n        return classroom.lessons.map((lesson: Lesson) => this.createLessonEvent(lesson, classroom));\r\n      });\r\n  }\r\n  \r\n  /**\r\n   * The function creates a label for teachers based on the given classroom information.\r\n   * @param {Classroom} classroom - The \"classroom\" parameter is an object of type \"Classroom\".\r\n   * @returns an object with the following properties:\r\n   */\r\n  private createTeachersLabelForEvent(classroom: Classroom): any {\r\n    let icon = 'student';\r\n    if (classroom.type && classroom.type.includes('Trial')) {\r\n      icon = 'blue-plane';\r\n    }\r\n    if (classroom.type && classroom.type.includes('Gift')) {\r\n      icon = 'gift-box';\r\n    }\r\n    return {\r\n      language: classroom.language,\r\n      teacherName: classroom.teacher && classroom.teacher.firstName,\r\n      icon,\r\n      hoursLeft: classroom.packages && classroom.packages.length > 0\r\n        ? this.classroomService.getAccumulatedHoursLeft(classroom.packages)\r\n        : classroom.hoursLeft,\r\n      teacherId: classroom.teacher && classroom.teacher.id,\r\n      classroom: classroom.id,\r\n      classroomType: classroom.type,\r\n    };\r\n  }\r\n  \r\n/**\r\n * The function creates a lesson event object with specific properties based on the given lesson and\r\n * classroom data.\r\n * @param {any} lesson - The lesson parameter is an object that represents a lesson. It contains\r\n * properties such as startingDate (the date and time the lesson starts), duration (the duration of the\r\n * lesson in hours), and id (a unique identifier for the lesson).\r\n * @param {Classroom} classroom - The `classroom` parameter is an object that represents a classroom.\r\n * It likely contains information such as the classroom name, teacher, students, and other relevant\r\n * details.\r\n * @returns an object with the following properties:\r\n * - id: the id of the lesson\r\n * - title: the short full name of the teacher\r\n * - start: the starting date and time of the lesson (with the time set to 12:00:00)\r\n * - end: the ending date and time of the lesson (calculated based on the starting date and the\r\n * duration of the lesson\r\n */\r\n  private createLessonEvent(lesson: any, classroom: Classroom): any {\r\n    const start = new Date(lesson.startingDate);\r\n    const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\r\n    const { teacher } = classroom;\r\n    const title = this.generalService.getShortFullName(teacher);\r\n    return {\r\n      id: lesson.id,\r\n      title,\r\n      start: lesson.startingDate.slice(0, 11) + '12:00:00',\r\n      end,\r\n      allDay: false,\r\n      backgroundColor: '#378006',\r\n      borderColor: '#378006',\r\n      textColor: '#ffffff',\r\n      extendedProps: {\r\n        type: 'lesson',\r\n        lesson,\r\n        classRoom: classroom,\r\n      },\r\n      editable: false,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * The function initializes the calendar agenda user ID based on the user's role and selected classroom\r\n   * or teacher.\r\n   */\r\n  private initializeCalendarAgendaUserId(): void {\r\n    if (this.authService.isTeacher) {\r\n      console.log(this.studentSelectedClassroom);\r\n      if (!this.studentSelectedClassroom) {\r\n        this.showToastMessage('info', '', 'No available classrooms for booking');\r\n      }\r\n      this.calendarAgendaUserId = this.getTeacherAspUserId();\r\n    } else if (this.authService.isStudent && this.selectedTeacher) {\r\n      this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\r\n    } else {\r\n      this.showToastMessage('info', '', 'No available classrooms for booking or enrolled classrooms have no available hours.');\r\n      this.goBack();\r\n    }\r\n  }\r\n\r\n  private showToastMessage(severity: string, summary: string, detail: string): void {\r\n    this.toastService.setShowToastmessage({\r\n      severity: severity,\r\n      summary: summary,\r\n      detail: detail\r\n    });\r\n  }\r\n\r\n  private getTeacherAspUserId(): string {\r\n    return this.studentSelectedClassroom.teacher?.aspUserId!;\r\n  }\r\n\r\n  private goBack(): void {\r\n    this.location.back();\r\n  }\r\n  \r\n  private getTeacherLabelIcon(teacherLabel: Classroom): string {\r\n    let icon = 'student';\r\n  \r\n    if (teacherLabel.status.includes('Trial')) {\r\n      icon = 'blue-plane';\r\n    } else if (teacherLabel.status.includes('Gift')) {\r\n      icon = 'gift-box';\r\n    }\r\n\r\n    return icon;\r\n  }\r\n  \r\n  private createTeachersLabel(teacherLabel: Classroom, icon: string): { language: string | undefined, teacherName: string, icon: string, hoursLeft: number, teacherId: string, classroom: string } {\r\n    const { language, teacher, id, hoursLeft } = teacherLabel;\r\n    console.log(teacherLabel);\r\n    return { \r\n      language, \r\n      teacherName: teacher!.firstName, \r\n      icon, \r\n      hoursLeft, \r\n      teacherId: teacher!.id, \r\n      classroom: id \r\n    };\r\n  }\r\n  \r\n  private getOutputDate(): Date {\r\n    const outputFormat = \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)\";\r\n    const inputDate = this.extraData.startingDate;\r\n    const outputDateStr = moment.utc(inputDate).startOf('day').local().format(outputFormat);\r\n    \r\n    const outputDate = new Date(Date.parse(outputDateStr));\r\n    outputDate.setHours(0, 0, 0, 0);\r\n  \r\n    return outputDate;\r\n  }\r\n\r\n  private hasExtraData() {\r\n    return typeof this.extraData !== 'undefined' && Object.keys(this.extraData).length > 0;\r\n  }\r\n\r\n  private hasExtraClassroomData(): boolean {\r\n    return !!this.extraClassroomData && Object.keys(this.extraClassroomData).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if the selected date and time is in the past.\r\n   * @param {Date} selectedDateTime - The selected date and time.\r\n   * @returns {boolean} True if the selected date and time is in the past, false otherwise.\r\n   */\r\n  private isDateTimeInThePast(selectedDateTime: Date) {\r\n    const currentDateTime = moment();\r\n    const selectedDateTimeMoment = moment(selectedDateTime);\r\n  \r\n    return selectedDateTimeMoment.isBefore(currentDateTime);\r\n  }\r\n\r\n}\r\n\r\n", "<div #bookingWrapper>\r\n<app-block-viewer header=\"Booking System\"\r\nheaderBackgroundImage=\"/assets/images/dashboard/calendar-banner.svg\" blockClass=\"border-radius-bottom-10\"\r\ncontainerClass=\"bg-white p-2 border-round-lg\" [headerClass]=\"'justify-content-center my-2'\"\r\n[backButtonLabel]=\"'Back'\"\r\n[headerTextClass]=\"'font-xl font-semibold justify-content-center'\" style=\"height: inherit;\">\r\n    <div class=\"surface-section lg:px-1 lg:h-full\">\r\n        <div class=\"grid h-full flex-column lg:flex-row relative overflow-y-scroll\">\r\n            <div class=\"calendar-nav hidden lg:block\">\r\n                <button type=\"button\" class=\"prev\" (click)=\"goToPrevWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n                <button type=\"button\" class=\"next\" (click)=\"goToNextWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n            </div>\r\n            <div class=\"col lg:col-fixed lg:pr-0 lg:m-3 lg:m-0 left-col\">\r\n                <div class=\"flex align-items-center justify-content-start flex-wrap gap-3 mb-3\">\r\n                    <label for=\"Teacher\" class=\"col-fix flex gap-1 font-sm\" style=\"width: 70px;\"><img\r\n                            src=\"/assets/icons/student.svg\" />\r\n                            {{user.role == UserRoles.TEACHER ? 'Classroom':'Teacher'}} </label>\r\n\r\n                <ng-container *ngIf=\"user.role == UserRoles.TEACHER\">\r\n                <p-dropdown [options]=\"availableClassrooms\" [(ngModel)]=\"studentSelectedClassroom\" \r\n                styleClass=\"max-w-10rem\" panelStyleClass=\"bordered-panel\"\r\n                     placeholder=\"Select Classroom\" (ngModelChange)=\"onClassroomChange($event)\">\r\n                    <ng-template pTemplate=\"selectedItem\">\r\n                        <div class=\"flex align-items-center gap-2\" *ngIf=\"studentSelectedClassroom\">\r\n                            <img *ngIf=\"authService.isTeacher\" [src]=\"classroomService.getClassroomTypeSmallIconPath(studentSelectedClassroom)\"\r\n                            height=\"12\" />\r\n                            <div class=\"font-xs\" [innerHTML]=\"classroomService.getStudentNamesWithHTML(studentSelectedClassroom)\"></div>\r\n                        </div>\r\n                    </ng-template>\r\n                    <ng-template let-classroom pTemplate=\"item\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <img *ngIf=\"authService.isTeacher\" [src]=\"classroomService.getClassroomTypeSmallIconPath(classroom)\"\r\n                            height=\"12\" />\r\n                            <div class=\"font-xs\" [innerHTML]=\"classroomService.getStudentNamesWithHTML(classroom, true)\"></div>\r\n                            <br>\r\n                        </div>\r\n                        <div class=\"flex gap-1 font-xs\">\r\n                             <img src=\"/assets/images/dashboard/calendar/check-circled.svg\" style=\"width: 12px\"/>\r\n                             {{ generalService.convertHoursToMinutesWithSuffix(classroomService.getAccumulatedHoursLeft(classroom.packages!), true) }} available</div>\r\n                    </ng-template>\r\n                </p-dropdown>\r\n                </ng-container>\r\n\r\n                        <ng-container *ngIf=\"user.role == UserRoles.STUDENT\">\r\n                            <p-dropdown [options]=\"teachersLabels\" [(ngModel)]=\"selectedTeachersLabel\" optionLabel=\"teacherName\"\r\n                            styleClass=\"max-w-10rem\" placeholder=\"Select Teacher\" \r\n                            (ngModelChange)=\"onTeacherChange($event)\">\r\n                                <ng-template pTemplate=\"selectedItem\">\r\n                                    <div class=\"flex align-items-center gap-2\" *ngIf=\"selectedTeachersLabel\">\r\n                                        <div class=\"capitalize\">{{ selectedTeachersLabel.language }} - {{ selectedTeachersLabel.teacherName }}</div>\r\n                                    </div>\r\n                                </ng-template>\r\n                                <ng-template let-teacher pTemplate=\"item\">\r\n                                    <div class=\"flex align-items-center gap-2 font-xs\">\r\n                                        <img src=\"/assets/images/dashboard/calendar/{{ teacher.icon }}.svg\" style=\"width: 18px\"/>\r\n                                        <div class=\"capitalize\">{{ teacher.language }} - {{ teacher.teacherName }}</div>\r\n                                        <br>\r\n                                    </div>\r\n                                      \r\n                                    <div class=\"flex gap-1 font-xs\">\r\n                                        <img src=\"/assets/images/dashboard/calendar/check-circled.svg\" style=\"width: 12px\"/>\r\n                                        {{ generalService.convertHoursToMinutesWithSuffix(teacher.hoursLeft, true) }} available</div>\r\n                                </ng-template>\r\n                            </p-dropdown>\r\n                        </ng-container>\r\n                            \r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-start flex-wrap gap-3 mb-3\">\r\n                    <label for=\"Duration\" class=\"col-fix flex gap-1 font-sm\" style=\"width: 70px;\">\r\n                        <img src=\"/assets/icons/hourglass.svg\" /> Duration</label>\r\n                        <p-dropdown [options]=\"availableDuration\" \r\n                        [(ngModel)]=\"selectedDuration\" \r\n                        (onChange)=\"onDurationSelected($event)\"\r\n                        optionLabel=\"viewValue\"\r\n                        ></p-dropdown>\r\n                    <!-- <p-dropdown class=\"dropdown\" [options]=\"classrooms\" (onChange)=\"onChooseClassroomByStudent($event)\"\r\n                        styleClass=\"dropdown-blue rounded\" optionLabel=\"language\">\r\n                        <ng-template let-classroom pTemplate=\"item\">\r\n                            <div class=\"country-item\">\r\n                                <div class=\"country-name\">{{classroom.language}}</div>\r\n                            </div>\r\n                        </ng-template>\r\n                    </p-dropdown> -->\r\n                </div>\r\n\r\n                <div class=\"flex flex-column justify-content-between\"\r\n                    [ngStyle]=\"{'height': !isTabletScreenSize() ? height -150 + 'px' : 'auto'}\">\r\n                    <div>\r\n                    <div class=\"mt-5\">\r\n                        <app-calendar-agenda (dayClicked)=\"onAgendaDayClicked($event)\" (currentMonthChanged)=\"onCurrentMonthChanged($event)\" [currentSelectedDay]=\"selectedDay\" title=\"\"\r\n                            [reverseGradient]=\"true\" [showAvailability]=\"true\" [userId]=\"calendarAgendaUserId\"></app-calendar-agenda>\r\n                    </div>\r\n                \r\n                    <div class=\"grid font-2xs justify-content-between\">\r\n                        <div class=\"col-fixed col-circle pr-0 pb-0 gap-1\">\r\n                            <div class=\"high-bg circle\"></div>\r\n                            <div>High Availability</div>\r\n                        </div>\r\n                        <div class=\"col-fixed col-circle pr-0 pb-0 gap-1\">\r\n                            <div class=\"low-bg circle\"></div>\r\n                            <div>Low Availability</div>\r\n                        </div>\r\n                        <div class=\"col-fixed col-circle pb-0 gap-1\">\r\n                            <div class=\"no-bg circle\"></div>\r\n                            <div>No Availability</div>\r\n                        </div>\r\n                    </div>\r\n                    </div>\r\n                    <div *ngIf=\"radioChecked\">\r\n                        <div class=\"border-round-xl block-gradient-reverse mt-3 mb-0\">\r\n                            <div class=\"card-header text-center\">\r\n                                <div class=\"card-title font-xs py-2\">You have chosen a lesson for</div>\r\n                            </div>\r\n                            <div class=\"card-body px-2\">\r\n                                <div class=\"flex justify-content-center font-sm gap-1 text-primary\">\r\n                                    <div class=\"\">\r\n                                        <i class=\"pi pi-calendar font-sm mr-1\"></i>\r\n                                        <span *ngIf=\"selectedDay\">{{selectedDay | date:'EEEE, dd/MM'}}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <i class=\"pi pi-clock font-sm mr-1\"></i>\r\n                                        <span *ngIf=\"selectedTimeSlot\">{{selectedTimeSlot}}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <i class=\"pi pi-hourglass font-sm mr-1\"></i>\r\n                                        <span>{{selectedDuration.viewValue}}</span>\r\n                                    </div>\r\n                                </div>\r\n                                <!-- <div class=\"flex w-100\">\r\n                                            <button class=\"extend-backage w-100 border-noround\" pButton type=\"button\" label=\"EXTEND Package\"></button>\r\n                                            </div> -->\r\n                            </div>\r\n                        </div>\r\n                        <div [hidden]=\"true\">\r\n                            <div class=\"grid justify-content-evenly font-2xs  mt-2\">\r\n                                <div class=\"col-fixed flex align-items-center\">\r\n                                    <img src=\"/assets/icons/add-to-calendar.svg\" width=\"15\" class=\"mr-1\" />Add to Google Calendar\r\n                                </div>\r\n                                <div class=\"col-fixed align-items-center pr-0\">\r\n                                    <img src=\"/assets/icons/add-to-calendar.svg\" width=\"15\" class=\"mr-1\" />Add to Outlook Calendar\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"flex justify-content-center mt-2\">\r\n                            <button (click)=\"onLessonRequested()\" styleClass=\" px-2 \" icon=\"pi pi-calendar\" iconPos=\"left\"\r\n                                class=\"font-base mt-2 border-round-lg request\" pButton type=\"button\"\r\n                                [loading]=\"buttonLoading\"\r\n                                [label]=\"isRescheduling ? 'RESCHEDULE' : 'REQUEST'\"></button>\r\n                        </div>\r\n                    </div>\r\n                \r\n                </div>\r\n            </div>\r\n            <div class=\"col h-full p-2 md:pr-4 right-col\">\r\n                \r\n            <div class=\"calendar-nav relative block lg:hidden\">\r\n                <button type=\"button\" class=\"prev z-5\" (click)=\"goToPrevWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n                <button type=\"button\" class=\"next z-5\" (click)=\"goToNextWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n            </div>\r\n                <div class=\"appointment-popup h-full pl-1\">\r\n                    <div id=\"calendar\" class=\"appointment-calendar h-full\">\r\n                        <!-- <div class=\"current-week\">\r\n                            <span><i class=\"fa fa-calendar\"></i> Week of {{startDate | date: 'mediumDate'}} </span>\r\n                            <div class=\"calendar-nav\">\r\n                            </div>\r\n                        </div> -->\r\n                        <div class=\"calendar-wrapper\">\r\n                            <div class=\"flex align-items-center justify-content-center text-primary mb-1\">\r\n                                {{findMonthFromDateRange()}}\r\n                            </div> \r\n                            <div class=\"calendar-week relative flex justify-content-center\">\r\n\r\n                                <ul class=\"p-0 mx-2\">\r\n                                    <li *ngFor=\"let day of weekDays\">{{day | date: 'EEE'}} {{day.getDate()}}</li>\r\n                                </ul>\r\n                            </div>\r\n\r\n                            \r\n\r\n                                <div class=\"calendar-hours\">\r\n                                    <p-scrollPanel #scrollPanel [style]=\"{width: '100%'}\" styleClass=\"custombar1 h-30rem lg:h-full\">\r\n                                        \r\n                        <ng-container *ngIf=\"noClassroomsFound\">\r\n                            <div class=\"flex w-full flex-column align-items-center justify-content-center\">\r\n\r\n                                <div class=\"text-center w-full pt-5\">\r\n                                <img src=\"/assets/icons/fast-time-clock-ui-icon.png\" class=\"max-w-14rem\" />\r\n                                </div>\r\n\r\n                                <p class=\"text-primary font-lg\"> You have <b>0</b> hours available</p>\r\n                                <a *ngIf=\"authService.isStudent\" type=\"button\" (click)=\"generalService.navigateToBuyPackage()\" class=\"font-lg gray-border-button border-round-3xl gap-2 px-3 py-2\">\r\n                                    Purchase Package\r\n                                </a>\r\n                            </div>\r\n                        </ng-container>\r\n                                        <ul class=\"calendar-inside pb-2\">\r\n                                            <li *ngFor=\"let day of weekDays\">\r\n                                                <ul *ngFor=\"let hour of availableHours\">\r\n                                                    <ng-container *ngIf=\"isTimeSlotAvailableOnDay(day, hour) || authService.isTeacher\">\r\n                                                    <li [ngClass]=\"{\r\n                                                        'availability': isTimeSlotAvailableOnDay(day, hour),\r\n                                                        'no-availability': !isTimeSlotAvailableOnDay(day, hour) && authService.isTeacher,\r\n                                                        }\"\r\n                                                        class=\"relative font-sm\">\r\n                                                        <div class=\"flex justify-content-center align-items-center\"\r\n                                                        [ngClass]=\"{'pointer-events-none': !isTimeSlotAvailableOnDay(day, hour)}\">\r\n                                                            <label [class.checked]=\"isSelectedDayAndTime(day, hour) && radioChecked\" class=\"hour-label\" for=\"radio{{day}}{{hour}}\" (click)=\"onTimeSlotSelected(day, hour)\">{{hour}}</label>\r\n                                                            <input [disabled]=\"!isTimeSlotAvailableOnDay(day, hour)\" type=\"radio\" id=\"radio{{day}}{{hour}}\"\r\n                                                                name=\"timeslot\" value=\"{{hour}}\" [checked]=\"isSelectedDayAndTime(day, hour) && radioChecked ? true : null\"\r\n                                                                 />\r\n                                                        </div>\r\n                                                    </li>\r\n                                                    </ng-container>\r\n                                                </ul>\r\n                                            </li>\r\n                                        </ul>\r\n                                    </p-scrollPanel>\r\n                                </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</app-block-viewer>\r\n</div>"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChE,SAAoBC,aAAa,QAAQ,qCAAqC;AAG9E,SAASC,OAAO,QAAQ,SAAS;AAKjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAAoCC,YAAY,QAA6B,kCAAkC;AAE/G,SAAeC,QAAQ,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICanCC,EAAA,CAAAC,SAAA,cACc;;;;IADqBD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,gBAAA,CAAAC,6BAAA,CAAAF,MAAA,CAAAG,wBAAA,GAAAN,EAAA,CAAAO,aAAA,CAAgF;;;;;IADvHP,EAAA,CAAAQ,cAAA,cAA4E;IACxER,EAAA,CAAAS,UAAA,IAAAC,yEAAA,kBACc;IACdV,EAAA,CAAAC,SAAA,cAA4G;IAChHD,EAAA,CAAAW,YAAA,EAAM;;;;IAHIX,EAAA,CAAAY,SAAA,EAA2B;IAA3BZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAU,WAAA,CAAAC,SAAA,CAA2B;IAEZd,EAAA,CAAAY,SAAA,EAAgF;IAAhFZ,EAAA,CAAAE,UAAA,cAAAC,MAAA,CAAAC,gBAAA,CAAAW,uBAAA,CAAAZ,MAAA,CAAAG,wBAAA,GAAAN,EAAA,CAAAgB,cAAA,CAAgF;;;;;IAHzGhB,EAAA,CAAAS,UAAA,IAAAQ,mEAAA,kBAA4E;;;;IAAhCjB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAG,wBAAA,CAA8B;;;;;IAQtEN,EAAA,CAAAC,SAAA,cACc;;;;;IADqBD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,gBAAA,CAAAC,6BAAA,CAAAa,YAAA,GAAAlB,EAAA,CAAAO,aAAA,CAAiE;;;;;IADxGP,EAAA,CAAAQ,cAAA,cAA2C;IACvCR,EAAA,CAAAS,UAAA,IAAAU,mEAAA,kBACc;IAEdnB,EADA,CAAAC,SAAA,cAAmG,SAC/F;IACRD,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,cAAgC;IAC3BR,EAAA,CAAAC,SAAA,cAAoF;IACpFD,EAAA,CAAAoB,MAAA,GAAmI;IAAApB,EAAA,CAAAW,YAAA,EAAM;;;;;IAPpIX,EAAA,CAAAY,SAAA,EAA2B;IAA3BZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAU,WAAA,CAAAC,SAAA,CAA2B;IAEZd,EAAA,CAAAY,SAAA,EAAuE;IAAvEZ,EAAA,CAAAE,UAAA,cAAAC,MAAA,CAAAC,gBAAA,CAAAW,uBAAA,CAAAG,YAAA,SAAAlB,EAAA,CAAAgB,cAAA,CAAuE;IAK3FhB,EAAA,CAAAY,SAAA,GAAmI;IAAnIZ,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAmB,cAAA,CAAAC,+BAAA,CAAApB,MAAA,CAAAC,gBAAA,CAAAoB,uBAAA,CAAAN,YAAA,CAAAO,QAAA,uBAAmI;;;;;;IApBhJzB,EAAA,CAAA0B,uBAAA,GAAqD;IACrD1B,EAAA,CAAAQ,cAAA,qBAEgF;IAFpCR,EAAA,CAAA2B,gBAAA,2BAAAC,oFAAAC,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAC,GAAA;MAAA,MAAA5B,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAA9B,MAAA,CAAAG,wBAAA,EAAAuB,MAAA,MAAA1B,MAAA,CAAAG,wBAAA,GAAAuB,MAAA;MAAA,OAAA7B,EAAA,CAAAkC,WAAA,CAAAL,MAAA;IAAA,EAAsC;IAE9C7B,EAAA,CAAAmC,UAAA,2BAAAP,oFAAAC,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAC,GAAA;MAAA,MAAA5B,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAiB/B,MAAA,CAAAiC,iBAAA,CAAAP,MAAA,CAAyB;IAAA,EAAC;IAQ3E7B,EAPA,CAAAS,UAAA,IAAA4B,6DAAA,0BAAsC,IAAAC,6DAAA,0BAOM;IAWhDtC,EAAA,CAAAW,YAAA,EAAa;;;;;IArBDX,EAAA,CAAAY,SAAA,EAA+B;IAA/BZ,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAoC,mBAAA,CAA+B;IAACvC,EAAA,CAAAwC,gBAAA,YAAArC,MAAA,CAAAG,wBAAA,CAAsC;;;;;IA8B1DN,EADJ,CAAAQ,cAAA,cAAyE,cAC7C;IAAAR,EAAA,CAAAoB,MAAA,GAA8E;IAC1GpB,EAD0G,CAAAW,YAAA,EAAM,EAC1G;;;;IADsBX,EAAA,CAAAY,SAAA,GAA8E;IAA9EZ,EAAA,CAAAyC,kBAAA,KAAAtC,MAAA,CAAAuC,qBAAA,CAAAC,QAAA,SAAAxC,MAAA,CAAAuC,qBAAA,CAAAE,WAAA,KAA8E;;;;;IAD1G5C,EAAA,CAAAS,UAAA,IAAAoC,mEAAA,kBAAyE;;;;IAA7B7C,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuC,qBAAA,CAA2B;;;;;IAKvE1C,EAAA,CAAAQ,cAAA,cAAmD;IAC/CR,EAAA,CAAAC,SAAA,cAAyF;IACzFD,EAAA,CAAAQ,cAAA,cAAwB;IAAAR,EAAA,CAAAoB,MAAA,GAAkD;IAAApB,EAAA,CAAAW,YAAA,EAAM;IAChFX,EAAA,CAAAC,SAAA,SAAI;IACRD,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAQ,cAAA,cAAgC;IAC5BR,EAAA,CAAAC,SAAA,cAAoF;IACpFD,EAAA,CAAAoB,MAAA,GAAuF;IAAApB,EAAA,CAAAW,YAAA,EAAM;;;;;IAPxFX,EAAA,CAAAY,SAAA,EAA8D;IAA9DZ,EAAA,CAAA8C,sBAAA,8CAAAC,UAAA,CAAAC,IAAA,UAAAhD,EAAA,CAAAO,aAAA,CAA8D;IAC3CP,EAAA,CAAAY,SAAA,GAAkD;IAAlDZ,EAAA,CAAAyC,kBAAA,KAAAM,UAAA,CAAAJ,QAAA,SAAAI,UAAA,CAAAH,WAAA,KAAkD;IAM1E5C,EAAA,CAAAY,SAAA,GAAuF;IAAvFZ,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAmB,cAAA,CAAAC,+BAAA,CAAAwB,UAAA,CAAAE,SAAA,sBAAuF;;;;;;IAlBvGjD,EAAA,CAAA0B,uBAAA,GAAqD;IACjD1B,EAAA,CAAAQ,cAAA,qBAE0C;IAFHR,EAAA,CAAA2B,gBAAA,2BAAAuB,oFAAArB,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAqB,GAAA;MAAA,MAAAhD,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAA9B,MAAA,CAAAuC,qBAAA,EAAAb,MAAA,MAAA1B,MAAA,CAAAuC,qBAAA,GAAAb,MAAA;MAAA,OAAA7B,EAAA,CAAAkC,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAE1E7B,EAAA,CAAAmC,UAAA,2BAAAe,oFAAArB,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAqB,GAAA;MAAA,MAAAhD,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAiB/B,MAAA,CAAAiD,eAAA,CAAAvB,MAAA,CAAuB;IAAA,EAAC;IAMrC7B,EALA,CAAAS,UAAA,IAAA4C,6DAAA,0BAAsC,IAAAC,6DAAA,0BAKI;IAW9CtD,EAAA,CAAAW,YAAA,EAAa;;;;;IAnBDX,EAAA,CAAAY,SAAA,EAA0B;IAA1BZ,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAoD,cAAA,CAA0B;IAACvD,EAAA,CAAAwC,gBAAA,YAAArC,MAAA,CAAAuC,qBAAA,CAAmC;;;;;IAyE9D1C,EAAA,CAAAQ,cAAA,WAA0B;IAAAR,EAAA,CAAAoB,MAAA,GAAoC;;IAAApB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3CX,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAAwD,iBAAA,CAAAxD,EAAA,CAAAyD,WAAA,OAAAtD,MAAA,CAAAuD,WAAA,iBAAoC;;;;;IAI9D1D,EAAA,CAAAQ,cAAA,WAA+B;IAAAR,EAAA,CAAAoB,MAAA,GAAoB;IAAApB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3BX,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAAwD,iBAAA,CAAArD,MAAA,CAAAwD,gBAAA,CAAoB;;;;;;IAV3D3D,EAHZ,CAAAQ,cAAA,UAA0B,cACwC,cACrB,cACI;IAAAR,EAAA,CAAAoB,MAAA,mCAA4B;IACrEpB,EADqE,CAAAW,YAAA,EAAM,EACrE;IAGEX,EAFR,CAAAQ,cAAA,cAA4B,cAC4C,cAClD;IACVR,EAAA,CAAAC,SAAA,YAA2C;IAC3CD,EAAA,CAAAS,UAAA,IAAAmD,6CAAA,mBAA0B;IAC9B5D,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,WAAK;IACDR,EAAA,CAAAC,SAAA,aAAwC;IACxCD,EAAA,CAAAS,UAAA,KAAAoD,8CAAA,mBAA+B;IACnC7D,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,WAAK;IACDR,EAAA,CAAAC,SAAA,aAA4C;IAC5CD,EAAA,CAAAQ,cAAA,YAAM;IAAAR,EAAA,CAAAoB,MAAA,IAA8B;IAOpDpB,EAPoD,CAAAW,YAAA,EAAO,EACzC,EACJ,EAIJ,EACJ;IAGEX,EAFR,CAAAQ,cAAA,eAAqB,eACuC,eACL;IAC3CR,EAAA,CAAAC,SAAA,eAAuE;IAAAD,EAAA,CAAAoB,MAAA,+BAC3E;IAAApB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,eAA+C;IAC3CR,EAAA,CAAAC,SAAA,eAAuE;IAAAD,EAAA,CAAAoB,MAAA,gCAC3E;IAERpB,EAFQ,CAAAW,YAAA,EAAM,EACJ,EACJ;IAEFX,EADJ,CAAAQ,cAAA,eAA8C,kBAIc;IAHhDR,EAAA,CAAAmC,UAAA,mBAAA2B,gEAAA;MAAA9D,EAAA,CAAA8B,aAAA,CAAAiC,GAAA;MAAA,MAAA5D,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAS/B,MAAA,CAAA6D,iBAAA,EAAmB;IAAA,EAAC;IAK7ChE,EAFgE,CAAAW,YAAA,EAAS,EAC/D,EACJ;;;;IAhCqBX,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuD,WAAA,CAAiB;IAIjB1D,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwD,gBAAA,CAAsB;IAIvB3D,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAwD,iBAAA,CAAArD,MAAA,CAAA8D,gBAAA,CAAAC,SAAA,CAA8B;IAQ/ClE,EAAA,CAAAY,SAAA,EAAe;IAAfZ,EAAA,CAAAE,UAAA,gBAAe;IAaZF,EAAA,CAAAY,SAAA,GAAyB;IACzBZ,EADA,CAAAE,UAAA,YAAAC,MAAA,CAAAgE,aAAA,CAAyB,UAAAhE,MAAA,CAAAiE,cAAA,4BAC0B;;;;;IA8B/CpE,EAAA,CAAAQ,cAAA,SAAiC;IAAAR,EAAA,CAAAoB,MAAA,GAAuC;;IAAApB,EAAA,CAAAW,YAAA,EAAK;;;;IAA5CX,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAAyC,kBAAA,KAAAzC,EAAA,CAAAyD,WAAA,OAAAY,MAAA,eAAAA,MAAA,CAAAC,OAAA,OAAuC;;;;;;IAiB5EtE,EAAA,CAAAQ,cAAA,YAAmK;IAApHR,EAAA,CAAAmC,UAAA,mBAAAoC,uEAAA;MAAAvE,EAAA,CAAA8B,aAAA,CAAA0C,GAAA;MAAA,MAAArE,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAS/B,MAAA,CAAAmB,cAAA,CAAAmD,oBAAA,EAAqC;IAAA,EAAC;IAC1FzE,EAAA,CAAAoB,MAAA,yBACJ;IAAApB,EAAA,CAAAW,YAAA,EAAI;;;;;IAVZX,EAAA,CAAA0B,uBAAA,GAAwC;IAGhC1B,EAFJ,CAAAQ,cAAA,cAA+E,cAEtC;IACrCR,EAAA,CAAAC,SAAA,cAA2E;IAC3ED,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAQ,cAAA,YAAgC;IAACR,EAAA,CAAAoB,MAAA,iBAAS;IAAApB,EAAA,CAAAQ,cAAA,QAAG;IAAAR,EAAA,CAAAoB,MAAA,QAAC;IAAApB,EAAA,CAAAW,YAAA,EAAI;IAACX,EAAA,CAAAoB,MAAA,uBAAe;IAAApB,EAAA,CAAAW,YAAA,EAAI;IACtEX,EAAA,CAAAS,UAAA,IAAAiE,mDAAA,gBAAmK;IAGvK1E,EAAA,CAAAW,YAAA,EAAM;;;;;IAHEX,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAU,WAAA,CAAA8D,SAAA,CAA2B;;;;;;IAQX3E,EAAA,CAAA0B,uBAAA,GAAmF;IAQ3E1B,EAPR,CAAAQ,cAAA,aAI6B,cAEiD,gBACyF;IAAxCR,EAAA,CAAAmC,UAAA,mBAAAyC,iFAAA;MAAA5E,EAAA,CAAA8B,aAAA,CAAA+C,IAAA;MAAA,MAAAC,QAAA,GAAA9E,EAAA,CAAAgC,aAAA,GAAA+C,SAAA;MAAA,MAAAC,OAAA,GAAAhF,EAAA,CAAAgC,aAAA,GAAA+C,SAAA;MAAA,MAAA5E,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAS/B,MAAA,CAAA8E,kBAAA,CAAAD,OAAA,EAAAF,QAAA,CAA6B;IAAA,EAAC;IAAC9E,EAAA,CAAAoB,MAAA,GAAQ;IAAApB,EAAA,CAAAW,YAAA,EAAQ;IAC/KX,EAAA,CAAAC,SAAA,gBAEO;IAEfD,EADI,CAAAW,YAAA,EAAM,EACL;;;;;;;IAZDX,EAAA,CAAAY,SAAA,EAGE;IAHFZ,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAkF,eAAA,KAAAC,GAAA,EAAAhF,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,IAAA3E,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAU,WAAA,CAAAC,SAAA,EAGE;IAGFd,EAAA,CAAAY,SAAA,EAAyE;IAAzEZ,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAqF,eAAA,KAAAC,GAAA,GAAAnF,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,GAAyE;IAC9D9E,EAAA,CAAAY,SAAA,EAAiE;IAAjEZ,EAAA,CAAAuF,WAAA,YAAApF,MAAA,CAAAqF,oBAAA,CAAAR,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAsF,YAAA,CAAiE;IAAoBzF,EAAA,CAAA0F,sBAAA,iBAAAV,OAAA,MAAAF,QAAA,KAA0B;IAAyC9E,EAAA,CAAAY,SAAA,EAAQ;IAARZ,EAAA,CAAAwD,iBAAA,CAAAsB,QAAA,CAAQ;IACjG9E,EAAA,CAAAY,SAAA,EAAyB;IAAzBZ,EAAA,CAAA0F,sBAAA,gBAAAV,OAAA,MAAAF,QAAA,KAAyB;IAC3E9E,EAAA,CAAA2F,qBAAA,UAAAb,QAAA,CAAgB;IAAC9E,EAD9B,CAAAE,UAAA,cAAAC,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,EAAiD,YAAA3E,MAAA,CAAAqF,oBAAA,CAAAR,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAsF,YAAA,eACsD;;;;;IAX1HzF,EAAA,CAAAQ,cAAA,SAAwC;IACpCR,EAAA,CAAAS,UAAA,IAAAmF,yDAAA,4BAAmF;IAevF5F,EAAA,CAAAW,YAAA,EAAK;;;;;;IAfcX,EAAA,CAAAY,SAAA,EAAkE;IAAlEZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAU,WAAA,CAAAC,SAAA,CAAkE;;;;;IAFzFd,EAAA,CAAAQ,cAAA,SAAiC;IAC7BR,EAAA,CAAAS,UAAA,IAAAoF,0CAAA,iBAAwC;IAiB5C7F,EAAA,CAAAW,YAAA,EAAK;;;;IAjBoBX,EAAA,CAAAY,SAAA,EAAiB;IAAjBZ,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA2F,cAAA,CAAiB;;;ADlJtF,OAAM,MAAOC,sBAAsB;EA2CjCC,YACS5F,gBAAkC,EAClCkB,cAA8B,EAC7BT,WAAwB,EACxBoF,WAAwB,EACxBC,eAAgC,EAChCC,aAA4B,EAC5BC,aAA4B,EAC7BC,YAA0B,EACzBC,KAAqB,EACrBC,MAAc,EACdC,KAAwB,EACxBC,QAAkB;IAXnB,KAAArG,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAkB,cAAc,GAAdA,cAAc;IACb,KAAAT,WAAW,GAAXA,WAAW;IACX,KAAAoF,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAtD6B,KAAAC,cAAc,GAAe,EAAgB;IAChD,KAAAC,WAAW,GAAgB,EAAiB;IAC/E,KAAArG,wBAAwB,GAAc,EAAe;IAC9D,KAAAsG,SAAS,GAAS,IAAIC,IAAI,EAAE;IAC5B,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,KAAK,GAAa,EAAE;IACpB,KAAAC,QAAQ,GAAmD,EAAE;IAC7D,KAAAlB,cAAc,GAAa,EAAE;IACrB,KAAAmB,IAAI,GAAG,IAAIrH,OAAO,EAAE;IACrB,KAAAsH,UAAU,GAAgB,EAAE;IAC5B,KAAAC,8BAA8B,GAAgB,EAAE;IAChD,KAAAC,aAAa,GAAG,IAAI;IAC3B,KAAAC,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,oBAAoB,GAAU,EAAE;IAGhC,KAAAC,IAAI,GAAS,EAAU;IACvB,KAAAlE,cAAc,GAAU,EAAE;IAC1B,KAAAb,qBAAqB,GAAQ,IAAI;IACjC,KAAAgF,YAAY,GAAQ,IAAI;IACxB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,iBAAiB,GAAU,EAAE;IAC7B,KAAA3D,gBAAgB,GAAQ,IAAI;IAC5B,KAAA4D,GAAG,GAAsB,EAAuB;IAChD,KAAAC,eAAe,GAAG,EAAE;IAGpB,KAAAvF,mBAAmB,GAAU,EAAE;IAC/B,KAAAwF,SAAS,GAAGhI,QAAQ;IACpB,KAAA0F,YAAY,GAAG,KAAK;IACpB,KAAAuC,oBAAoB,GAAG,EAAE;IACzB,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,kBAAkB,GAAQ,EAAE;IAC5B,KAAA9D,cAAc,GAAG,KAAK;IACtB,KAAA+D,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAApE,aAAa,GAAG,KAAK;IAenB,IAAI,CAACyC,SAAS,GAAG,IAAIC,IAAI,EAAE;EAC7B;EACA2B,QAAQA,CAAA;IACN,MAAMC,WAAW,GAAG,IAAI,CAACnC,KAAK,CAACoC,QAAQ,CAACD,WAAW;IACnD,MAAME,YAAY,GAAGF,WAAW,CAAC,QAAQ,CAAC;IAC1C,MAAMG,eAAe,GAAGH,WAAW,CAAC,WAAW,CAAC;IAChD,MAAMI,mBAAmB,GAAGJ,WAAW,CAAC,YAAY,CAAC;IACrD,MAAMK,cAAc,GAAGL,WAAW,CAAC,UAAU,CAAC;IAC9C,IAAI,CAACL,UAAU,GAAG,IAAI,CAACW,kBAAkB,EAAE;IAE3C,IAAIF,mBAAmB,EAAE;MACvB,IAAI,CAACzE,cAAc,GAAG,IAAI;IAC5B;IACA,IAAI0E,cAAc,EAAE;MAClB,IAAI,CAACX,SAAS,GAAG,IAAI;IACvB;IACA,IAAIQ,YAAY,EAAE;MAChB,MAAMK,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACP,YAAY,CAAC;MACvCQ,OAAO,CAACC,GAAG,CAACJ,MAAM,CAAC;MACnB,IAAI,CAACf,SAAS,GAAG;QACf,UAAU,EAAEe,MAAM,CAACK,EAAE;QACrB,cAAc,EAAEL,MAAM,CAACM,MAAM;QAC7B,aAAa,EAAEN,MAAM,CAACO,WAAW;QACjC,QAAQ,EAAEP,MAAM,CAACM,MAAM;QACvB,cAAc,EAAEN,MAAM,CAACQ,YAAY;QACnC,UAAU,EAAER,MAAM,CAACrB,QAAQ;QAC3B,cAAc,EAAEqB,MAAM,CAACS;OACxB;IACH;IAEA,IAAIb,eAAe,EAAE;MACnB,MAAMc,SAAS,GAAGT,IAAI,CAACC,KAAK,CAACN,eAAe,CAAC;MAC7CO,OAAO,CAACC,GAAG,CAACM,SAAS,CAAC;MACtB,IAAI,CAACxB,kBAAkB,GAAG;QACxB,aAAa,EAAEwB,SAAS,CAACL,EAAE;QAC3B,QAAQ,EAAEK,SAAS,CAACJ,MAAM;QAC1B,eAAe,EAAEI,SAAS,CAACC;OAC5B;IACH;IAEA,IAAI,CAAChC,QAAQ,GAAG,CACd;MAAEiC,KAAK,EAAE,EAAE;MAAE1F,SAAS,EAAE;IAAK,CAAC,EAC9B;MAAE0F,KAAK,EAAE,EAAE;MAAE1F,SAAS,EAAE;IAAK,CAAE,EAC/B;MAAE0F,KAAK,EAAE,EAAE;MAAE1F,SAAS,EAAE;IAAK,CAAE,EAC/B;MAAE0F,KAAK,EAAE,EAAE;MAAE1F,SAAS,EAAE;IAAI,CAAE,EAC9B;MAAE0F,KAAK,EAAE,EAAE;MAAE1F,SAAS,EAAE;IAAQ,CAAE,EAClC;MAAE0F,KAAK,EAAE,EAAE;MAAE1F,SAAS,EAAE;IAAQ,CAAE,EAClC;MAAE0F,KAAK,EAAE,GAAG;MAAE1F,SAAS,EAAE;IAAQ,CAAE,EACnC;MAAE0F,KAAK,EAAE,GAAG;MAAE1F,SAAS,EAAE;IAAI,CAAE,EAC/B;MAAE0F,KAAK,EAAE,GAAG;MAAE1F,SAAS,EAAE;IAAQ,CAAE,EACnC;MAAE0F,KAAK,EAAE,GAAG;MAAE1F,SAAS,EAAE;IAAQ,CAAE,EACnC;MAAE0F,KAAK,EAAE,GAAG;MAAE1F,SAAS,EAAE;IAAQ,CAAE,EACnC;MAAE0F,KAAK,EAAE,GAAG;MAAE1F,SAAS,EAAE;IAAI,CAAE,CAChC;IACD,IAAI,CAAC0D,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;IAC3C,IAAI,CAAC1D,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAAC;IACxC,IAAI,CAACkC,iBAAiB,CAAC,IAAIhD,IAAI,EAAE,CAAC;IAClC,IAAI,CAACiD,cAAc,EAAE;IACrB,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI,CAACtC,IAAI,GAAG,IAAI,CAAC5G,WAAW,CAACmJ,eAAe,EAAE;IAE9C,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAlB,kBAAkBA,CAAA;IAChB,MAAMmB,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,OAAOF,WAAW,GAAG,GAAG;EAC1B;EAGAG,kBAAkBA,CAAA;IAChB,MAAMH,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,OAAOF,WAAW,GAAG,IAAI,CAAC,CAAC;EAC7B;EAEAI,WAAWA,CAAA;IAET,IAAI,CAAClC,UAAU,GAAG,IAAI,CAACW,kBAAkB,EAAE;IAC3CI,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChB,UAAU,CAAC;EAC9B;EAEAmC,eAAeA,CAAA;IAEb,IAAI,CAACtD,IAAI,CAACuD,IAAI,GAAG,IAAI,CAAClJ,cAAc,CAACmJ,UAAU,CAACC,IAAI,CAClDhL,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAACkL,GAAG,IAAG;MACdxB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;MAChB,IAAIA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,KAAK,EAAE;QAC1B,OAAOvL,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,MAAM;QACL,OAAO,IAAI,CAAC6G,aAAa,CAAC2E,cAAc;MAC1C;IACF,CAAC,CAAC,EACFtL,SAAS,CAAC,CAAC,CAAC,CACb,CAACuL,SAAS,CAACzC,MAAM,IAAG;MACnB,IAAIA,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI,CAAC0C,YAAY,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC1C,MAAM,GAAGA,MAAM;QAClB,IAAI,CAAC5B,cAAc,CAACuE,aAAa,CAACC,KAAK,CAAC5C,MAAM,GAAIA,MAAM,GAAG,EAAE,GAAG,IAAK;MACvE,CAAC,MAAM;QACL,IAAI,CAAC0C,YAAY,CAAC,CAAC,CAAC;QACpB,IAAI,CAACtE,cAAc,CAACuE,aAAa,CAACC,KAAK,CAAC5C,MAAM,GAAI,MAAO;MAC3D;MACA,IAAI,CAAC9B,KAAK,CAAC2E,aAAa,EAAE,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,kBAAkBA,CAAA,GAClB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACrE,IAAI,CAACsE,WAAW,EAAE;EACzB;EAEAP,YAAYA,CAACQ,IAAY;IACvB,IAAI,CAACnD,SAAS,GAAGmD,IAAI;EACvB;EAEAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACpD,SAAS;EACvB;EAEA+C,cAAcA,CAAA;IACZM,UAAU,CAAC,MAAK;MACd,MAAMC,aAAa,GAAG,IAAI,CAAChF,WAAW,CAACiF,kBAAmB,CAACX,aAAa,CAACY,sBAAsB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;MAC3H,MAAMC,oBAAoB,GAAGH,aAAa,CAACI,YAAY,GAAG,CAAC;MAC3DJ,aAAa,CAACK,SAAS,GAAGF,oBAAoB;IAChD,CAAC,EAAE,IAAI,CAAC;EACV;EAEAG,kBAAkBA,CAACC,KAAU;IAC3B,IAAI,CAACvI,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC8B,YAAY,GAAG,KAAK;IACzB0D,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1F,WAAW,CAAC;IAC7B;EACF;EAEAuB,kBAAkBA,CAACkH,GAAS,EAAEC,IAAY;IAExC,IAAI,CAAC1I,WAAW,GAAG7D,MAAM,CAACsM,GAAG,EAAE,iBAAiB,CAAC,CAACE,MAAM,EAAE;IAC1D,IAAI,CAAC1I,gBAAgB,GAAGyI,IAAI;IAC5B,IAAI,CAACE,yBAAyB,EAAE;IAChC,MAAMC,gBAAgB,GAAG1M,MAAM,CAACsM,GAAG,EAAE,kCAAkC,CAAC;IACxE,MAAMK,iBAAiB,GAAGD,gBAAgB,CAACE,MAAM,CAAC,kBAAkB,GAAGL,IAAI,CAAC;IAE5E;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,IAAI,IAAI,CAAC3E,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;MACvC,IAAI,CAAClH,YAAY,GAAG,IAAI;IAC1B,CAAC,MAAM;MACL,IAAI,CAAC,IAAI,CAACmH,mBAAmB,CAAC,IAAI/F,IAAI,CAAC2F,iBAAiB,CAAC,CAAC,EAAE;QAC1D,IAAI,CAAC/G,YAAY,GAAG,IAAI;MAC1B,CAAC,MAAM;QACL,IAAI,CAACY,YAAY,CAACwG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACvH,YAAY,GAAG,KAAK;MAC3B;IACF;EACF;EAEAwH,qBAAqBA,CAAA;IACnB,IAAI,CAACvJ,WAAW,GAAG,IAAImD,IAAI,EAAE;IAC7B,IAAI,CAAClD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC2I,yBAAyB,EAAE;IAChC,IAAI,CAAC7G,YAAY,GAAG,KAAK;EAC3B;EAEAD,oBAAoBA,CAAC2G,GAAS,EAAEC,IAAY;IAC1C,MAAMc,CAAC,GAAG,IAAIrG,IAAI,CAACsF,GAAG,CAAC;IACvB,IAAI,IAAI,CAACgB,YAAY,EAAE,EAAE;MACvB,OAAQD,CAAC,CAAC5I,OAAO,EAAE,GAAG,EAAE,GAAG4I,CAAC,CAACE,QAAQ,EAAE,KAAO,IAAI,CAAC1J,WAAY,CAACY,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAACZ,WAAY,CAAC0J,QAAQ,EAAG,IAAIhB,IAAI,KAAK,IAAI,CAACzI,gBAAgB;IAClJ,CAAC,MAAM;MACL,OAAOwI,GAAG,KAAK,IAAI,CAACzI,WAAW,IAAI0I,IAAI,KAAK,IAAI,CAACzI,gBAAgB;IACnE;EACF;EAEF;;;;;;;EAOU0J,sBAAsBA,CAACC,OAAY;IACzC,IAAI,CAACA,OAAO,IAAI,IAAI,CAAChM,cAAc,CAACiM,eAAe,CAACD,OAAO,CAAC,EAAE;MAC5D,OAAOhO,EAAE,CAAC,IAAI,CAAC;IACjB;IACA,MAAMkO,IAAI,GAAG;MACX,GAAG,IAAI,CAACC,aAAa,EAAE;MACvBC,MAAM,EAAEJ,OAAO,CAACK;KACjB;IACD,OAAO,IAAI,CAACC,wBAAwB,CAACJ,IAAI,CAAC;EAE5C;EAEQI,wBAAwBA,CAACC,WAAoD;IACnF1E,OAAO,CAACC,GAAG,CAACyE,WAAW,CAAC;IACxB,OAAO,IAAI,CAAC3H,eAAe,CAAC4H,4BAA4B,CAACD,WAAW,CAAC,CAACnD,IAAI,CACxEnL,GAAG,CAAEoL,GAAQ,IAAI;MACf;MAEA;MACA,IAAI,CAACoD,aAAa,GAAIpD,GAAG,CAACqD,oBAAqB;MAE/C,IAAI,CAAClI,cAAc,GAAG,IAAI,CAACwG,yBAAyB,EAAE;MAEtD;MACA;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;EAEA2B,wBAAwBA,CAACC,OAAiB,EAAEC,SAAiB;IAC3D,OAAOD,OAAO,CAACE,MAAM,CAAEpF,MAAW,IAAKA,MAAM,CAACsE,OAAO,CAACjE,EAAE,KAAK8E,SAAS,CAAC;EACzE;EAEA/K,eAAeA,CAAC8I,KAA4B;IAC9C;IACI,IAAI,CAACvI,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC8B,YAAY,GAAG,KAAK;IAE3B;IACE,IAAI,CAAC8B,eAAe,GAAG,IAAI,CAACF,iBAAiB,CAACgH,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK6C,KAAK,CAACiC,SAAS,CAAC;IAE3F;IACE,MAAM5E,WAAW,GAAG,IAAI,CAAChH,mBAAmB,CAAC8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACgH,SAAS,CAAC;IAC7GP,OAAO,CAACC,GAAG,CAACG,WAAW,CAAC;IAE5B;IACI,IAAI,CAACgF,oCAAoC,CAAChF,WAAW,CAAC;IAExD;IACE,IAAI,CAACtC,IAAI,CAACuH,GAAG,CAAC,IAAI,CAACnB,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAI;MAChF,IAAI,CAACnD,oBAAoB,GAAG,IAAI,CAACF,YAAY;MAC7C6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC5B,oBAAoB,CAAC;IAChE,CAAC,CAAC,CAAC;IAEL;IACE,IAAI,CAACQ,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAACoG,SAAS;EAC5D;EAEA;;;;;;;EAOAvL,iBAAiBA,CAAC8J,KAAgB;IAChC/C,OAAO,CAACC,GAAG,CAAC8C,KAAK,CAAC;IAClB,IAAI,CAAC5L,wBAAwB,GAAG4L,KAAK;IACrC/C,OAAO,CAACC,GAAG,CAAC8C,KAAK,CAAC;IAClB,IAAI,IAAI,CAAC5L,wBAAwB,CAACgN,OAAO,EAAE;MACzC,IAAI,CAACtF,oBAAoB,GAAG,IAAI,CAAC1H,wBAAwB,CAACgN,OAAO,EAAEK,SAAU;IAC/E;IACA,IAAI,CAACY,oCAAoC,CAACrC,KAAK,CAAC;EAClD;EAEAuC,eAAeA,CAACnB,OAAa;IAC3B,OAAO,IAAIA,OAAO,CAACoB,QAAQ,IAAIpB,OAAO,CAACqB,SAAS,EAAE;EACpD;EACA;;;;EAKA;;;;EAIA9E,iBAAiBA,CAACjD,SAAe;IAC/B,MAAMgI,KAAK,GAAG/O,MAAM,CAAC+G,SAAS,CAAC,CAACyF,MAAM,EAAE;IACxC,MAAMwC,QAAQ,GAAG,IAAIhI,IAAI,CAAC+H,KAAK,CAACE,WAAW,EAAE,EAAEF,KAAK,CAACxB,QAAQ,EAAE,EAAEwB,KAAK,CAACtK,OAAO,EAAE,CAAC;IACjF,MAAMyK,YAAY,GAAG,IAAIlI,IAAI,CAACgI,QAAQ,CAAC;IACvC,IAAI,CAAC/H,QAAQ,GAAGkI,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE,IAAI,CAAC7G;IAAS,CAAE,EAAE,CAAC8G,CAAC,EAAEC,CAAC,KAAI;MAC9D,MAAMlC,CAAC,GAAG,IAAIrG,IAAI,CAACkI,YAAY,CAAC;MAChC7B,CAAC,CAACmC,OAAO,CAACnC,CAAC,CAAC5I,OAAO,EAAE,GAAG8K,CAAC,CAAC;MAC1B,OAAOlC,CAAC;IACV,CAAC,CAAC;EAEJ;EAEA;;;;EAIAO,aAAaA,CAAA;IACX;IACA,MAAM6B,KAAK,GAAG,IAAI,CAACxI,QAAQ,CAACvH,GAAG,CAACgQ,UAAU,IAAI,IAAI1I,IAAI,CAAC0I,UAAU,CAAC,CAAC;IAEnE;IACA,MAAMC,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;IACzB,MAAMjD,MAAM,GAAGiD,KAAK,CAACA,KAAK,CAACJ,MAAM,GAAG,CAAC,CAAC;IAEtC;IACA;IACA,OAAO;MACLM,QAAQ,EAAE3P,MAAM,CAAE2P,QAAQ,EAAG,iCAAiC,CAAC,CAAC/C,MAAM,CAAC,YAAY,CAAC,GAAG,gBAAgB;MACvGJ,MAAM,EAAExM,MAAM,CAAEwM,MAAM,EAAG,iCAAiC,CAAC,CAACI,MAAM,CAAC,YAAY,CAAC,GAAG;KACpF;EAEH;EAEAgD,sBAAsBA,CAAA;IACpB,MAAMjC,IAAI,GAAG,IAAI,CAACC,aAAa,EAAE;IAEjC,MAAM+B,QAAQ,GAAG3P,MAAM,CAAC2N,IAAI,CAACgC,QAAQ,CAAC;IACtC,MAAMnD,MAAM,GAAGxM,MAAM,CAAC2N,IAAI,CAACnB,MAAM,CAAC;IAElC,MAAMqD,SAAS,GAAGF,QAAQ,CAAC/C,MAAM,CAAC,MAAM,CAAC;IACzC,MAAMkD,OAAO,GAAGtD,MAAM,CAACI,MAAM,CAAC,MAAM,CAAC;IACrC,MAAMmD,QAAQ,GAAGJ,QAAQ,CAAC/C,MAAM,CAAC,MAAM,CAAC;IACxC,MAAMoD,MAAM,GAAGxD,MAAM,CAACI,MAAM,CAAC,MAAM,CAAC;IAEpC,IAAIiD,SAAS,KAAKC,OAAO,EAAE;MACzB,OAAO,GAAGD,SAAS,IAAIE,QAAQ,EAAE;IACnC,CAAC,MAAM;MACL,OAAO,GAAGF,SAAS,MAAMC,OAAO,IAAIE,MAAM,EAAE;IAC9C;EACF;EAEAvD,yBAAyBA,CAAA;IACvB,MAAMwD,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC9E,MAAMhK,cAAc,GAAa,EAAE;IACnC,MAAMiK,SAAS,GAAG,CAAC;IACnB,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMC,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAEjD,KAAK,IAAI7D,IAAI,GAAG2D,SAAS,EAAE3D,IAAI,IAAI4D,OAAO,EAAE5D,IAAI,EAAE,EAAE;MAClD,MAAM8D,UAAU,GAAG9D,IAAI,CAAC+D,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,KAAK,MAAMC,MAAM,IAAIJ,gBAAgB,EAAE;QACrC,MAAMK,UAAU,GAAG,GAAGJ,UAAU,IAAIG,MAAM,EAAE;QAC5C,IAAIE,WAAW,GAAG,IAAI;QAEtB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,IAAIA,WAAW,EAAE;UACfzK,cAAc,CAAC0K,IAAI,CAACF,UAAU,CAAC;QACjC;MACF;IACF;IAEA,OAAOxK,cAAc;EACvB;EAEF;;;EAGAgE,cAAcA,CAAA;IACZ,MAAMiG,SAAS,GAAG,CAAC;IACnB,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMS,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C,IAAI,CAAC1J,KAAK,GAAG,EAAE;IAEf,KAAK,IAAIqF,IAAI,GAAG2D,SAAS,EAAE3D,IAAI,IAAI4D,OAAO,EAAE5D,IAAI,EAAE,EAAE;MAClD,MAAM8D,UAAU,GAAG9D,IAAI,CAAC+D,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEnD,KAAK,MAAMC,MAAM,IAAII,YAAY,EAAE;QACjC,MAAMH,UAAU,GAAG,GAAGJ,UAAU,IAAIG,MAAM,EAAE;QAC5C,IAAI,CAACtJ,KAAK,CAACyJ,IAAI,CAACF,UAAU,CAAC;MAC7B;IACF;IAEA,KAAK,IAAIlE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG2D,SAAS,EAAE3D,IAAI,EAAE,EAAE;MAC3C,MAAM8D,UAAU,GAAG9D,IAAI,CAAC+D,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEnD,KAAK,MAAMC,MAAM,IAAII,YAAY,EAAE;QACjC,MAAMH,UAAU,GAAG,GAAGJ,UAAU,IAAIG,MAAM,EAAE;QAC5C,IAAI,CAACtJ,KAAK,CAACyJ,IAAI,CAACF,UAAU,CAAC;MAC7B;IACF;EACF;EAEAI,qBAAqBA,CAAC3J,KAAa;IACjC,OAAOA,KAAK,GAAG,EAAE;EACnB;EACA4J,gCAAgCA,CAAC5J,KAAa;IAC5C,MAAM9D,SAAS,GAAG8D,KAAK,CAAC,CAAC;IACzB,MAAM6J,WAAW,GAAG,IAAI,CAACF,qBAAqB,CAACzN,SAAS,CAAC;IACzD,MAAM4N,WAAW,GAAG,IAAI,CAACH,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnDvH,OAAO,CAACC,GAAG,CAACnG,SAAS,CAAC;IACtB,IAAI,CAAC2E,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACyG,MAAM,CAAEE,IAAI,IAAKA,IAAI,CAAC1E,KAAK,IAAIgH,WAAW,IAAItC,IAAI,CAAC1E,KAAK,IAAIiH,WAAW,CAAC;EAEjH;EACA;;;;EAIA9G,YAAYA,CAAA;IAEV,MAAM+G,WAAW,GAAG,CAAC;IACrB,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,SAAS,GAAG,EAAE;IAClB,KAAK,MAAM9E,GAAG,IAAI,IAAI,CAACnF,QAAQ,EAAE;MAC/B,MAAMkK,MAAM,GAAG,IAAI,CAAClK,QAAQ,CAACmF,GAAG,CAAC;MACjC,KAAK,MAAMC,IAAI,IAAI8E,MAAM,EAAE;QACzB,IAAIA,MAAM,CAAC9E,IAAI,CAAC,EAAE;UAChB,MAAM,CAAC+E,OAAO,EAAEC,SAAS,CAAC,GAAGhF,IAAI,CAACiF,KAAK,CAAC,GAAG,CAAC;UAC5C,MAAMC,OAAO,GAAGC,QAAQ,CAACJ,OAAO,CAAC;UACjC,MAAMK,SAAS,GAAGD,QAAQ,CAACH,SAAS,CAAC;UACrC,IAAIE,OAAO,GAAGN,OAAO,IAAKM,OAAO,KAAKN,OAAO,IAAIQ,SAAS,GAAGP,SAAU,EAAE;YACvED,OAAO,GAAGM,OAAO;YACjBL,SAAS,GAAGO,SAAS;UACvB;QACF;MACF;IACF;IACA,MAAMzB,SAAS,GAAGiB,OAAO;IACzB,MAAMhB,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIZ,CAAC,GAAGW,SAAS,EAAEX,CAAC,IAAIY,OAAO,EAAEZ,CAAC,EAAE,EAAE;MACzC,MAAMhD,IAAI,GAAGgD,CAAC,CAACe,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1C,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,IAAIpB,MAAM,GAAGoB,CAAC,GAAG,EAAE,GAAGX,WAAW;QACjC,IAAIT,MAAM,IAAI,EAAE,EAAE;UAChBA,MAAM,GAAG,EAAE;QACb;QACA,IAAKjB,CAAC,KAAKY,OAAO,IAAIK,MAAM,GAAGU,SAAS,IAAM3B,CAAC,KAAKW,SAAS,IAAIM,MAAM,GAAGY,SAAU,EAAE;UACpF;QACF;QACA,MAAMX,UAAU,GAAG,GAAGlE,IAAI,IAAIiE,MAAM,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAClE,KAAK,MAAMjE,GAAG,IAAI,IAAI,CAACnF,QAAQ,EAAE;UAC/B,IAAI,CAACA,QAAQ,CAACmF,GAAG,CAAC,CAACmE,UAAU,CAAC,GAAG,IAAI;QACvC;MACF;IACF;EACF;EAEE;;;;;EAKAoB,cAAcA,CAACvF,GAAS,EAAEC,IAAY;IACpC,MAAMmD,UAAU,GAAGpD,GAAG,CAACwF,YAAY,EAAE;IACrC,MAAMpB,WAAW,GAAG,CAAC,IAAI,CAACvJ,QAAQ,CAACuI,UAAU,CAAC,GAAGnD,IAAI,CAAC;IACtD,IAAI,CAACpF,QAAQ,CAACuI,UAAU,CAAC,GAAG,IAAI,CAACvI,QAAQ,CAACuI,UAAU,CAAC,IAAI,EAAE;IAC3D,IAAI,CAACvI,QAAQ,CAACuI,UAAU,CAAC,CAACnD,IAAI,CAAC,GAAGmE,WAAW;EAC/C;EAEA;;;EAGAqB,YAAYA,CAAA;IACV,IAAI,CAAChL,SAAS,CAACyI,OAAO,CAAC,IAAI,CAACzI,SAAS,CAACtC,OAAO,EAAE,GAAG,IAAI,CAAC+D,SAAS,CAAC;IACjE,IAAI,CAACzB,SAAS,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC;IACzCuC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACxC,SAAS,CAAC;IACnC,IAAI,CAACiD,iBAAiB,CAAC,IAAI,CAACjD,SAAS,CAAC;IACtC,IAAI,CAACK,IAAI,CAACuD,IAAI,GAAG,IAAI,CAAC6C,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAG;MAElFxB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;EAEA;;;EAGAkH,YAAYA,CAAA;IACV,IAAI,CAACjL,SAAS,CAACyI,OAAO,CAAC,IAAI,CAACzI,SAAS,CAACtC,OAAO,EAAE,GAAG,IAAI,CAAC+D,SAAS,CAAC;IACjE,IAAI,CAACzB,SAAS,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC;IACzC,IAAI,CAACiD,iBAAiB,CAAC,IAAI,CAACjD,SAAS,CAAC;IACtC,IAAI,CAACK,IAAI,CAACuD,IAAI,GAAG,IAAI,CAAC6C,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAG,CACpF,CAAC,CAAC;EACJ;EAEA;;;;;;EAMAvF,wBAAwBA,CAAC0M,SAAiB,EAAE1F,IAAY;IACtD,MAAMD,GAAG,GAAGtM,MAAM,CAACiS,SAAS,EAAE,6BAA6B,CAAC;IAC5D,MAAMC,YAAY,GAAG5F,GAAG,CAACM,MAAM,CAAC,YAAY,CAAC;IAC7C,MAAMuF,OAAO,GAAG7F,GAAG,CAACM,MAAM,CAAC,KAAK,CAAC,CAACwF,WAAW,EAAE;IAE/C,IAAI,IAAI,CAAClE,aAAa,EAAE;MAGxB,MAAMmD,MAAM,GAAG,IAAI,CAACnD,aAAa,CAACM,IAAI,CAAE6D,CAAM,IAAKA,CAAC,CAACC,IAAI,KAAKJ,YAAY,CAAC;MAE3E;MACA,IAAI,IAAI,CAACtK,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;QACvC,OAAO,IAAI;MACb;MACA,IAAIuE,MAAM,IAAIA,MAAM,CAACkB,YAAY,CAAClD,MAAM,GAAG,CAAC,EAAE;QAC5C,MAAMpJ,cAAc,GAAwBoL,MAAM,CAACkB,YAAY;QAC/D,IAAIC,SAAS,GAAG,IAAI,CAACtL,KAAK,CAACuL,OAAO,CAAClG,IAAI,CAAC;QACxC,MAAMmG,iBAAiB,GAAG,IAAI,CAACtO,gBAAgB,CAAC2F,KAAK;QACrD,KAAK,IAAIwF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtJ,cAAc,CAACoJ,MAAM,EAAEE,CAAC,EAAE,EAAE;UAC9C,MAAMoD,KAAK,GAAG1M,cAAc,CAACsJ,CAAC,CAAC;UAC/B,IAAIqD,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5L,KAAK,CAACuL,OAAO,CAACE,KAAK,CAACvD,IAAI,CAAC,EAAEoD,SAAS,CAAC;UAC/D,MAAMO,GAAG,GAAGJ,KAAK,CAACK,EAAE,KAAK,OAAO,GAAG,IAAI,CAAC9L,KAAK,CAACmI,MAAM,GAAG,CAAC,GAAG,IAAI,CAACnI,KAAK,CAACuL,OAAO,CAACE,KAAK,CAACK,EAAE,CAAC;UAEvF,IAAIJ,KAAK,IAAI,CAAC,IAAIG,GAAG,IAAI,CAAC,IAAIH,KAAK,IAAIJ,SAAS,IAAIA,SAAS,IAAIO,GAAG,EAAE;YACpE,MAAMhL,iBAAiB,GAAG,CAACgL,GAAG,GAAGH,KAAK,IAAID,KAAK,CAACK,EAAE,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;YAC7E,IAAIjL,iBAAiB,IAAI2K,iBAAiB,EAAE;cAC1C,OAAO,IAAI;YACb;UACF;QACF;MACF;IACF;IACE,OAAO,KAAK;EACd;EAED;;;;;EAKCvO,iBAAiBA,CAAA;IACnB;IACI,IAAI,IAAI,CAACL,gBAAgB,KAAK,EAAE,EAAE;MAChC,IAAI,CAAC0C,YAAY,CAACwG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IAEF;IACE,MAAM8F,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;IAEzC;IACE,MAAMC,WAAW,GAAGnT,MAAM,EAAE,CAACoT,OAAO,CAAC,MAAM,CAAC;IAEhD;IACI,MAAMC,OAAO,GAAGrT,MAAM,CAAC,IAAI,CAAC6D,WAAW,CAAC;IAE1C;IACE,MAAMyP,aAAa,GAAGD,OAAO,CAACzG,MAAM,CAAC,6BAA6B,CAAC;IAEnE,IAAI,CAAC,IAAI,CAACrH,wBAAwB,CAAC+N,aAAa,EAAE,IAAI,CAACxP,gBAAgB,CAAC,EAAE;MACxE,IAAI,CAAC0C,YAAY,CAACwG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACE;IAEN;IACE,IAAI,IAAI,CAAC7I,aAAa,EAAE;MACtB;IACF;IAEF;IACE,IAAI,CAACA,aAAa,GAAG,IAAI;IAE3B;IACE,IAAImF,MAAM,GAAGxJ,YAAY,CAACsT,SAAS;IAEvC;IACI,IAAI,IAAI,CAAC3L,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;MACvC,IAAI,IAAI,CAAC0G,qBAAqB,EAAE,EAAE;QAChC,IAAI,IAAI,CAACnL,kBAAkB,CAACoB,MAAM,CAAC2I,WAAW,EAAE,CAACqB,QAAQ,CAAC3T,aAAa,CAAC4T,KAAK,CAACtB,WAAW,EAAE,CAAC,EAAE;UAC5F3I,MAAM,GAAGxJ,YAAY,CAAC0T,cAAc;QACtC,CAAC,MAAM;UACLlK,MAAM,GAAGxJ,YAAY,CAAC2T,QAAQ;QAChC;MACF,CAAC,MAAM;QACL,MAAMC,iBAAiB,GAAG,IAAI,CAACnR,mBAAmB,CAAC8L,IAAI,CAAE3E,SAAc,IAAI;UACzE,OAAOA,SAAS,CAACL,EAAE,KAAK,IAAI,CAAC/I,wBAAwB,EAAE+I,EAAE;QAC3D,CAAC,CAAC;QACF,IAAIqK,iBAAiB,CAAC/J,IAAI,CAACsI,WAAW,EAAE,CAACqB,QAAQ,CAAC3T,aAAa,CAAC4T,KAAK,CAACtB,WAAW,EAAE,CAAC,EAAE;UACpF3I,MAAM,GAAGxJ,YAAY,CAAC0T,cAAc;QACtC,CAAC,MAAM;UACLlK,MAAM,GAAGxJ,YAAY,CAAC2T,QAAQ;QAChC;MACF;IACF;IAEA,IAAIlK,WAAW;IAEjB;IACE,IAAI,IAAI,CAAC9B,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4T,OAAO,EAAE;MACvC,MAAMD,iBAAiB,GAAG,IAAI,CAACnR,mBAAmB,CAAC8L,IAAI,CAAE3E,SAAoB,IAAI;QAC/E,OAAOA,SAAS,CAAC4D,OAAQ,CAACjE,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACyL,SAAS,IAAIzE,SAAS,CAAC/G,QAAQ,KAAK,IAAI,CAACD,qBAAqB,CAACC,QAAQ;MACrI,CAAC,CAAC;MACF,CAAC4G,WAAW,CAAC,GAAGmK,iBAAiB,GAAG,CAACA,iBAAiB,CAACrK,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;IACrE,CAAC,MAAM;MACL,CAACE,WAAW,CAAC,GAAG,CAAC,IAAI,CAACjJ,wBAAwB,EAAE+I,EAAE,CAAC;IACrD;IAEF;IACE,MAAML,MAAM,GAAsB;MAChCO,WAAW;MACXD,MAAM;MACNE,YAAY,EAAE,IAAI,CAAClI,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC;MAC3GgE,QAAQ,EAAE,IAAI,CAACrG,cAAc,CAACuS,qBAAqB,CAAC,IAAI,CAAC5P,gBAAgB,CAAC2F,KAAK,CAAC;MAChFH,YAAY,EAAE;KACf;IAEH;IACE,IAAI,IAAI,CAACrF,cAAc,EAAE;MACvB,IAAI0P,YAAY,GAAwB;QACtCzK,EAAE,EAAE,IAAI,CAACpB,SAAS,CAAC8L,QAAQ;QAC3BxK,WAAW;QACXD,MAAM,EAAE,IAAI,CAACnB,SAAS,GAAGrI,YAAY,CAAC2T,QAAQ,GAAG,IAAI,CAACxL,SAAS,CAACqB,MAAM;QACtEE,YAAY,EAAE,IAAI,CAAClI,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC;QAC3GgE,QAAQ,EAAE,IAAI,CAACrG,cAAc,CAACuS,qBAAqB,CAAC,IAAI,CAAC5P,gBAAgB,CAAC2F,KAAK,CAAC;QAChFH,YAAY,EAAE,KAAK;QACnBuK,QAAQ,EAAE,IAAI,CAAC7L,SAAS,GAAG,WAAW,GAAG;OAC1C;MACDgB,OAAO,CAACC,GAAG,CAAC0K,YAAY,CAAC;MAC3B,IAAI,CAAC7M,IAAI,CAACuD,IAAI,GAAG,IAAI,CAACpE,aAAa,CAAC6N,MAAM,CAACH,YAAY,CAAC,CAAC/I,SAAS,CAACJ,GAAG,IAAG;QACvExB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;QAChB,IAAI,CAACtE,YAAY,CAACwG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE,yBAAyB,GAAGnN,MAAM,CAACqU,SAAS,CAAC,IAAI,CAAC5S,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC8I,MAAM,CAAC,kBAAkB;SAC9K,CAAC;QAEF,IAAI,CAAChG,QAAQ,CAAC0N,IAAI,EAAE;QACpB;MACF,CAAC,EAAEC,KAAK,IAAG;QACT,IAAI,CAACjQ,aAAa,GAAG,KAAK;MAC5B,CAAC,CAAC;IACF,CAAC,MAAM;MACL,MAAMkQ,eAAe,GAAGxU,MAAM,CAACqU,SAAS,CAAC,IAAI,CAAC5S,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC8I,MAAM,CAAC,kBAAkB,CAAC;MAClK,IAAI,CAACxF,IAAI,CAACuD,IAAI,GAAG,IAAI,CAACpE,aAAa,CAACkO,MAAM,CAACtL,MAAM,CAAC,CAAC+B,SAAS,CAACJ,GAAG,IAAG;QACjExB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;QAChB,IAAI,CAACtE,YAAY,CAACwG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE1D,MAAM,KAAKxJ,YAAY,CAAC2T,QAAQ,GAAG,qBAAqB,GAAGY,eAAe,GAAG,uBAAuB,GAAGA;SAChH,CAAC;QACF,IAAI,CAAC5N,QAAQ,CAAC0N,IAAI,EAAE;MACtB,CAAC,EAAEC,KAAK,IAAG;QACT,IAAI,CAACjQ,aAAa,GAAG,KAAK;MAC5B,CAAC,CAAC;IACJ;EACF;EAEQ8F,kBAAkBA,CAAA;IACxB,IAAI,CAAChD,IAAI,CAACuD,IAAI,GAAG,IAAI,CAACpK,gBAAgB,CAACmU,oBAAoB,CAAC,IAAI,CAAC9M,IAAI,CAAC4B,EAAE,CAAC,CAACqB,IAAI,CAC5EjL,SAAS,CAAEyH,UAAe,IAAI;MAE5B;MACAA,UAAU,GAAG,IAAI,CAAC9G,gBAAgB,CAACoU,yBAAyB,CAACtN,UAAU,CAAC;MACxE,OAAO,IAAI,CAACuN,uBAAuB,CAACvN,UAAU,CAAC;MAGjD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA,IAAI,IAAI,CAACO,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;QACvC,IAAI,CAACrM,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC,CAAC,CAAC;QAC3D,IAAI,CAACyF,oBAAoB,GAAG,IAAI,CAAC1H,wBAAwB,CAACgN,OAAO,EAAEK,SAAU;MAC/E,CAAC,MACI;QACH,IAAI,CAACpG,eAAe,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC3E,qBAAqB,GAAG,IAAI,CAACa,cAAc,CAAC,CAAC,CAAC;QACnD,MAAMgG,WAAW,GAAG,IAAI,CAAChH,mBAAmB,CAAE8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACgH,SAAS,CAAC;QAC9G,IAAI,CAACiH,gCAAgC,CAACpH,WAAW,CAACtG,SAAS,CAAC;QAC5D,IAAI,CAAC+E,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAACoG,SAAS;MAC5D;MACA,OAAO,IAAI,CAACN,sBAAsB,CAAC,IAAI,CAAChG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH,CAAC0D,SAAS,CAAEJ,GAAQ,IAAI;MACvBxB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;MAChB;IACF,CAAC,CAAC;EACJ;EAEA;;;;;EAKA+J,iBAAiBA,CAACvC,IAAU;IAC1B,MAAM5C,UAAU,GAAG4C,IAAI,CAACR,YAAY,EAAE;IACtC,MAAMgD,UAAU,GAAG9U,MAAM,CAAC0P,UAAU,CAAC;IACrC,OAAOoF,UAAU,CAAClI,MAAM,CAAC,YAAY,CAAC;EACxC;EAEAmI,kBAAkBA,CAAC1I,KAAU;IAC3B/C,OAAO,CAACC,GAAG,CAAC8C,KAAK,CAAC;IAClB,IAAI,CAACrC,iBAAiB,CAACqC,KAAK,CAAC;IAC7B,IAAI,CAACjF,IAAI,CAACuH,GAAG,CAAC,IAAI,CAACnB,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAI;MAChF,IAAI,CAACnD,oBAAoB,GAAG,IAAI,CAACF,YAAY;MAC7C6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC5B,oBAAoB,CAAC;IAChE,CAAC,CAAC,CAAC;EACL;EAEF;;;;;;EAMUiN,uBAAuBA,CAACvN,UAAuB;IACrD,IAAI,IAAI,CAACiG,YAAY,EAAE,EAAE;MACvB,MAAMzD,SAAS,GAAG,IAAI,CAACmL,gBAAgB,CAAC3N,UAAU,CAAC;MACnD,IAAI,CAACwC,SAAS,EAAE;QACdP,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD,OAAO0L,SAAS;MAClB;MAEA,IAAI,CAACC,eAAe,CAACrL,SAAS,CAAC;MAC/B,IAAI,CAACsL,qBAAqB,CAACtL,SAAS,CAAC;MACrCP,OAAO,CAACC,GAAG,CAACM,SAAS,CAAC;MACtB,IAAI,CAACuL,mBAAmB,CAACvL,SAAS,CAAC4D,OAAQ,CAAC;IAE9C,CAAC,MAAM;MACL,MAAM4H,MAAM,GAAG,IAAI,CAACC,2BAA2B,CAACjO,UAAU,CAAC;MAC3DiC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE8L,MAAM,CAAC;MAC3D,IAAI,CAAC5N,YAAY,CAACkJ,IAAI,CAAC,GAAG0E,MAAM,CAAC;IACnC;IACA,IAAI,CAACE,2BAA2B,EAAE;IAClC,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,kBAAkB,EAAE;IAGzB,IAAI,IAAI,CAACzU,WAAW,CAACC,SAAS,EAAE;MAC9B,IAAI,CAAC,IAAI,CAACR,wBAAwB,EAAE;QAClC,IAAI,CAACiI,iBAAiB,GAAG,IAAI;QAC7B,OAAOjJ,EAAE,CAAC,IAAI,CAAC;MACjB;IACF;IACA,IAAI,CAACiW,8BAA8B,EAAE;IAErC,OAAO,IAAI,CAAClI,sBAAsB,CAAC,IAAI,CAAChG,iBAAiB,CAAC,CAAC,CAAC,CAAC;EAC/D;EAEF;;;;;;EAMUwN,gBAAgBA,CAAC3N,UAAuB;IAC9C,MAAMqC,WAAW,GAAG,IAAI,CAACtB,SAAS,CAACsB,WAAW;IAC9C,OAAOrC,UAAU,CAACmH,IAAI,CAAE3E,SAAoB,IAAKA,SAAS,CAACL,EAAE,KAAKE,WAAW,CAAC;EAChF;EAEF;;;;EAIUwL,eAAeA,CAACrL,SAAoB;IAC1C,MAAM1G,IAAI,GAAG,IAAI,CAACwS,mBAAmB,CAAC9L,SAAS,CAAC;IAChD,MAAM+L,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAAChM,SAAS,EAAE1G,IAAI,CAAC;IAC/D,IAAI,CAACO,cAAc,CAACiN,IAAI,CAACiF,aAAa,CAAC;EACzC;EAEF;;;;EAIUT,qBAAqBA,CAACtL,SAAoB;IAChD,IAAI,CAACnH,mBAAmB,CAACiO,IAAI,CAAC9G,SAAS,CAAC;EAC1C;EAEF;;;;EAIUuL,mBAAmBA,CAAC3H,OAAa;IACvC,IAAI,CAACjG,iBAAiB,CAACmJ,IAAI,CAAClD,OAAO,CAAC;EACtC;EAEF;;;;EAIU8H,2BAA2BA,CAAA;IACjC,IAAI,IAAI,CAAChR,cAAc,EAAE;MACvB,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC0G,IAAI,CAAC1G,QAAQ,IAAIA,QAAQ,CAACiC,KAAK,KAAK,IAAI,CAAC3B,SAAS,CAACN,QAAQ,GAAG,EAAE,CAAC;IACzG,CAAC,MAAM;MACL,IAAI,CAAC1D,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAAC;IAC1C;IACA,IAAI,IAAI,CAAC0L,qBAAqB,EAAE,EAAE;MAChC,IAAI,CAAC/S,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAACnB,kBAAkB,CAACqB,WAAW,CAAC;MAGxH,IAAI,IAAI,CAACjJ,wBAAwB,EAAE;QACjC,IAAI,CAACqQ,gCAAgC,CAAC,IAAI,CAACvQ,gBAAgB,CAACoB,uBAAuB,CAAC,IAAI,CAAClB,wBAAwB,CAACmB,QAAS,CAAC,CAAC;MAC/H;MACA,IAAI,CAAC,IAAI,CAACnB,wBAAwB,EAAE;QAClC,IAAI,CAAC+F,YAAY,CAACwG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QAEF,IAAI,CAAC2I,kCAAkC,EAAE;MAC3C,CAAC,MAAM;QACL,IAAI,CAACpO,eAAe,GAAG,IAAI,CAACjH,wBAAwB,CAACgN,OAAO;QAC5D,IAAI,CAAC5K,qBAAqB,GAAG,IAAI,CAACa,cAAc,CAAC8K,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAAC5E,SAAS,KAAK,IAAI,CAACpJ,wBAAwB,CAAC+I,EAAE,CAAC;MACtH;IACF,CAAC,MAAM;MACL,IAAI,IAAI,CAAC5B,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;QACvC,IAAI,CAACrM,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC,CAAC,CAAC;QAE3D,IAAI,CAACgF,eAAe,GAAG,IAAI,CAACE,IAAI;MAClC,CAAC,MAAM;QACL,IAAI,CAACkO,kCAAkC,EAAE;MAC3C;IACF;EACF;EAEF;;;;EAIEA,kCAAkCA,CAAA;IAChC,IAAI,IAAI,CAAClO,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;MACvC,IAAI,CAACrM,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACgF,eAAe,GAAG,IAAI,CAACE,IAAI;IAClC,CAAC,MAAM;MACL0B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC/B,iBAAiB,CAAC;MACnC,IAAI,CAACE,eAAe,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC,CAAC;MAChD,IAAI,CAAC3E,qBAAqB,GAAG,IAAI,CAACa,cAAc,CAAC,CAAC,CAAC;MACnD,MAAMmG,SAAS,GAAG,IAAI,CAACnH,mBAAmB,CAAC8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACgH,SAAS,CAAC;MAE3G,IAAIA,SAAS,EAAE;QACb,IAAG,CAAC,IAAI,CAACtF,cAAc,EAAE;UACvB+E,OAAO,CAACC,GAAG,CAACM,SAAS,CAAC;UACtB,IAAI,CAAC6E,oCAAoC,CAAC7E,SAAS,CAAC;UACpD,IAAI,CAAC9B,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACyG,MAAM,CAAEzG,QAAQ,IAAI;YACzD,OAAO,IAAI,CAACrG,cAAc,CAACuS,qBAAqB,CAAClM,QAAQ,CAACiC,KAAK,CAAC,IAAI,IAAI,CAACxJ,gBAAgB,CAACoB,uBAAuB,CAACkI,SAAS,CAACjI,QAAS,CAAC;UACxI,CAAC,CAAC;UACF;QACF;MACF;IACF;EACF;EAEF;;;;;EAKE8M,oCAAoCA,CAAC7E,SAAoB;IACvD,IAAI,IAAI,CAACtJ,gBAAgB,CAACwV,gBAAgB,CAAClM,SAAS,CAAC,EAAE;MACrD,MAAMmM,aAAa,GAAG,IAAI,CAAClO,QAAQ,CAAC,CAAC,CAAC;MACtC,IAAI,CAACC,iBAAiB,GAAG,CAACiO,aAAa,CAAC;MACxC,IAAI,CAAC5R,gBAAgB,GAAG4R,aAAa;IACvC,CAAC,MAAM;MACL,IAAI,CAACjO,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAC3C,IAAI,CAAC1D,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAAC;MACxC,MAAMmO,oBAAoB,GAAG,IAAI,CAAC1V,gBAAgB,CAACoB,uBAAuB,CAACkI,SAAS,CAACjI,QAAS,CAAC;MAC/F,IAAI,CAACkP,gCAAgC,CAACmF,oBAAoB,CAAC;IAC7D;EACF;EAEAC,qBAAqBA,CAACC,WAAgB;IACpC,MAAMlD,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;IACvC5J,OAAO,CAACC,GAAG,CAAC0J,UAAU,CAAC;IACvB;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;EACF;EAEQuC,0BAA0BA,CAAA;IAChC,MAAMvC,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;IACvC,IAAI,CAACnM,SAAS,GAAG,IAAIC,IAAI,CAACiM,UAAU,CAAC;IACrC,MAAMmD,SAAS,GAAG,IAAI,CAAChO,SAAS,CAACuB,YAAY;IAE7C,IAAI,CAAC,IAAI,CAACpF,cAAc,EAAE;MACxB;MACA;MACA;MACA;IAAA,CACD,MAAM;MACL,MAAM8R,YAAY,GAAGrW,MAAM,CAACoW,SAAS,CAAC,CAACxJ,MAAM,CAAC,OAAO,CAAC;MACtD,IAAI,CAACxH,kBAAkB,CAAC6N,UAAU,EAAEoD,YAAY,CAAC;IACnD;EAEF;EAEF;;;EAGUZ,kBAAkBA,CAAA;IACxB,MAAMxC,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;IACvC,IAAI,CAAClJ,iBAAiB,CAACiJ,UAAU,CAAC;EACpC;EAEF;;;;;;EAMUqC,2BAA2BA,CAACjO,UAAuB;IACzD,OAAO,IAAI,CAAC9G,gBAAgB,CAAC+V,yBAAyB,CAACjP,UAAU,CAAC,CAC/DkP,OAAO,CAAE1M,SAAc,IAAI;MAC1B,MAAM+L,aAAa,GAAG,IAAI,CAACY,2BAA2B,CAAC3M,SAAS,CAAC;MACjE,IAAI,CAACnG,cAAc,CAACiN,IAAI,CAACiF,aAAa,CAAC;MACvC,IAAI,CAAClT,mBAAmB,CAACiO,IAAI,CAAC9G,SAAS,CAAC;MACxC,IAAI,CAACrC,iBAAiB,CAACmJ,IAAI,CAAC9G,SAAS,CAAC4D,OAAO,CAAC;MAC9C,OAAO5D,SAAS,CAACwE,OAAO,CAAC3O,GAAG,CAAEyJ,MAAc,IAAK,IAAI,CAACsN,iBAAiB,CAACtN,MAAM,EAAEU,SAAS,CAAC,CAAC;IAC7F,CAAC,CAAC;EACN;EAEA;;;;;EAKQ2M,2BAA2BA,CAAC3M,SAAoB;IACtD,IAAI1G,IAAI,GAAG,SAAS;IACpB,IAAI0G,SAAS,CAACC,IAAI,IAAID,SAAS,CAACC,IAAI,CAAC2J,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtDtQ,IAAI,GAAG,YAAY;IACrB;IACA,IAAI0G,SAAS,CAACC,IAAI,IAAID,SAAS,CAACC,IAAI,CAAC2J,QAAQ,CAAC,MAAM,CAAC,EAAE;MACrDtQ,IAAI,GAAG,UAAU;IACnB;IACA,OAAO;MACLL,QAAQ,EAAE+G,SAAS,CAAC/G,QAAQ;MAC5BC,WAAW,EAAE8G,SAAS,CAAC4D,OAAO,IAAI5D,SAAS,CAAC4D,OAAO,CAACqB,SAAS;MAC7D3L,IAAI;MACJC,SAAS,EAAEyG,SAAS,CAACjI,QAAQ,IAAIiI,SAAS,CAACjI,QAAQ,CAACyN,MAAM,GAAG,CAAC,GAC1D,IAAI,CAAC9O,gBAAgB,CAACoB,uBAAuB,CAACkI,SAAS,CAACjI,QAAQ,CAAC,GACjEiI,SAAS,CAACzG,SAAS;MACvBkL,SAAS,EAAEzE,SAAS,CAAC4D,OAAO,IAAI5D,SAAS,CAAC4D,OAAO,CAACjE,EAAE;MACpDK,SAAS,EAAEA,SAAS,CAACL,EAAE;MACvBkN,aAAa,EAAE7M,SAAS,CAACC;KAC1B;EACH;EAEF;;;;;;;;;;;;;;;;EAgBU2M,iBAAiBA,CAACtN,MAAW,EAAEU,SAAoB;IACzD,MAAM+I,KAAK,GAAG,IAAI5L,IAAI,CAACmC,MAAM,CAACQ,YAAY,CAAC;IAC3C,MAAMoJ,GAAG,GAAG,IAAI/L,IAAI,CAAC4L,KAAK,CAAC+D,OAAO,EAAE,GAAGxN,MAAM,CAACrB,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACxE,MAAM;MAAE2F;IAAO,CAAE,GAAG5D,SAAS;IAC7B,MAAM+M,KAAK,GAAG,IAAI,CAACnV,cAAc,CAACoV,gBAAgB,CAACpJ,OAAO,CAAC;IAC3D,OAAO;MACLjE,EAAE,EAAEL,MAAM,CAACK,EAAE;MACboN,KAAK;MACLhE,KAAK,EAAEzJ,MAAM,CAACQ,YAAY,CAACmN,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,UAAU;MACpD/D,GAAG;MACHgE,MAAM,EAAE,KAAK;MACbC,eAAe,EAAE,SAAS;MAC1BC,WAAW,EAAE,SAAS;MACtBC,SAAS,EAAE,SAAS;MACpBC,aAAa,EAAE;QACbrN,IAAI,EAAE,QAAQ;QACdX,MAAM;QACNiO,SAAS,EAAEvN;OACZ;MACDwN,QAAQ,EAAE;KACX;EACH;EAEA;;;;EAIQ3B,8BAA8BA,CAAA;IACpC,IAAI,IAAI,CAAC1U,WAAW,CAACC,SAAS,EAAE;MAC9BqI,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9I,wBAAwB,CAAC;MAC1C,IAAI,CAAC,IAAI,CAACA,wBAAwB,EAAE;QAClC,IAAI,CAAC6W,gBAAgB,CAAC,MAAM,EAAE,EAAE,EAAE,qCAAqC,CAAC;MAC1E;MACA,IAAI,CAACnP,oBAAoB,GAAG,IAAI,CAACoP,mBAAmB,EAAE;IACxD,CAAC,MAAM,IAAI,IAAI,CAACvW,WAAW,CAAC8D,SAAS,IAAI,IAAI,CAAC4C,eAAe,EAAE;MAC7D,IAAI,CAACS,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAACoG,SAAS;IAC5D,CAAC,MAAM;MACL,IAAI,CAACwJ,gBAAgB,CAAC,MAAM,EAAE,EAAE,EAAE,qFAAqF,CAAC;MACxH,IAAI,CAACE,MAAM,EAAE;IACf;EACF;EAEQF,gBAAgBA,CAACrK,QAAgB,EAAEC,OAAe,EAAEC,MAAc;IACxE,IAAI,CAAC3G,YAAY,CAACwG,mBAAmB,CAAC;MACpCC,QAAQ,EAAEA,QAAQ;MAClBC,OAAO,EAAEA,OAAO;MAChBC,MAAM,EAAEA;KACT,CAAC;EACJ;EAEQoK,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAAC9W,wBAAwB,CAACgN,OAAO,EAAEK,SAAU;EAC1D;EAEQ0J,MAAMA,CAAA;IACZ,IAAI,CAAC5Q,QAAQ,CAAC0N,IAAI,EAAE;EACtB;EAEQqB,mBAAmBA,CAAC8B,YAAuB;IACjD,IAAItU,IAAI,GAAG,SAAS;IAEpB,IAAIsU,YAAY,CAAChO,MAAM,CAACgK,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzCtQ,IAAI,GAAG,YAAY;IACrB,CAAC,MAAM,IAAIsU,YAAY,CAAChO,MAAM,CAACgK,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC/CtQ,IAAI,GAAG,UAAU;IACnB;IAEA,OAAOA,IAAI;EACb;EAEQ0S,mBAAmBA,CAAC4B,YAAuB,EAAEtU,IAAY;IAC/D,MAAM;MAAEL,QAAQ;MAAE2K,OAAO;MAAEjE,EAAE;MAAEpG;IAAS,CAAE,GAAGqU,YAAY;IACzDnO,OAAO,CAACC,GAAG,CAACkO,YAAY,CAAC;IACzB,OAAO;MACL3U,QAAQ;MACRC,WAAW,EAAE0K,OAAQ,CAACqB,SAAS;MAC/B3L,IAAI;MACJC,SAAS;MACTkL,SAAS,EAAEb,OAAQ,CAACjE,EAAE;MACtBK,SAAS,EAAEL;KACZ;EACH;EAEQ0J,aAAaA,CAAA;IACnB,MAAMwE,YAAY,GAAG,sCAAsC;IAC3D,MAAMtB,SAAS,GAAG,IAAI,CAAChO,SAAS,CAACuB,YAAY;IAC7C,MAAMgO,aAAa,GAAG3X,MAAM,CAAC4X,GAAG,CAACxB,SAAS,CAAC,CAAChD,OAAO,CAAC,KAAK,CAAC,CAACyE,KAAK,EAAE,CAACjL,MAAM,CAAC8K,YAAY,CAAC;IAEvF,MAAMzE,UAAU,GAAG,IAAIjM,IAAI,CAACA,IAAI,CAACqC,KAAK,CAACsO,aAAa,CAAC,CAAC;IACtD1E,UAAU,CAAC6E,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE/B,OAAO7E,UAAU;EACnB;EAEQ3F,YAAYA,CAAA;IAClB,OAAO,OAAO,IAAI,CAAClF,SAAS,KAAK,WAAW,IAAI2P,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5P,SAAS,CAAC,CAACiH,MAAM,GAAG,CAAC;EACxF;EAEQmE,qBAAqBA,CAAA;IAC3B,OAAO,CAAC,CAAC,IAAI,CAACnL,kBAAkB,IAAI0P,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3P,kBAAkB,CAAC,CAACgH,MAAM,GAAG,CAAC;EACrF;EAEA;;;;;EAKQtC,mBAAmBA,CAACL,gBAAsB;IAChD,MAAMuL,eAAe,GAAGjY,MAAM,EAAE;IAChC,MAAMkY,sBAAsB,GAAGlY,MAAM,CAAC0M,gBAAgB,CAAC;IAEvD,OAAOwL,sBAAsB,CAACC,QAAQ,CAACF,eAAe,CAAC;EACzD;EAAC,QAAA3I,CAAA,G;qBApoCUpJ,sBAAsB,EAAA/F,EAAA,CAAAiY,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAnY,EAAA,CAAAiY,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArY,EAAA,CAAAiY,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAvY,EAAA,CAAAiY,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAzY,EAAA,CAAAiY,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAA3Y,EAAA,CAAAiY,iBAAA,CAAAW,EAAA,CAAAC,aAAA,GAAA7Y,EAAA,CAAAiY,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAA/Y,EAAA,CAAAiY,iBAAA,CAAAe,EAAA,CAAAC,YAAA,GAAAjZ,EAAA,CAAAiY,iBAAA,CAAAiB,EAAA,CAAAC,cAAA,GAAAnZ,EAAA,CAAAiY,iBAAA,CAAAiB,EAAA,CAAAE,MAAA,GAAApZ,EAAA,CAAAiY,iBAAA,CAAAjY,EAAA,CAAAqZ,iBAAA,GAAArZ,EAAA,CAAAiY,iBAAA,CAAAqB,GAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBzT,sBAAsB;IAAA0T,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;QAAtB5Z,EAAA,CAAAmC,UAAA,oBAAA2X,iDAAA;UAAA,OAAAD,GAAA,CAAAxP,kBAAA,EAAoB;QAAA,UAAArK,EAAA,CAAA+Z,eAAA,CAAE;;;;;;;;;;;;;QClDnB/Z,EAThB,CAAAQ,cAAA,mBAAqB,0BAKuE,aACzC,aACiC,aAC9B,gBACsB;QAAzBR,EAAA,CAAAmC,UAAA,mBAAA6X,wDAAA;UAAAha,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAjI,YAAA,EAAc;QAAA,EAAC;QACvD5R,EAAA,CAAAC,SAAA,aAAyD;QAC7DD,EAAA,CAAAW,YAAA,EAAS;QACTX,EAAA,CAAAQ,cAAA,gBAA4D;QAAzBR,EAAA,CAAAmC,UAAA,mBAAA+X,wDAAA;UAAAla,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAhI,YAAA,EAAc;QAAA,EAAC;QACvD7R,EAAA,CAAAC,SAAA,aAAyD;QAEjED,EADI,CAAAW,YAAA,EAAS,EACP;QAGEX,EAFR,CAAAQ,cAAA,cAA6D,eACuB,iBACC;QAAAR,EAAA,CAAAC,SAAA,eACnC;QAClCD,EAAA,CAAAoB,MAAA,IAA2D;QAAApB,EAAA,CAAAW,YAAA,EAAQ;QA2BvEX,EAzBR,CAAAS,UAAA,KAAA0Z,+CAAA,2BAAqD,KAAAC,+CAAA,2BAyBQ;QAuB7Dpa,EAAA,CAAAW,YAAA,EAAM;QAEFX,EADJ,CAAAQ,cAAA,eAAgF,iBACE;QAC1ER,EAAA,CAAAC,SAAA,eAAyC;QAACD,EAAA,CAAAoB,MAAA,iBAAQ;QAAApB,EAAA,CAAAW,YAAA,EAAQ;QAC1DX,EAAA,CAAAQ,cAAA,sBAIC;QAHDR,EAAA,CAAA2B,gBAAA,2BAAA0Y,qEAAAxY,MAAA;UAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAAja,EAAA,CAAAiC,kBAAA,CAAA4X,GAAA,CAAA5V,gBAAA,EAAApC,MAAA,MAAAgY,GAAA,CAAA5V,gBAAA,GAAApC,MAAA;UAAA,OAAA7B,EAAA,CAAAkC,WAAA,CAAAL,MAAA;QAAA,EAA8B;QAC9B7B,EAAA,CAAAmC,UAAA,sBAAAmY,gEAAAzY,MAAA;UAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAY2X,GAAA,CAAA5N,kBAAA,CAAApK,MAAA,CAA0B;QAAA,EAAC;QAW/C7B,EATS,CAAAW,YAAA,EAAa,EAShB;QAMEX,EAJR,CAAAQ,cAAA,eACgF,WACvE,eACa,+BAEyE;QADxBR,EAA1C,CAAAmC,UAAA,wBAAAoY,2EAAA1Y,MAAA;UAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAc2X,GAAA,CAAAjF,kBAAA,CAAA/S,MAAA,CAA0B;QAAA,EAAC,iCAAA2Y,oFAAA3Y,MAAA;UAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAwB2X,GAAA,CAAA9D,qBAAA,CAAAlU,MAAA,CAA6B;QAAA,EAAC;QAExH7B,EAD2F,CAAAW,YAAA,EAAsB,EAC3G;QAGFX,EADJ,CAAAQ,cAAA,eAAmD,eACG;QAC9CR,EAAA,CAAAC,SAAA,eAAkC;QAClCD,EAAA,CAAAQ,cAAA,WAAK;QAAAR,EAAA,CAAAoB,MAAA,yBAAiB;QAC1BpB,EAD0B,CAAAW,YAAA,EAAM,EAC1B;QACNX,EAAA,CAAAQ,cAAA,eAAkD;QAC9CR,EAAA,CAAAC,SAAA,eAAiC;QACjCD,EAAA,CAAAQ,cAAA,WAAK;QAAAR,EAAA,CAAAoB,MAAA,wBAAgB;QACzBpB,EADyB,CAAAW,YAAA,EAAM,EACzB;QACNX,EAAA,CAAAQ,cAAA,eAA6C;QACzCR,EAAA,CAAAC,SAAA,eAAgC;QAChCD,EAAA,CAAAQ,cAAA,WAAK;QAAAR,EAAA,CAAAoB,MAAA,uBAAe;QAG5BpB,EAH4B,CAAAW,YAAA,EAAM,EACxB,EACJ,EACA;QACNX,EAAA,CAAAS,UAAA,KAAAga,sCAAA,mBAA0B;QA4ClCza,EADI,CAAAW,YAAA,EAAM,EACJ;QAIFX,EAHJ,CAAAQ,cAAA,eAA8C,eAEK,kBACiB;QAAzBR,EAAA,CAAAmC,UAAA,mBAAAuY,yDAAA;UAAA1a,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAjI,YAAA,EAAc;QAAA,EAAC;QAC3D5R,EAAA,CAAAC,SAAA,cAAyD;QAC7DD,EAAA,CAAAW,YAAA,EAAS;QACTX,EAAA,CAAAQ,cAAA,kBAAgE;QAAzBR,EAAA,CAAAmC,UAAA,mBAAAwY,yDAAA;UAAA3a,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;UAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAhI,YAAA,EAAc;QAAA,EAAC;QAC3D7R,EAAA,CAAAC,SAAA,cAAyD;QAEjED,EADI,CAAAW,YAAA,EAAS,EACP;QASUX,EARZ,CAAAQ,cAAA,eAA2C,eACgB,eAMrB,eACoD;QAC1ER,EAAA,CAAAoB,MAAA,IACJ;QAAApB,EAAA,CAAAW,YAAA,EAAM;QAGFX,EAFJ,CAAAQ,cAAA,eAAgE,cAEvC;QACjBR,EAAA,CAAAS,UAAA,KAAAma,qCAAA,iBAAiC;QAEzC5a,EADI,CAAAW,YAAA,EAAK,EACH;QAKEX,EADJ,CAAAQ,cAAA,eAA4B,4BACwE;QAE5GR,EAAA,CAAAS,UAAA,KAAAoa,+CAAA,4BAAwC;QAaxB7a,EAAA,CAAAQ,cAAA,cAAiC;QAC7BR,EAAA,CAAAS,UAAA,KAAAqa,qCAAA,iBAAiC;QA6B7E9a,EAVwC,CAAAW,YAAA,EAAK,EACO,EACd,EACR,EACJ,EACJ,EACJ,EACJ,EACJ,EACS,EACb;;;QAtOwCX,EAAA,CAAAY,SAAA,GAA6C;QAE3FZ,EAF8C,CAAAE,UAAA,8CAA6C,2BACjE,mEACwC;QAetCF,EAAA,CAAAY,SAAA,IAA2D;QAA3DZ,EAAA,CAAAqB,kBAAA,MAAAwY,GAAA,CAAApS,IAAA,CAAAiF,IAAA,IAAAmN,GAAA,CAAA9R,SAAA,CAAA4E,OAAA,gCAA2D;QAExD3M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAApS,IAAA,CAAAiF,IAAA,IAAAmN,GAAA,CAAA9R,SAAA,CAAA4E,OAAA,CAAoC;QAyB5B3M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAApS,IAAA,CAAAiF,IAAA,IAAAmN,GAAA,CAAA9R,SAAA,CAAA4L,OAAA,CAAoC;QA2BvC3T,EAAA,CAAAY,SAAA,GAA6B;QAA7BZ,EAAA,CAAAE,UAAA,YAAA2Z,GAAA,CAAAjS,iBAAA,CAA6B;QACzC5H,EAAA,CAAAwC,gBAAA,YAAAqX,GAAA,CAAA5V,gBAAA,CAA8B;QAelCjE,EAAA,CAAAY,SAAA,EAA2E;QAA3EZ,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAqF,eAAA,KAAA0V,GAAA,GAAAlB,GAAA,CAAAxP,kBAAA,KAAAwP,GAAA,CAAAvR,MAAA,wBAA2E;QAG8CtI,EAAA,CAAAY,SAAA,GAAkC;QAChGZ,EAD8D,CAAAE,UAAA,uBAAA2Z,GAAA,CAAAnW,WAAA,CAAkC,yBAC3H,0BAA0B,WAAAmW,GAAA,CAAA7R,oBAAA,CAAgC;QAkBpFhI,EAAA,CAAAY,SAAA,IAAkB;QAAlBZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAApU,YAAA,CAAkB;QAgEZzF,EAAA,CAAAY,SAAA,IACJ;QADIZ,EAAA,CAAAqB,kBAAA,MAAAwY,GAAA,CAAApK,sBAAA,QACJ;QAI4BzP,EAAA,CAAAY,SAAA,GAAW;QAAXZ,EAAA,CAAAE,UAAA,YAAA2Z,GAAA,CAAA/S,QAAA,CAAW;QAOH9G,EAAA,CAAAY,SAAA,GAAyB;QAAzBZ,EAAA,CAAAgb,UAAA,CAAAhb,EAAA,CAAAib,eAAA,KAAAC,GAAA,EAAyB;QAElDlb,EAAA,CAAAY,SAAA,GAAuB;QAAvBZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAAtR,iBAAA,CAAuB;QAcEvI,EAAA,CAAAY,SAAA,GAAW;QAAXZ,EAAA,CAAAE,UAAA,YAAA2Z,GAAA,CAAA/S,QAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}