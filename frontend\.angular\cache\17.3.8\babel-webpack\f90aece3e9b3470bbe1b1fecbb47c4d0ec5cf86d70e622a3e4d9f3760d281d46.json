{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter, inject, Injector, signal } from '@angular/core';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { environment } from 'src/environments/environment';\nimport { SubSink } from 'subsink';\nimport { take } from 'rxjs/operators';\nimport { UntypedFormGroup, UntypedFormControl, Validators } from '@angular/forms';\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\nimport { NoteActionsService, NoteUpdateAction } from 'src/app/core/services/note-actions.service';\nimport { toObservable } from '@angular/core/rxjs-interop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/note.service\";\nimport * as i3 from \"src/app/core/services/note-listeners.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"src/app/core/services/toast.service\";\nimport * as i6 from \"src/app/core/services/notification.service\";\nimport * as i7 from \"primeng/dynamicdialog\";\nimport * as i8 from \"src/app/core/services/note-actions.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/api\";\nimport * as i11 from \"primeng/menu\";\nimport * as i12 from \"primeng/tooltip\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"primeng/editor\";\nconst _c0 = [\"menu3\"];\nconst _c1 = [\"rte\"];\nconst _c2 = [\"editor\"];\nconst _c3 = (a0, a1) => ({\n  \"pi-chevron-down\": a0,\n  \"pi-chevron-up\": a1\n});\nconst _c4 = a0 => ({\n  \"open\": a0\n});\nconst _c5 = a0 => ({\n  \"text-white\": a0\n});\nconst _c6 = () => ({\n  \"height\": \"360px\"\n});\nconst _c7 = () => ({\n  \"height\": \"150px\"\n});\nconst _c8 = a0 => ({\n  \"opened\": a0\n});\nconst _c9 = () => ({\n  \"height\": \"170px\"\n});\nfunction NoteDetailsComponent_ng_container_0_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(3, 2, ctx_r1.note.edited, \"dd-MM\"), \" \\u00A0 by \", ctx_r1.note.editedBy.firstName, \"\");\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 34);\n    i0.ɵɵelement(3, \"path\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 36);\n    i0.ɵɵtext(5, \" Pinned \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"fill\", ctx_r1.getPinFillColor());\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4, \" Favourite \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_24_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 39)(2, \"span\", 40);\n    i0.ɵɵtext(3, \"Shared with\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 41);\n    i0.ɵɵelement(5, \"i\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c5, ctx_r1.isActive));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getUsersNoteHasBeenSharedWith(ctx_r1.note), \"\");\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_24_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 39)(2, \"span\", 40);\n    i0.ɵɵtext(3, \"Shared by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 41);\n    i0.ɵɵelement(5, \"i\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c5, ctx_r1.isActive));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getNoteCreatedBy(ctx_r1.note), \"\");\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, NoteDetailsComponent_ng_container_0_div_24_ng_container_1_Template, 7, 4, \"ng-container\", 3)(2, NoteDetailsComponent_ng_container_0_div_24_ng_container_2_Template, 7, 4, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getNoteInfoClassName());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInMine);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isInMine);\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_0_div_25_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(!ctx_r1.showGroupActions ? ctx_r1.toggleMenuItems($event) : null);\n    });\n    i0.ɵɵelement(1, \"div\", 44)(2, \"div\", 44)(3, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"note-burger-\", ctx_r1.note.id, \"\");\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_26_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getNoteBurgerClassName());\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_26_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 49);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getNoteBurgerClassName());\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_0_div_26_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.checkNoteItem(ctx_r1.note));\n    });\n    i0.ɵɵtemplate(1, NoteDetailsComponent_ng_container_0_div_26_img_1_Template, 1, 1, \"img\", 46)(2, NoteDetailsComponent_ng_container_0_div_26_img_2_Template, 1, 1, \"img\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checked);\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_30_ng_template_3_Template(rf, ctx) {}\nfunction NoteDetailsComponent_ng_container_0_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"p-editor\", 51, 2);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoteDetailsComponent_ng_container_0_div_30_Template_p_editor_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.note.text, $event) || (ctx_r1.note.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onTextChange\", function NoteDetailsComponent_ng_container_0_div_30_Template_p_editor_onTextChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContentChange($event));\n    });\n    i0.ɵɵtemplate(3, NoteDetailsComponent_ng_container_0_div_30_ng_template_3_Template, 0, 0, \"ng-template\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c6));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.note.text);\n    i0.ɵɵproperty(\"readonly\", true);\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_31_ng_template_8_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_31_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NoteDetailsComponent_ng_container_0_div_31_ng_template_8_ng_container_0_Template, 1, 0, \"ng-container\", 61);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n    const quillEditorTemplate_r7 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", quillEditorTemplate_r7);\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_31_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_0_div_31_div_10_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.noteActionsService.addOrUpdateNote(true, ctx_r1.classroom));\n    });\n    i0.ɵɵtext(1, \" Save as your note \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"pTooltip\", \"This note was not created by you. If you modify it, it will be saved as new and the original will stay as is\");\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_31_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_0_div_31_div_11_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.noteActionsService.update(ctx_r1.note));\n    });\n    i0.ɵɵtext(1, \" Update \");\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 53)(2, \"div\", 54)(3, \"div\", 55);\n    i0.ɵɵtext(4, \"* Note Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoteDetailsComponent_ng_container_0_div_31_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.note.title, $event) || (ctx_r1.note.title = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p-editor\", 57, 2);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoteDetailsComponent_ng_container_0_div_31_Template_p_editor_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.note.text, $event) || (ctx_r1.note.text = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onTextChange\", function NoteDetailsComponent_ng_container_0_div_31_Template_p_editor_onTextChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContentChange($event));\n    });\n    i0.ɵɵtemplate(8, NoteDetailsComponent_ng_container_0_div_31_ng_template_8_Template, 1, 1, \"ng-template\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 58);\n    i0.ɵɵtemplate(10, NoteDetailsComponent_ng_container_0_div_31_div_10_Template, 2, 1, \"div\", 59)(11, NoteDetailsComponent_ng_container_0_div_31_div_11_Template, 3, 0, \"div\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.note.title);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(7, _c7));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.note.text);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.note.createdBy.aspUserId != ctx_r1.user.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.note.createdBy.aspUserId == ctx_r1.user.id);\n  }\n}\nfunction NoteDetailsComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_0_Template_div_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggle($event));\n    });\n    i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8)(6, \"div\", 9);\n    i0.ɵɵelement(7, \"i\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"div\", 12)(10, \"div\", 13);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 14)(13, \"div\", 15)(14, \"span\", 16);\n    i0.ɵɵelement(15, \"i\", 17);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, NoteDetailsComponent_ng_container_0_span_18_Template, 4, 5, \"span\", 18);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(19, \"div\", 19)(20, \"div\", 20)(21, \"div\", 21);\n    i0.ɵɵtemplate(22, NoteDetailsComponent_ng_container_0_div_22_Template, 6, 2, \"div\", 22)(23, NoteDetailsComponent_ng_container_0_div_23_Template, 5, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, NoteDetailsComponent_ng_container_0_div_24_Template, 3, 3, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, NoteDetailsComponent_ng_container_0_div_25_Template, 4, 2, \"div\", 24)(26, NoteDetailsComponent_ng_container_0_div_26_Template, 3, 2, \"div\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 26)(28, \"div\", 27)(29, \"div\", 28);\n    i0.ɵɵtemplate(30, NoteDetailsComponent_ng_container_0_div_30_Template, 4, 5, \"div\", 29)(31, NoteDetailsComponent_ng_container_0_div_31_Template, 12, 8, \"div\", 3);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(32, \"p-menu\", 30, 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isActive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getHeaderClassName());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(21, _c3, !ctx_r1.isActive, ctx_r1.isActive));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.note.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(17, 18, ctx_r1.note.created, \"dd-MM-yyyy\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.note.edited);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.note.pinned);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFavorited());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.note.sharedWithUsers.length > 0 && ctx_r1.withClassroom);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showGroupActions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showGroupActions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c4, ctx_r1.isActive));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"defaultRTE-\", ctx_r1.note.id, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.editNote());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.editNote());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"model\", ctx_r1.filteredMenuItems);\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_1_div_17_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closedNewNoteSelected());\n    });\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"note-burger-\", ctx_r1.note.id, \"\");\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_ng_template_27_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NoteDetailsComponent_ng_container_1_ng_template_27_ng_container_0_Template, 1, 0, \"ng-container\", 61);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const quillEditorTemplate_r7 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", quillEditorTemplate_r7);\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1, \"* Text is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"for you\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_1_div_34_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.noteActionsService.addOrUpdateNoteAndShareWithTeacher(ctx_r1.note, false, ctx_r1.classroom));\n    });\n    i0.ɵɵtext(1, \" Save & Share \");\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_1_div_35_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.noteActionsService.update(ctx_r1.note));\n    });\n    i0.ɵɵtext(1, \" Save and Share with classroom\");\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoteDetailsComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 65);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_1_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggle());\n    });\n    i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8)(6, \"div\", 9);\n    i0.ɵɵelement(7, \"i\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 66)(9, \"div\", 67)(10, \"div\", 68);\n    i0.ɵɵtext(11, \"Create new Note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 69);\n    i0.ɵɵelement(13, \"div\", 15);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(14, \"div\", 19)(15, \"div\", 20);\n    i0.ɵɵelement(16, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, NoteDetailsComponent_ng_container_1_div_17_Template, 2, 2, \"div\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 26)(19, \"div\", 27)(20, \"div\", 70)(21, \"form\", 53)(22, \"div\", 54)(23, \"div\", 55);\n    i0.ɵɵtext(24, \"* Note Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p-editor\", 72);\n    i0.ɵɵlistener(\"ngModel\", function NoteDetailsComponent_ng_container_1_Template_p_editor_ngModel_26_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.textEditor);\n    });\n    i0.ɵɵtemplate(27, NoteDetailsComponent_ng_container_1_ng_template_27_Template, 1, 1, \"ng-template\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, NoteDetailsComponent_ng_container_1_div_28_Template, 2, 0, \"div\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 58)(30, \"div\", 74);\n    i0.ɵɵlistener(\"click\", function NoteDetailsComponent_ng_container_1_Template_div_click_30_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.noteActionsService.addOrUpdateNote(false, ctx_r1.classroom));\n    });\n    i0.ɵɵtext(31, \" Save \");\n    i0.ɵɵtemplate(32, NoteDetailsComponent_ng_container_1_span_32_Template, 2, 0, \"span\", 3);\n    i0.ɵɵelement(33, \"i\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, NoteDetailsComponent_ng_container_1_div_34_Template, 3, 0, \"div\", 76)(35, NoteDetailsComponent_ng_container_1_div_35_Template, 3, 0, \"div\", 60);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isActive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c8, ctx_r1.isActive));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(16, _c3, !ctx_r1.isActive, ctx_r1.isActive));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showGroupActions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r1.isActive));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(21, _c9));\n    i0.ɵɵproperty(\"debug\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTextError);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.inClassroom && ctx_r1.role == \"Teacher\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.inClassroom && ctx_r1.role == \"Teacher\");\n  }\n}\nfunction NoteDetailsComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"span\", 80);\n    i0.ɵɵelement(2, \"select\", 81)(3, \"select\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 80);\n    i0.ɵɵelement(5, \"button\", 83)(6, \"button\", 84)(7, \"button\", 85)(8, \"button\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 80);\n    i0.ɵɵelement(10, \"select\", 87)(11, \"select\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 80);\n    i0.ɵɵelement(13, \"button\", 89)(14, \"button\", 90)(15, \"button\", 91)(16, \"button\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 80);\n    i0.ɵɵelement(18, \"button\", 93)(19, \"button\", 94)(20, \"button\", 95)(21, \"button\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 80);\n    i0.ɵɵelement(23, \"button\", 97)(24, \"select\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 80);\n    i0.ɵɵelement(26, \"button\", 99)(27, \"button\", 100)(28, \"button\", 101);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class NoteDetailsComponent {\n  constructor(generalService, noteService, noteListenersService, cd, authService, toastService, notificationService, dialogService, noteActionsService) {\n    this.generalService = generalService;\n    this.noteService = noteService;\n    this.noteListenersService = noteListenersService;\n    this.cd = cd;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.notificationService = notificationService;\n    this.dialogService = dialogService;\n    this.noteActionsService = noteActionsService;\n    this.note = {};\n    this.isInMine = false;\n    this.withClassroom = false;\n    this.type = '';\n    this.classroom = {};\n    this.showGroupActions = false;\n    this.closedNewNote = new EventEmitter();\n    this.noteChecked = new EventEmitter();\n    this.classrooms = [];\n    this.relatedClassroomId = 0;\n    this.rte = {};\n    this.subs = new SubSink();\n    this.showBurger = false;\n    this.editNote = signal(false);\n    this.checked = false;\n    this.insertImageSettings = {\n      saveUrl: environment.apiUrl + '/notesImages'\n    };\n    this.showSend = false;\n    this.showSendAndShare = false;\n    this.usersToSend = [];\n    this.user = {};\n    this.role = \"\";\n    this.isOpen = true;\n    this.injector = inject(Injector);\n    this.tools = {\n      type: 'Expand',\n      items: ['Bold', 'Italic', 'Underline', 'StrikeThrough', 'FontName', 'FontSize', 'FontColor', 'BackgroundColor', 'LowerCase', 'UpperCase', '|', 'Formats', 'Alignments', 'OrderedList', 'UnorderedList', 'Outdent', 'Indent', '|', 'Undo', 'Redo']\n    };\n    this.menuItems = [];\n    this.tryToSave = false;\n    this.form = new UntypedFormGroup({});\n    this.filteredMenuItems = [];\n    this.isActive = false;\n  }\n  toggle() {\n    this.isActive = !this.isActive;\n    this.isOpen = !this.isOpen;\n  }\n  ngOnInit() {\n    this.initcloseNewNoteSelectedListener();\n    this.initNoteUpdateListener();\n    this.form = new UntypedFormGroup({\n      title: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      text: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      })\n    });\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.user.role;\n    this.noteActionsService.setNote(this.note);\n    this.noteActionsService.setNoteForm(this.form);\n    // this.subs.sink = this.noteListenersService.showGroupActions.subscribe(res => {\n    //   this.showGroupActions = res;\n    //   alert(res);\n    //   if (!res) {\n    //     this.checked = false;\n    //   }\n    // })\n    if (this.type === 'create') {\n      this.toggle();\n    }\n    this.filteredMenuItems = this.prepareMenuItems();\n    this.handleMenuItems();\n    toObservable(this.editNote, {\n      injector: this.injector\n    }).subscribe({\n      next: data => {\n        if (data) {\n          this.initializeQuill();\n        }\n      }\n    });\n  }\n  ngOnChanges() {\n    this.getMenuItemsForItem(this.note);\n  }\n  ngAfterViewChecked() {\n    this.cd.detectChanges();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.noteListenersService.setGroupActions(false);\n  }\n  ngAfterContentInit() {}\n  ngAfterViewInit() {}\n  toggleMenuItems(event) {\n    this.menu3.show(event);\n  }\n  prepareMenuItems() {\n    // when we create a new note, we dont need a menu\n    if (this.type === 'create') {\n      return [];\n    }\n    const that = this;\n    this.menuItems = [{\n      id: \"edit\",\n      label: 'Edit',\n      icon: 'pi pi-fw pi-file-edit',\n      command: event => {\n        that.isActive = true;\n        that.editNote.set(true);\n      }\n    }, {\n      id: \"share\",\n      label: 'Share',\n      icon: 'pi pi-fw pi-share-alt',\n      command: event => {\n        if (that.authService.isStudent) {\n          that.sendOrShare({\n            action: 'shareWith',\n            map: {\n              classroom: that.classroom\n            }\n          }, true);\n        } else {\n          if (!this.generalService.isNullishObject(that.classroom) && that.classroom.classroomStudents.length === 1) {\n            const send = {\n              noteId: this.note.id,\n              classroomId: +that.classroom.id,\n              studentIds: [that.classroom.classroomStudents[0].aspUserId.toString()]\n            };\n            // Share the note with the specified classroom\n            this.noteActionsService.shareNote(send, \"Note has been shared\");\n          } else {\n            that.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n              action: 'shareWith',\n              dialogsubTitle: 'Share  with...',\n              dialogTitle: 'Share file with...',\n              preselectedClassroom: that.classroom\n            });\n          }\n        }\n      }\n    }, {\n      id: \"favourite\",\n      label: this.isFavorited() ? 'Unfavourite' : 'Favourite',\n      icon: 'pi pi-fw pi-star',\n      command: event => {\n        console.log(event);\n        this.noteActionsService.favorite(this.note, this.user, this.isFavorited());\n      }\n    }, {\n      id: \"pin\",\n      label: that.note.pinned ? 'Unpin' : 'Pin',\n      icon: 'pi pi-fw pi-map-marker',\n      command: event => {\n        that.noteActionsService.pin(that.note);\n      }\n    }, {\n      id: \"duplicate\",\n      label: 'Duplicate',\n      icon: 'pi pi-fw pi-copy',\n      command: event => {\n        console.log(event);\n        that.noteActionsService.dublicate(that.note, that.classroom);\n      }\n    }, {\n      id: \"send\",\n      label: 'Send',\n      icon: 'pi pi-fw pi-arrow-right',\n      command: event => {\n        that.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n          action: 'send',\n          dialogsubTitle: 'Send to...',\n          dialogTitle: 'Send note to ...'\n        });\n      }\n    }, {\n      id: \"send-share\",\n      label: 'Send & Share',\n      icon: 'pi pi-fw pi-arrow-right',\n      command: event => {\n        that.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n          action: 'send-and-share',\n          dialogsubTitle: 'Send & Share to...',\n          dialogTitle: 'Send & Share note to ...'\n        });\n      }\n    }, {\n      id: \"download\",\n      label: 'Download',\n      icon: 'pi pi-fw pi-cloud-download',\n      command: event => {\n        that.noteActionsService.download(that.note);\n      }\n    }, {\n      id: \"unshare\",\n      label: 'Unshare',\n      icon: 'pi pi-fw pi-share-alt',\n      command: event => {\n        let unshareWith = [];\n        let usersShared = that.note.sharedWithUsers;\n        let names = \"\";\n        if (that.authService.isStudent) {\n          if (this.isInMine) {\n            unshareWith.push(that.classroom.teacher.aspUserId);\n          } else {\n            unshareWith.push(that.user.aspUserId);\n          }\n        } else {\n          for (let user of usersShared) {\n            unshareWith.push(user.aspUserId);\n          }\n        }\n        const send = {\n          noteId: that.note.id,\n          classroomId: +that.classroom.id,\n          studentIds: unshareWith\n        };\n        that.noteActionsService.unShareNote(send, 'Note has been unshared');\n        if (that.authService.isTeacher && this.isInMine && this.note.pinned) {\n          that.noteActionsService.pin(this.note);\n        }\n      }\n    }, {\n      id: \"delete\",\n      label: 'Delete',\n      icon: 'pi pi-fw pi-trash',\n      command: event => {\n        that.noteActionsService.removeNote(that.note);\n      }\n    }];\n    return this.menuItems;\n  }\n  /**\n   * The function `getMenuItemsForItem` returns an array of menu items based on the type of note and its\n   * properties.\n   * @param {Note} item - The parameter \"item\" is of type \"Note\", which represents a note object.\n   * @returns The function `getMenuItemsForItem` returns an array of `MenuItem` objects.\n   */\n  getMenuItemsForItem(item) {\n    return this.filteredMenuItems;\n  }\n  filterMenuItems(items, idsToRemove, idsToAdd) {\n    // Filter out items to remove\n    const filteredOutItems = items.filter(item => !idsToRemove.includes(item.id));\n    // Add new items\n    this.filteredMenuItems = [...filteredOutItems, ...idsToAdd];\n    console.log(this.filteredMenuItems);\n    return this.filteredMenuItems;\n  }\n  /**\n   * The function \"getUsersNoteHasBeenSharedWith\" takes a note object as input and returns a string\n   * containing the names of users the note has been shared with.\n   * @param {Note} note - The parameter \"note\" is of type \"Note\", which represents a note object.\n   * @returns a string containing the names of the users that the note has been shared with.\n   */\n  getNoteCreatedBy(note) {\n    let usersShared = note.createdBy;\n    let names = \"\";\n    names += usersShared.firstName + \" \";\n    return names;\n  }\n  /**\n   * The function \"getUsersNoteHasBeenSharedWith\" takes a note object as input and returns a string\n   * containing the names of users the note has been shared with.\n   * @param {Note} note - The parameter \"note\" is of type \"Note\", which represents a note object.\n   * @returns a string containing the names of the users that the note has been shared with.\n   */\n  getUsersNoteHasBeenSharedWith(note) {\n    let usersShared = note.sharedWithUsers;\n    let names = \"\";\n    for (let user of usersShared) {\n      names += user.firstName + \", \";\n    }\n    names = names.substring(0, names.length - 2);\n    return names;\n  }\n  /**\n   * The function \"showSendNotification\" returns true if the note is not mine and it is shared\n   * with at least one user, otherwise it returns false.\n   * @returns a boolean value. If the condition `!this.isInMine` is true and the length of\n   * `this.note.sharedWithUsers` is greater than 0, then it returns `true`. Otherwise, it returns\n   * `false`.\n   */\n  showSendNotification() {\n    if (!this.isInMine) {\n      if (this.note.sharedWithUsers.length > 0) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * The function sends a notification to each user that a note has been shared with them.\n   */\n  sendNotification() {\n    for (let user of this.note.sharedWithUsers) {\n      let notification = {\n        title: `View note ${this.note.title} that has been shared with you`,\n        description: \"Go to your classroom notes to view the note we shared with you\",\n        createdForId: user.aspUserId,\n        type: \"note\",\n        isRead: false\n      };\n      this.subs.sink = this.notificationService.create(notification).pipe(take(1)).subscribe(res => {\n        console.log(res);\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Notification has been sent'\n        });\n      });\n    }\n  }\n  /**\n   * Determines if the note is favorited by the current user.\n   *\n   * @return {boolean} - True if the note is favorited by the current user, false otherwise.\n   */\n  isFavorited() {\n    return this.note.favouritedBy.some(user => user.aspUserId == this.authService.getUserId());\n  }\n  /**\n   * The `sendOrShare` function takes an event object as a parameter, extracts the selected users and\n   * their corresponding classroom IDs from the event, and then sends or shares a note based on the kind\n   * of event.\n   * @param {any} event - The parameter `event` is of type `any`, which means it can be any type of\n   * object.\n   */\n  sendOrShare(event, isStudent = false) {\n    this.noteActionsService.sendOrShare(this.note, event, isStudent);\n  }\n  close(event) {\n    this.showSend = false;\n    this.showSendAndShare = false;\n  }\n  ifFieldValid(field) {\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n  }\n  isTextValid() {\n    // this.showTextError =  true\n  }\n  closedNewNoteSelected() {\n    this.closedNewNote.emit({});\n  }\n  /**\n   * The function returns the fill color for a pin icon based on whether it is favorited or not.\n   * @returns The color being returned depends on whether the item is favorited or not. If the item is\n   * favorited, the color returned is '#FFFFFF' (white). If the item is not favorited, the color returned\n   * is '#6677D1' (a shade of blue).\n   */\n  getPinFillColor() {\n    return this.isFavorited() || this.note.pinned ? '#FFFFFF' : '#6677D1';\n  }\n  getHeaderClassName() {\n    return {\n      'opened2 activated': this.isActive,\n      'opened': this.isFavorited(),\n      'text-white': this.note.pinned,\n      'isPinned': this.note.pinned,\n      'isPinnedAndFavourite': this.isFavorited() && this.note.pinned\n    };\n  }\n  getNoteInfoClassName() {\n    return {\n      'text-white': this.note.pinned || this.isFavorited()\n    };\n  }\n  /**\n   * Gets the CSS class name for the note burger icon.\n   * Returns 'filter-white' if the note is active, favorited, or pinned.\n   * Otherwise returns an empty string.\n   */\n  getNoteBurgerClassName() {\n    return this.isActive || this.isFavorited() || this.note.pinned ? \"filter-white\" : \"\";\n  }\n  checkNoteItem(el) {\n    this.checked = !this.checked;\n    if (this.checked) {\n      // this.noteService.checkedLibraries.push(this.classroomFile);\n      this.noteChecked.emit({\n        checked: true,\n        note: this.note\n      });\n    } else {\n      this.noteChecked.emit({\n        checked: false,\n        note: this.note\n      });\n    }\n  }\n  openDialogWithComponent(component, dialogData, width = 360) {\n    this.generalService.openDialogWithComponent(this.dialogService, component, 400, dialogData, null, result => {\n      console.log(result);\n      if (result && result.action === 'shareWith') {\n        if (!this.withClassroom && this.authService.isTeacher) {\n          this.noteActionsService.dublicateAndShare(this.note, result);\n        } else {\n          this.sendOrShare(result);\n        }\n      } else if (result && result.action === 'send') {\n        this.sendOrShare(result);\n      } else if (result && result.action === 'send-and-share') {\n        this.sendOrShare(result);\n      }\n    });\n  }\n  initcloseNewNoteSelectedListener() {\n    this.subs.add(this.noteActionsService.closeExpandedNoteListener.subscribe(shouldClose => {\n      if (shouldClose) {\n        this.closedNewNote.emit({});\n        this.noteActionsService.setcloseNewNoteSelected(false);\n      }\n    }));\n  }\n  initNoteUpdateListener() {\n    this.subs.add(this.noteActionsService.updateNote.subscribe(noteUpdateSource => {\n      if (!noteUpdateSource) {\n        return;\n      }\n      if (!this.generalService.isNullishObject(noteUpdateSource)) {\n        console.log(noteUpdateSource);\n        this.processNoteUpdate(noteUpdateSource);\n      }\n    }));\n  }\n  processNoteUpdate(note) {\n    this.note = note.note;\n    switch (note.action) {\n      case NoteUpdateAction.UPDATE:\n        this.editNote.set(false);\n        break;\n      case NoteUpdateAction.PIN:\n        break;\n      case NoteUpdateAction.FAVOURITE:\n        if (this.isFavorited()) {\n          let index = this.note.favouritedBy.findIndex(el => el.id === this.authService.getUserId());\n          this.note.favouritedBy.splice(index, 1);\n        } else {\n          let userToAdd = this.authService.getLoggedInUser();\n          userToAdd.aspUserId = userToAdd.id;\n          this.note.favouritedBy.push(userToAdd);\n        }\n        break;\n    }\n    this.getMenuItemsForItem(this.note);\n    this.filteredMenuItems = this.prepareMenuItems();\n    this.handleMenuItems();\n    this.noteService.setUpdateNoteListener(true);\n  }\n  /**\n   * Handles filtering of menu items based on user role and context.\n   *\n   * Checks if currently viewing a classroom note.\n   * If so, applies filtering:\n   * - For students, removes share, send, delete, pin, duplicate, favourite menu items\n   * - For teachers, removes delete, pin, favourite menu items\n   *\n   * This restricts available actions on classroom notes based on role.\n   */\n  handleMenuItems() {\n    if (!this.classroom || !this.note) {\n      return;\n    }\n    const removedMenuItems = this.noteService.buildMenuOptionsForRemoval(this.note, this.view, this.classroom);\n    const hasClasroom = !this.generalService.isNullishObject(this.classroom);\n    const filterItems = this.filterMenuItems(this.prepareMenuItems(), removedMenuItems, []);\n  }\n  initializeQuill() {\n    if (!this.editor) {\n      return;\n    }\n    const quill = this.editor.getQuill(); // Get the Quill instance\n    // Override the default image handler\n    quill.getModule('toolbar').addHandler('image', this.imageHandler.bind(this));\n  }\n  imageHandler() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const input = document.createElement('input');\n      input.setAttribute('type', 'file');\n      input.setAttribute('accept', 'image/*');\n      input.click();\n      input.onchange = /*#__PURE__*/_asyncToGenerator(function* () {\n        const file = input.files?.[0];\n        if (file) {\n          // Check file size (5 MB limit)\n          if (file.size > 5 * 1024 * 1024) {\n            // 5 MB in bytes\n            alert('The image exceeds the 5 MB limit. Please choose a smaller image.');\n            return; // Drop the upload\n          }\n          try {\n            const reader = new FileReader();\n            reader.onload = e => {\n              const imageUrl = e.target?.result;\n              const range = _this.editor.getQuill().getSelection(true);\n              _this.editor.getQuill().insertEmbed(range.index, 'image', imageUrl, 'user');\n            };\n            reader.readAsDataURL(file);\n          } catch (error) {\n            console.error('Error reading image:', error);\n          }\n        }\n      });\n    })();\n  }\n  pasteHandler(event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const items = event.clipboardData?.items;\n      if (items) {\n        for (let i = 0; i < items.length; i++) {\n          const item = items[i];\n          if (item.type.indexOf('image') !== -1) {\n            event.preventDefault(); // Prevent the default paste behavior\n            const file = item.getAsFile();\n            if (file) {\n              // Check file size (5 MB limit)\n              if (file.size > 5 * 1024 * 1024) {\n                // 5 MB in bytes\n                alert('The pasted image exceeds the 5 MB limit. Please use a smaller image.');\n                return; // Drop the upload\n              }\n              try {\n                const reader = new FileReader();\n                reader.onload = e => {\n                  const imageUrl = e.target?.result;\n                  const range = _this2.editor.getQuill().getSelection(true);\n                  _this2.editor.getQuill().insertEmbed(range.index, 'image', imageUrl, 'user');\n                };\n                reader.readAsDataURL(file);\n              } catch (error) {\n                console.error('Error reading pasted image:', error);\n              }\n            }\n          }\n        }\n      }\n    })();\n  }\n  onContentChange(content) {\n    console.log('Original content:', content);\n    // Parse the HTML content\n    const parser = new DOMParser();\n    const doc = parser.parseFromString(content.htmlValue, 'text/html');\n    // Find all <img> tags in the content\n    const images = doc.querySelectorAll('img');\n    let contentUpdated = false;\n    images.forEach(img => {\n      const src = img.getAttribute('src');\n      // Check if the image is a data URL (base64)\n      if (src && src.startsWith('data:image')) {\n        // Calculate the size of the base64 image\n        const base64Length = src.length - (src.indexOf(',') + 1);\n        const padding = src.charAt(src.length - 2) === '=' ? 2 : src.charAt(src.length - 1) === '=' ? 1 : 0;\n        const fileSize = base64Length * 0.75 - padding; // Size in bytes\n        // Check if the image exceeds the size limit (5 MB)\n        if (fileSize > 5 * 1024 * 1024) {\n          // 5 MB in bytes\n          // Remove the image from the content\n          img.remove();\n          contentUpdated = true;\n        }\n      }\n    });\n    // If any images were removed, update the content's htmlValue\n    if (contentUpdated) {\n      const updatedHtml = doc.body.innerHTML;\n      console.log('Updated content:', updatedHtml);\n      // Update the htmlValue property\n      content.htmlValue = updatedHtml;\n      // Notify the user\n      alert('One or more images exceeded the 5 MB limit and were removed.');\n    }\n  }\n  static #_ = this.ɵfac = function NoteDetailsComponent_Factory(t) {\n    return new (t || NoteDetailsComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.NoteService), i0.ɵɵdirectiveInject(i3.NoteListenersService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.ToastService), i0.ɵɵdirectiveInject(i6.NotificationService), i0.ɵɵdirectiveInject(i7.DialogService), i0.ɵɵdirectiveInject(i8.NoteActionsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NoteDetailsComponent,\n    selectors: [[\"app-note-details\"]],\n    viewQuery: function NoteDetailsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menu3 = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rte = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editor = _t.first);\n      }\n    },\n    inputs: {\n      note: \"note\",\n      isInMine: \"isInMine\",\n      withClassroom: \"withClassroom\",\n      type: \"type\",\n      view: \"view\",\n      classroom: \"classroom\",\n      showGroupActions: \"showGroupActions\"\n    },\n    outputs: {\n      closedNewNote: \"closedNewNote\",\n      noteChecked: \"noteChecked\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService, NoteActionsService]), i0.ɵɵNgOnChangesFeature],\n    decls: 4,\n    vars: 2,\n    consts: [[\"quillEditorTemplate\", \"\"], [\"menu3\", \"\"], [\"editor\", \"\"], [4, \"ngIf\"], [1, \"accordion\", \"border-round-3xl\", \"note\"], [1, \"header\", \"w-full\", 3, \"ngClass\"], [1, \"flex\", \"w-full\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\"], [1, \"circle\", \"ml-1\"], [1, \"down-arrow\", \"pi\", 3, \"ngClass\"], [1, \"package-header\", \"w-100\", \"justify-content-between\", \"align-items-center\", \"font-xs\"], [1, \"note-title\", \"file-path-text\"], [1, \"note-title-text\", \"file-path-text\"], [1, \"note-dates\", \"hidden\", \"sm:block\"], [1, \"flex\", \"align-items-center\", \"font-2xs\", \"gap-2\"], [1, \"font-2xs\"], [1, \"pi\", \"pi-file-edit\", \"font-xs\"], [\"class\", \"font-2xs\", 4, \"ngIf\"], [1, \"ml-auto\", \"flex\", \"align-items-center\"], [1, \"note-info\"], [1, \"note-info-top\"], [\"class\", \"note-info-element\", 4, \"ngIf\"], [\"class\", \"note-info-bottom\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"note-burger hvr-grow\", \"style\", \"position: relative;\", 3, \"id\", \"click\", 4, \"ngIf\"], [\"style\", \"position: relative;\", \"class\", \"flex align-items-center\", 3, \"click\", 4, \"ngIf\"], [1, \"accordion-content\", \"accordion-content-transition\", 3, \"ngClass\"], [1, \"note-content\"], [1, \"note-text\", \"p-2\", 3, \"id\"], [\"class\", \" plain\", 4, \"ngIf\"], [\"popup\", \"popup\", \"appendTo\", \"body\", 3, \"model\"], [1, \"pi\", \"pi-pencil\", \"font-xs\"], [1, \"note-info-element\"], [1, \"note-info-icon\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"11.174\", \"height\", \"14.519\", \"viewBox\", \"0 0 8.174 11.519\"], [\"id\", \"pin-18_Converted_-01\", \"data-name\", \"pin-18 [Converted]-01\", \"d\", \"M4.092,0H4.086A4.088,4.088,0,0,0,.506,6.054l3.583,5.466L7.672,6.054A4.087,4.087,0,0,0,4.092,0Z\", \"transform\", \"translate(-0.002)\"], [1, \"note-info-text\", \"hidden\", \"sm:block\"], [1, \"pi\", \"pi-star-fill\", \"font-sm\"], [1, \"note-info-bottom\", 3, \"ngClass\"], [1, \"line-height-1\", \"pt-1\", \"note-info-width\", \"flex\", \"justify-content-end\", \"gap-1\", 3, \"ngClass\"], [1, \"hidden\", \"sm:block\"], [1, \"block\", \"sm:hidden\"], [1, \"pi\", \"pi-share-alt\", \"font-2xs\"], [1, \"note-burger\", \"hvr-grow\", 2, \"position\", \"relative\", 3, \"click\", \"id\"], [1, \"inner-circle\"], [1, \"flex\", \"align-items-center\", 2, \"position\", \"relative\", 3, \"click\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", \"class\", \"pointer\", \"style\", \"width:2rem; height:2rem\", 3, \"ngClass\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", \"class\", \"pointer\", \"style\", \"width:2rem; height:2rem\", 3, \"ngClass\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", 1, \"pointer\", 2, \"width\", \"2rem\", \"height\", \"2rem\", 3, \"ngClass\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", 1, \"pointer\", 2, \"width\", \"2rem\", \"height\", \"2rem\", 3, \"ngClass\"], [1, \"plain\"], [3, \"ngModelChange\", \"onTextChange\", \"ngModel\", \"readonly\"], [\"pTemplate\", \"header\"], [3, \"formGroup\"], [1, \"input-field\"], [1, \"input-element-title\"], [\"formControlName\", \"title\", \"type\", \"text\", 1, \"input-element\", 3, \"ngModelChange\", \"ngModel\"], [\"formControlName\", \"text\", 3, \"ngModelChange\", \"onTextChange\", \"ngModel\"], [1, \"btns\", \"justify-content-center\"], [\"class\", \"light-purple-button general-rounded-btn blue-grad m-t-15\", \"style\", \"width:300px\", \"title\", \" \", 3, \"pTooltip\", \"click\", 4, \"ngIf\"], [\"class\", \"light-purple-button general-rounded-btn blue-grad m-t-15\", \"style\", \"width:200px\", 3, \"click\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"title\", \" \", 1, \"light-purple-button\", \"general-rounded-btn\", \"blue-grad\", \"m-t-15\", 2, \"width\", \"300px\", 3, \"click\", \"pTooltip\"], [1, \"light-purple-button\", \"general-rounded-btn\", \"blue-grad\", \"m-t-15\", 2, \"width\", \"200px\", 3, \"click\"], [1, \"pi\", \"pi-refresh\"], [1, \"\", 3, \"click\"], [1, \"package-header\", \"w-100\", \"justify-content-between\", \"align-items-center\", \"font-xs\", \"font-bold\"], [1, \"note-title\"], [1, \"note-title-text\"], [1, \"note-dates\"], [1, \"note-text\", \"p-2\"], [\"formControlName\", \"title\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"text\", 3, \"ngModel\", \"debug\"], [\"class\", \"input-error\", 4, \"ngIf\"], [1, \"light-purple-button\", \"general-rounded-btn\", \"blue-grad\", \"m-t-15\", \"m-1\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\", \"btn-action\", 3, \"click\"], [1, \"pi\", \"pi-save\"], [\"class\", \"light-purple-button general-rounded-btn blue-grad m-t-15  m-1 flex align-items-center justify-content-center gap-2 btn-action\", 3, \"click\", 4, \"ngIf\"], [1, \"pi\", \"pi-times\"], [1, \"input-error\"], [\"id\", \"toolbar-container\"], [1, \"ql-formats\"], [1, \"ql-font\"], [1, \"ql-size\"], [\"title\", \"bold\", \"arial-label\", \"bold\", 1, \"ql-bold\"], [\"title\", \"italic\", \"arial-label\", \"italic\", 1, \"ql-italic\"], [\"title\", \"underline\", \"arial-label\", \"underline\", 1, \"ql-underline\"], [\"title\", \"strike\", \"arial-label\", \"strike\", 1, \"ql-strike\"], [\"title\", \"color\", \"arial-label\", \"color\", 1, \"ql-color\"], [\"title\", \"background\", \"arial-label\", \"background\", 1, \"ql-background\"], [\"value\", \"1\", \"title\", \"header 1\", \"arial-label\", \"header 1\", 1, \"ql-header\"], [\"value\", \"2\", \"title\", \"header 2\", \"arial-label\", \"header 2\", 1, \"ql-header\"], [\"title\", \"blockquote\", \"arial-label\", \"blockquote\", 1, \"ql-blockquote\"], [\"title\", \"code block\", \"arial-label\", \"code block\", 1, \"ql-code-block\"], [\"value\", \"ordered\", \"title\", \"ordered list\", \"arial-label\", \"ordered list\", 1, \"ql-list\"], [\"value\", \"bullet\", \"title\", \"unordered list\", \"arial-label\", \"unordered list\", 1, \"ql-list\"], [\"value\", \"-1\", \"title\", \"indent\", \"arial-label\", \"indent\", 1, \"ql-indent\"], [\"value\", \"+1\", \"title\", \"indent\", \"arial-label\", \"indent\", 1, \"ql-indent\"], [\"value\", \"rtl\", \"title\", \"direction\", \"arial-label\", \"direction\", 1, \"ql-direction\"], [\"title\", \"align\", \"arial-label\", \"align\", 1, \"ql-align\"], [\"title\", \"link\", \"arial-label\", \"link\", 1, \"ql-link\"], [\"title\", \"image\", \"arial-label\", \"image\", 1, \"ql-image\"], [\"title\", \"Video\", \"arial-label\", \"video\", 1, \"ql-video\"]],\n    template: function NoteDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NoteDetailsComponent_ng_container_0_Template, 34, 26, \"ng-container\", 3)(1, NoteDetailsComponent_ng_container_1_Template, 36, 22, \"ng-container\", 3)(2, NoteDetailsComponent_ng_template_2_Template, 29, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.type !== \"create\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.type === \"create\");\n      }\n    },\n    dependencies: [i9.NgClass, i9.NgIf, i9.NgTemplateOutlet, i10.PrimeTemplate, i11.Menu, i12.Tooltip, i13.ɵNgNoValidate, i13.DefaultValueAccessor, i13.NgControlStatus, i13.NgControlStatusGroup, i13.NgModel, i13.FormGroupDirective, i13.FormControlName, i14.Editor, i9.DatePipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.note[_ngcontent-%COMP%] {\\n  border-radius: 40px;\\n  border: 1px solid #eaf0f5;\\n  margin-top: 2px;\\n  position: relative;\\n}\\n.note[_ngcontent-%COMP%]::before {\\n  pointer-events: none;\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 30px;\\n  padding: 2px;\\n}\\n.note.isFavNote[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(221, 90%, 59%) 0%, hsl(241, 76%, 67%) 29%, hsl(261, 66%, 64%) 53%, hsl(279, 56%, 60%) 68%, hsl(296, 48%, 56%) 77%, hsl(311, 53%, 56%) 84%, hsl(321, 62%, 57%) 89%, hsl(330, 67%, 59%) 93%, hsl(337, 71%, 60%) 97%, hsl(344, 72%, 61%) 100%);\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  color: var(--main-color);\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  width: 40px;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    margin-left: 20px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n    margin-left: 0px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.note-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n    justify-content: end;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 10px;\\n  margin-left: auto;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: end;\\n  text-align: right;\\n  margin-right: 10px;\\n  font-size: 0.725rem;\\n  color: #2d3b8e;\\n}\\n\\n.note-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n.rte[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  border-radius: 20px;\\n  z-index: 0 !important;\\n}\\n\\n.note-text-content[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n}\\n\\n.update-button[_ngcontent-%COMP%] {\\n  padding: 5px 30px;\\n  background-color: var(--light-purple);\\n  text-align: center;\\n  border-radius: 10px;\\n  color: white;\\n  margin-top: 15px;\\n  cursor: pointer;\\n}\\n\\n.update-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--main-color);\\n}\\n\\n.section-arrow[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n}\\n\\n.note-burger[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 50%;\\n  min-width: 36px;\\n  min-height: 36px;\\n  border: 2px solid #6563ec;\\n  display: flex;\\n  align-items: center;\\n  box-sizing: border-box;\\n  justify-content: center;\\n  cursor: pointer;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: #6563ec;\\n  margin: 2px;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%] {\\n  width: 34px;\\n  height: 34px;\\n  border-radius: 50%;\\n  background-color: transparent;\\n}\\n\\n.note-menu[_ngcontent-%COMP%] {\\n  width: 300px;\\n  position: absolute;\\n  right: 30px;\\n  top: 69px;\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid var(--main-color);\\n  box-sizing: border-box;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  background-color: white;\\n  z-index: 1;\\n  display: none;\\n  flex-direction: column;\\n  padding: 10px;\\n}\\n@media screen and (max-width: 1124px) {\\n  .note-menu[_ngcontent-%COMP%] {\\n    right: 30px;\\n  }\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n  align-items: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  cursor: pointer;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n[_nghost-%COMP%]     .e-toolbar-items {\\n  border-radius: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-toolbar {\\n  border-radius: 20px 20px 0px 0px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-content {\\n  border-radius: 0 0 20px 20px !important;\\n  border-top: 0px !important;\\n}\\n\\n.plain[_ngcontent-%COMP%] {\\n  border-radius: 0px;\\n  margin: -13px 2px 0 2px;\\n  border-top: 0;\\n  border-bottom-right-radius: 12px;\\n  border-bottom-left-radius: 12px;\\n  position: relative;\\n}\\n\\n.filter-white[_ngcontent-%COMP%] {\\n  filter: invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(200%) contrast(101%);\\n}\\n\\n.note-info-width[_ngcontent-%COMP%] {\\n  min-width: 10rem;\\n}\\n\\n.note-title-text[_ngcontent-%COMP%] {\\n  font-weight: normal;\\n  font-size: 1rem;\\n  letter-spacing: 0.01em;\\n  text-align: left;\\n  max-width: 17rem;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-title-text[_ngcontent-%COMP%] {\\n    max-width: 9rem;\\n    font-size: 0.825rem;\\n  }\\n}\\n\\n.accordion[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  margin-top: 10px;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px;\\n  cursor: pointer;\\n  border-radius: 31px;\\n  box-shadow: 3px 3px 6px 2px rgba(0, 0, 0, 0.16);\\n  z-index: 4;\\n  position: relative;\\n  border: 1px solid transparent;\\n}\\n.header.isPinned[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(232, 49%, 54%) 0%, hsl(232, 49%, 54%) 13%, hsl(232, 49%, 54%) 25%, hsl(232, 49%, 54%) 37%, hsl(232, 49%, 54%) 50%, hsl(232, 49%, 54%) 63%, hsl(232, 49%, 54%) 75%, hsl(232, 49%, 54%) 87%, hsl(232, 49%, 54%) 100%);\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(231, 46%, 48%) 0%, hsl(231, 46%, 51%) 11%, hsl(232, 49%, 54%) 22%, hsl(232, 53%, 57%) 33%, hsl(232, 57%, 60%) 44%, hsl(233, 62%, 63%) 56%, hsl(233, 67%, 65%) 67%, hsl(233, 74%, 68%) 78%, hsl(233, 82%, 71%) 89%, hsl(233, 92%, 74%) 100%);\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%] {\\n  border: 1px solid transparent;\\n  color: white;\\n  background-color: #7f8dfa;\\n}\\n.header.opened.activated[_ngcontent-%COMP%], .header.opened2.activated[_ngcontent-%COMP%] {\\n  background-color: #7f8dfa;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n  background: transparent;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.down-arrow[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.down-arrow.pi-chevron-up[_ngcontent-%COMP%] {\\n  transform: rotate(360deg);\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(180deg, hsl(249, 92%, 74%) 0%, hsl(248, 90%, 73%) 11%, hsl(247, 88%, 72%) 22%, hsl(246, 86%, 71%) 33%, hsl(245, 84%, 70%) 44%, hsl(244, 82%, 68%) 56%, hsl(242, 80%, 67%) 67%, hsl(241, 79%, 66%) 78%, hsl(240, 77%, 65%) 89%, hsl(238, 76%, 63%) 100%);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n\\n.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.accordion-content[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n}\\n\\n.open[_ngcontent-%COMP%] {\\n  background-color: white;\\n  margin-top: -20px;\\n  border-bottom-left-radius: 30px;\\n  border-bottom-right-radius: 30px;\\n  height: 370px;\\n  max-height: 370px;\\n  padding: 20px 3px 3px 3px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .open[_ngcontent-%COMP%] {\\n    height: 410px;\\n    max-height: 410px;\\n  }\\n}\\n\\n.btn-action[_ngcontent-%COMP%] {\\n  width: 200px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .btn-action[_ngcontent-%COMP%] {\\n    width: auto;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .p-editor-container .p-editor-toolbar.ql-snow {\\n  border: none;\\n  padding: 0 !important;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor {\\n  background: transparent;\\n  padding: 4px;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor p {\\n  font-size: 1rem;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor[contenteditable=true] {\\n  border: 1px solid var(--my-gray);\\n  border-radius: 10px;\\n}\\n[_nghost-%COMP%]     .circle.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n  transform: translate(80%, -50%);\\n}\\n@media only screen and (max-width: 768px) {\\n  [_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n    transform: translate(45%, -50%);\\n  }\\n}\\n\\n  .p-menu .p-menuitem-link {\\n  padding: 0.5rem 1rem !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "inject", "Injector", "signal", "DialogService", "environment", "SubSink", "take", "UntypedFormGroup", "UntypedFormControl", "Validators", "LibraryFileActionsDialogComponent", "NoteActionsService", "NoteUpdateAction", "toObservable", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "ctx_r1", "note", "edited", "editedBy", "firstName", "ɵɵstyleProp", "getPinFillColor", "ɵɵelementContainerStart", "ɵɵproperty", "ɵɵpureFunction1", "_c5", "isActive", "ɵɵtextInterpolate1", "getUsersNoteHasBeenSharedWith", "getNoteCreatedBy", "ɵɵtemplate", "NoteDetailsComponent_ng_container_0_div_24_ng_container_1_Template", "NoteDetailsComponent_ng_container_0_div_24_ng_container_2_Template", "getNoteInfoClassName", "isInMine", "ɵɵlistener", "NoteDetailsComponent_ng_container_0_div_25_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "showGroupActions", "toggleMenuItems", "ɵɵpropertyInterpolate1", "id", "getNoteBurgerClassName", "NoteDetailsComponent_ng_container_0_div_26_Template_div_click_0_listener", "_r4", "checkNoteItem", "NoteDetailsComponent_ng_container_0_div_26_img_1_Template", "NoteDetailsComponent_ng_container_0_div_26_img_2_Template", "checked", "ɵɵtwoWayListener", "NoteDetailsComponent_ng_container_0_div_30_Template_p_editor_ngModelChange_1_listener", "_r5", "ɵɵtwoWayBindingSet", "text", "NoteDetailsComponent_ng_container_0_div_30_Template_p_editor_onTextChange_1_listener", "onContentChange", "NoteDetailsComponent_ng_container_0_div_30_ng_template_3_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c6", "ɵɵtwoWayProperty", "ɵɵelementContainer", "NoteDetailsComponent_ng_container_0_div_31_ng_template_8_ng_container_0_Template", "quillEditorTemplate_r7", "NoteDetailsComponent_ng_container_0_div_31_div_10_Template_div_click_0_listener", "_r8", "noteActionsService", "addOrUpdateNote", "classroom", "NoteDetailsComponent_ng_container_0_div_31_div_11_Template_div_click_0_listener", "_r9", "update", "NoteDetailsComponent_ng_container_0_div_31_Template_input_ngModelChange_5_listener", "_r6", "title", "NoteDetailsComponent_ng_container_0_div_31_Template_p_editor_ngModelChange_6_listener", "NoteDetailsComponent_ng_container_0_div_31_Template_p_editor_onTextChange_6_listener", "NoteDetailsComponent_ng_container_0_div_31_ng_template_8_Template", "NoteDetailsComponent_ng_container_0_div_31_div_10_Template", "NoteDetailsComponent_ng_container_0_div_31_div_11_Template", "form", "_c7", "created<PERSON>y", "aspUserId", "user", "NoteDetailsComponent_ng_container_0_Template_div_click_3_listener", "_r1", "toggle", "NoteDetailsComponent_ng_container_0_span_18_Template", "NoteDetailsComponent_ng_container_0_div_22_Template", "NoteDetailsComponent_ng_container_0_div_23_Template", "NoteDetailsComponent_ng_container_0_div_24_Template", "NoteDetailsComponent_ng_container_0_div_25_Template", "NoteDetailsComponent_ng_container_0_div_26_Template", "NoteDetailsComponent_ng_container_0_div_30_Template", "NoteDetailsComponent_ng_container_0_div_31_Template", "ɵɵclassProp", "getHeaderClassName", "ɵɵpureFunction2", "_c3", "ɵɵtextInterpolate", "created", "pinned", "isFavorited", "sharedWithUsers", "length", "withClassroom", "_c4", "editNote", "filteredMenuItems", "NoteDetailsComponent_ng_container_1_div_17_Template_div_click_0_listener", "_r11", "closedNewNoteSelected", "NoteDetailsComponent_ng_container_1_ng_template_27_ng_container_0_Template", "NoteDetailsComponent_ng_container_1_div_34_Template_div_click_0_listener", "_r12", "addOrUpdateNoteAndShareWithTeacher", "NoteDetailsComponent_ng_container_1_div_35_Template_div_click_0_listener", "_r13", "NoteDetailsComponent_ng_container_1_Template_div_click_3_listener", "_r10", "NoteDetailsComponent_ng_container_1_div_17_Template", "NoteDetailsComponent_ng_container_1_Template_p_editor_ngModel_26_listener", "textEditor", "NoteDetailsComponent_ng_container_1_ng_template_27_Template", "NoteDetailsComponent_ng_container_1_div_28_Template", "NoteDetailsComponent_ng_container_1_Template_div_click_30_listener", "NoteDetailsComponent_ng_container_1_span_32_Template", "NoteDetailsComponent_ng_container_1_div_34_Template", "NoteDetailsComponent_ng_container_1_div_35_Template", "_c8", "_c9", "showTextError", "inClassroom", "role", "authService", "isStudent", "NoteDetailsComponent", "constructor", "generalService", "noteService", "noteListenersService", "cd", "toastService", "notificationService", "dialogService", "type", "closedNewNote", "noteChecked", "classrooms", "relatedClassroomId", "rte", "subs", "showBurger", "insertImageSettings", "saveUrl", "apiUrl", "showSend", "showSendAndShare", "usersToSend", "isOpen", "injector", "tools", "items", "menuItems", "tryToSave", "ngOnInit", "initcloseNewNoteSelectedListener", "initNoteUpdateListener", "validators", "required", "getLoggedInUser", "setNote", "setNoteForm", "prepareMenuItems", "handleMenuItems", "subscribe", "next", "data", "initializeQuill", "ngOnChanges", "getMenuItemsForItem", "ngAfterViewChecked", "detectChanges", "ngOnDestroy", "unsubscribe", "setGroupActions", "ngAfterContentInit", "ngAfterViewInit", "event", "menu3", "show", "that", "label", "icon", "command", "set", "sendOrShare", "action", "map", "isNullishObject", "classroomStudents", "send", "noteId", "classroomId", "studentIds", "toString", "shareNote", "openDialogWithComponent", "dialogsubTitle", "dialogTitle", "preselectedClassroom", "console", "log", "favorite", "pin", "dublicate", "download", "unshareWith", "usersShared", "names", "push", "teacher", "unShareNote", "<PERSON><PERSON><PERSON>er", "removeNote", "item", "filterMenuItems", "idsToRemove", "idsToAdd", "filteredOutItems", "filter", "includes", "substring", "showSendNotification", "sendNotification", "notification", "description", "createdForId", "isRead", "sink", "create", "pipe", "res", "setShowToastmessage", "severity", "summary", "detail", "favourited<PERSON>y", "some", "getUserId", "close", "ifFieldValid", "field", "get", "invalid", "touched", "isTextValid", "emit", "el", "component", "dialogData", "width", "result", "dublicateAndShare", "add", "closeExpandedNoteListener", "shouldClose", "setcloseNewNoteSelected", "updateNote", "noteUpdateSource", "processNoteUpdate", "UPDATE", "PIN", "FAVOURITE", "index", "findIndex", "splice", "userToAdd", "setUpdateNoteListener", "removedMenuItems", "buildMenuOptionsForRemoval", "view", "hasClasroom", "filterItems", "editor", "quill", "getQuill", "getModule", "add<PERSON><PERSON><PERSON>", "imageHandler", "bind", "_this", "_asyncToGenerator", "input", "document", "createElement", "setAttribute", "click", "onchange", "file", "files", "size", "alert", "reader", "FileReader", "onload", "e", "imageUrl", "target", "range", "getSelection", "insertEmbed", "readAsDataURL", "error", "pasteHandler", "_this2", "clipboardData", "i", "indexOf", "preventDefault", "getAsFile", "content", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc", "parseFromString", "htmlValue", "images", "querySelectorAll", "contentUpdated", "for<PERSON>ach", "img", "src", "getAttribute", "startsWith", "base64Length", "padding", "char<PERSON>t", "fileSize", "remove", "updatedHtml", "body", "innerHTML", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "NoteService", "i3", "NoteListenersService", "ChangeDetectorRef", "i4", "AuthService", "i5", "ToastService", "i6", "NotificationService", "i7", "i8", "_2", "selectors", "viewQuery", "NoteDetailsComponent_Query", "rf", "ctx", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NoteDetailsComponent_Template", "NoteDetailsComponent_ng_container_0_Template", "NoteDetailsComponent_ng_container_1_Template", "NoteDetailsComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\notes-list\\note-details\\note-details.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\notes-list\\note-details\\note-details.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, inject, Injector, Input, OnInit, Output, signal, ViewChild } from '@angular/core';\r\n\r\nimport { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { Note, NoteCreateRequest, NoteUpdateRequest, SendShareNoteRequest } from 'src/app/core/models/note.model';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { take } from 'rxjs/operators';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { NotificationPost } from 'src/app/core/models/notification.model';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { UntypedFormGroup, UntypedFormControl, Validators } from '@angular/forms';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { Observable } from 'rxjs';\r\nimport { NoteActionsService, NoteUpdateAction, NoteUpdateActionSource } from 'src/app/core/services/note-actions.service';\r\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\r\nimport { OverlayPanel } from 'primeng/overlaypanel';\r\nimport { Editor } from 'primeng/editor';\r\nimport { toObservable } from '@angular/core/rxjs-interop';\r\n\r\n@Component({\r\n  selector: 'app-note-details',\r\n  templateUrl: './note-details.component.html',\r\n  styleUrls: ['./note-details.component.scss'],\r\n  providers: [DialogService, NoteActionsService],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class NoteDetailsComponent implements OnInit {\r\n  @ViewChild('menu3') menu3!: OverlayPanel;\r\n  @Input() note: Note = {} as Note;\r\n  @Input() isInMine: boolean = false;\r\n  @Input() withClassroom: boolean = false;\r\n  @Input() type: string = '';\r\n  @Input() view!: NoteViewAccess;\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  @Input() showGroupActions: boolean = false;\r\n  @Output() closedNewNote = new EventEmitter();\r\n  @Output() noteChecked = new EventEmitter();\r\n  classrooms: Classroom[] = [];\r\n  relatedClassroomId = 0;\r\n  @ViewChild('rte')\r\n  public rte: any = {} as any\r\n  private subs = new SubSink();\r\n  showBurger: boolean = false;\r\n  editNote = signal(false);\r\n  checked: boolean = false;\r\n  public insertImageSettings = {\r\n    saveUrl: environment.apiUrl + '/notesImages'\r\n  };\r\n  showSend: boolean = false;\r\n  showSendAndShare: boolean = false;\r\n  usersToSend: User[] = [];\r\n  public user: User = {} as User;\r\n  public role: string = \"\"\r\n  public isOpen: boolean = true;\r\n\r\n  @ViewChild('editor', { static: true }) editor: Editor | undefined; // Reference to the PrimeNG Editor component\r\n\r\n  private injector = inject(Injector);\r\n\r\n  public tools: object = {\r\n    type: 'Expand',\r\n    items: ['Bold', 'Italic', 'Underline', 'StrikeThrough',\r\n      'FontName', 'FontSize', 'FontColor', 'BackgroundColor',\r\n      'LowerCase', 'UpperCase', '|',\r\n      'Formats', 'Alignments', 'OrderedList', 'UnorderedList',\r\n      'Outdent', 'Indent', '|', 'Undo', 'Redo']\r\n  };\r\n  menuItems: MenuItem[] = [];\r\n  tryToSave: boolean = false;\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  filteredMenuItems: MenuItem[] = [];\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private noteService: NoteService,\r\n    private noteListenersService: NoteListenersService,\r\n    private cd: ChangeDetectorRef,\r\n    private authService: AuthService,\r\n    private toastService: ToastService,\r\n    private notificationService: NotificationService,\r\n    private dialogService: DialogService,\r\n    public noteActionsService: NoteActionsService,\r\n  ) { }\r\n\r\n  isActive: boolean = false;\r\n\r\n  toggle() {\r\n    this.isActive = !this.isActive;\r\n    this.isOpen = !this.isOpen;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initcloseNewNoteSelectedListener();\r\n    this.initNoteUpdateListener();\r\n    this.form = new UntypedFormGroup({\r\n      title: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      text: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n    });\r\n\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.user.role!;\r\n    this.noteActionsService.setNote(this.note);\r\n    this.noteActionsService.setNoteForm(this.form);\r\n    // this.subs.sink = this.noteListenersService.showGroupActions.subscribe(res => {\r\n    //   this.showGroupActions = res;\r\n    //   alert(res);\r\n    //   if (!res) {\r\n    //     this.checked = false;\r\n    //   }\r\n    // })\r\n\r\n\r\n    if (this.type === 'create') {\r\n      this.toggle();\r\n    }\r\n    this.filteredMenuItems = this.prepareMenuItems();\r\n    this.handleMenuItems();\r\n\r\n\r\n    toObservable(this.editNote, {\r\n      injector: this.injector\r\n    }).subscribe({\r\n      next: (data) => {\r\n        if (data) {\r\n          this.initializeQuill();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.getMenuItemsForItem(this.note);\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.noteListenersService.setGroupActions(false);\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  toggleMenuItems(event: any) {\r\n    this.menu3.show(event);\r\n  }\r\n\r\n  prepareMenuItems() {\r\n    // when we create a new note, we dont need a menu\r\n    if (this.type === 'create') {\r\n      return [];\r\n    }\r\n    const that = this;\r\n\r\n    this.menuItems = [\r\n      {\r\n        id: \"edit\",\r\n        label: 'Edit', icon: 'pi pi-fw pi-file-edit',\r\n        command: (event) => {\r\n\r\n          that.isActive = true;\r\n          that.editNote.set(true);\r\n        }\r\n      },\r\n      {\r\n        id: \"share\",\r\n        label: 'Share', icon: 'pi pi-fw pi-share-alt',\r\n        command: (event) => {\r\n          if (that.authService.isStudent) {\r\n            that.sendOrShare({ action: 'shareWith', map: { classroom: that.classroom } }, true);\r\n          } else {\r\n            if (!this.generalService.isNullishObject(that.classroom) && that.classroom.classroomStudents.length === 1) {\r\n              const send: SendShareNoteRequest = {\r\n                noteId: this.note.id,\r\n                classroomId: +that.classroom.id,\r\n                studentIds: [(that.classroom.classroomStudents[0].aspUserId!).toString()],\r\n              };\r\n              // Share the note with the specified classroom\r\n              this.noteActionsService.shareNote(send, \"Note has been shared\");\r\n            } else {\r\n              that.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n                {\r\n                  action: 'shareWith',\r\n                  dialogsubTitle: 'Share  with...',\r\n                  dialogTitle: 'Share file with...',\r\n                  preselectedClassroom: that.classroom,\r\n                });\r\n            }\r\n          }\r\n        }\r\n      },\r\n      {\r\n        id: \"favourite\",\r\n        label: this.isFavorited() ? 'Unfavourite' : 'Favourite', icon: 'pi pi-fw pi-star',\r\n        command: (event) => {\r\n          console.log(event);\r\n          this.noteActionsService.favorite(this.note, this.user, this.isFavorited());\r\n        }\r\n      },\r\n      {\r\n        id: \"pin\",\r\n        label: that.note.pinned ? 'Unpin' : 'Pin', icon: 'pi pi-fw pi-map-marker',\r\n        command: (event) => {\r\n          that.noteActionsService.pin(that.note);\r\n        }\r\n      },\r\n      {\r\n        id: \"duplicate\",\r\n        label: 'Duplicate', icon: 'pi pi-fw pi-copy',\r\n        command: (event) => {\r\n          console.log(event);\r\n          that.noteActionsService.dublicate(that.note, that.classroom);\r\n        }\r\n      },\r\n      {\r\n        id: \"send\",\r\n        label: 'Send', icon: 'pi pi-fw pi-arrow-right',\r\n        command: (event) => {\r\n          that.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            {\r\n              action: 'send',\r\n              dialogsubTitle: 'Send to...',\r\n              dialogTitle: 'Send note to ...',\r\n            });\r\n        }\r\n      },\r\n      {\r\n        id: \"send-share\",\r\n        label: 'Send & Share', icon: 'pi pi-fw pi-arrow-right',\r\n        command: (event) => {\r\n          that.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            {\r\n              action: 'send-and-share',\r\n              dialogsubTitle: 'Send & Share to...',\r\n              dialogTitle: 'Send & Share note to ...',\r\n            });\r\n        }\r\n      },\r\n      {\r\n        id: \"download\",\r\n        label: 'Download', icon: 'pi pi-fw pi-cloud-download',\r\n        command: (event) => {\r\n          that.noteActionsService.download(that.note);\r\n        }\r\n      },\r\n      {\r\n        id: \"unshare\",\r\n        label: 'Unshare', icon: 'pi pi-fw pi-share-alt',\r\n        command: (event) => {\r\n          let unshareWith: any[] = [];\r\n          let usersShared: User[] = that.note.sharedWithUsers;\r\n          let names: string = \"\";\r\n\r\n          if (that.authService.isStudent) {\r\n            if (this.isInMine) {\r\n              unshareWith.push(that.classroom.teacher!.aspUserId);\r\n            } else {\r\n              unshareWith.push(that.user.aspUserId);\r\n            }\r\n          } else {\r\n            for (let user of usersShared) {\r\n              unshareWith.push(user.aspUserId);\r\n            }\r\n          }\r\n\r\n          const send: SendShareNoteRequest = {\r\n            noteId: that.note.id,\r\n            classroomId: +that.classroom.id,\r\n            studentIds: unshareWith\r\n          };\r\n          that.noteActionsService.unShareNote(send, 'Note has been unshared');\r\n          if (that.authService.isTeacher && this.isInMine && this.note.pinned) {\r\n            that.noteActionsService.pin(this.note);\r\n          }\r\n        }\r\n      },\r\n      {\r\n        id: \"delete\",\r\n        label: 'Delete', icon: 'pi pi-fw pi-trash',\r\n        command: (event) => {\r\n          that.noteActionsService.removeNote(that.note);\r\n        }\r\n      }\r\n    ];\r\n\r\n    return this.menuItems;\r\n  }\r\n\r\n\r\n  /**\r\n   * The function `getMenuItemsForItem` returns an array of menu items based on the type of note and its\r\n   * properties.\r\n   * @param {Note} item - The parameter \"item\" is of type \"Note\", which represents a note object.\r\n   * @returns The function `getMenuItemsForItem` returns an array of `MenuItem` objects.\r\n   */\r\n  private getMenuItemsForItem(item: Note): MenuItem[] {\r\n    return this.filteredMenuItems;\r\n  }\r\n\r\n  filterMenuItems(items: MenuItem[], idsToRemove: string[], idsToAdd: MenuItem[]) {\r\n\r\n    // Filter out items to remove\r\n    const filteredOutItems = items.filter(item => !idsToRemove.includes(item.id!));\r\n\r\n    // Add new items\r\n    this.filteredMenuItems = [...filteredOutItems, ...idsToAdd];\r\n\r\n    console.log(this.filteredMenuItems)\r\n    return this.filteredMenuItems;\r\n  }\r\n  /**\r\n   * The function \"getUsersNoteHasBeenSharedWith\" takes a note object as input and returns a string\r\n   * containing the names of users the note has been shared with.\r\n   * @param {Note} note - The parameter \"note\" is of type \"Note\", which represents a note object.\r\n   * @returns a string containing the names of the users that the note has been shared with.\r\n   */\r\n  getNoteCreatedBy(note: Note): string {\r\n    let usersShared = note.createdBy;\r\n    let names: string = \"\";\r\n    names += usersShared.firstName + \" \"\r\n\r\n    return names;\r\n  }\r\n\r\n  /**\r\n   * The function \"getUsersNoteHasBeenSharedWith\" takes a note object as input and returns a string\r\n   * containing the names of users the note has been shared with.\r\n   * @param {Note} note - The parameter \"note\" is of type \"Note\", which represents a note object.\r\n   * @returns a string containing the names of the users that the note has been shared with.\r\n   */\r\n  getUsersNoteHasBeenSharedWith(note: Note): string {\r\n    let usersShared: User[] = note.sharedWithUsers;\r\n    let names: string = \"\";\r\n    for (let user of usersShared) {\r\n      names += user.firstName + \", \"\r\n    }\r\n    names = names.substring(0, names.length - 2);\r\n\r\n    return names;\r\n  }\r\n\r\n  /**\r\n   * The function \"showSendNotification\" returns true if the note is not mine and it is shared\r\n   * with at least one user, otherwise it returns false.\r\n   * @returns a boolean value. If the condition `!this.isInMine` is true and the length of\r\n   * `this.note.sharedWithUsers` is greater than 0, then it returns `true`. Otherwise, it returns\r\n   * `false`.\r\n   */\r\n  showSendNotification() {\r\n    if (!this.isInMine) {\r\n      if (this.note.sharedWithUsers.length > 0) {\r\n        return true\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * The function sends a notification to each user that a note has been shared with them.\r\n   */\r\n  sendNotification() {\r\n    for (let user of this.note.sharedWithUsers) {\r\n      let notification: NotificationPost = {\r\n        title: `View note ${this.note.title} that has been shared with you`,\r\n        description: \"Go to your classroom notes to view the note we shared with you\",\r\n        createdForId: user.aspUserId!,\r\n        type: \"note\",\r\n        isRead: false\r\n      }\r\n      this.subs.sink = this.notificationService.create(notification).pipe(take(1)).subscribe(res => {\r\n        console.log(res)\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Notification has been sent'\r\n        });\r\n      })\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determines if the note is favorited by the current user.\r\n   *\r\n   * @return {boolean} - True if the note is favorited by the current user, false otherwise.\r\n   */\r\n  isFavorited() {\r\n    return this.note.favouritedBy.some((user: User) => user.aspUserId == this.authService.getUserId())\r\n  }\r\n\r\n  /**\r\n   * The `sendOrShare` function takes an event object as a parameter, extracts the selected users and\r\n   * their corresponding classroom IDs from the event, and then sends or shares a note based on the kind\r\n   * of event.\r\n   * @param {any} event - The parameter `event` is of type `any`, which means it can be any type of\r\n   * object.\r\n   */\r\n  sendOrShare(event: any, isStudent = false) {\r\n    this.noteActionsService.sendOrShare(this.note, event, isStudent);\r\n  }\r\n\r\n  close(event: any) {\r\n    this.showSend = false\r\n    this.showSendAndShare = false\r\n  }\r\n\r\n  ifFieldValid(field: string) {\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid))\r\n  }\r\n\r\n  isTextValid() {\r\n    // this.showTextError =  true\r\n  }\r\n\r\n  closedNewNoteSelected() {\r\n    this.closedNewNote.emit({});\r\n  }\r\n\r\n  /**\r\n   * The function returns the fill color for a pin icon based on whether it is favorited or not.\r\n   * @returns The color being returned depends on whether the item is favorited or not. If the item is\r\n   * favorited, the color returned is '#FFFFFF' (white). If the item is not favorited, the color returned\r\n   * is '#6677D1' (a shade of blue).\r\n   */\r\n  getPinFillColor(): string {\r\n    return this.isFavorited() || (this.note.pinned) ? '#FFFFFF' : '#6677D1';\r\n  }\r\n\r\n  getHeaderClassName(): any {\r\n    return {\r\n      'opened2 activated': this.isActive,\r\n      'opened': this.isFavorited(),\r\n      'text-white': this.note.pinned,\r\n      'isPinned': this.note.pinned,\r\n      'isPinnedAndFavourite': this.isFavorited() && this.note.pinned\r\n    };\r\n  }\r\n\r\n  getNoteInfoClassName(): any {\r\n    return {\r\n      'text-white': this.note.pinned || this.isFavorited(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gets the CSS class name for the note burger icon.\r\n   * Returns 'filter-white' if the note is active, favorited, or pinned.\r\n   * Otherwise returns an empty string.\r\n   */\r\n  getNoteBurgerClassName(): string {\r\n    return this.isActive || this.isFavorited() || this.note.pinned\r\n      ? \"filter-white\"\r\n      : \"\";\r\n  }\r\n\r\n  checkNoteItem(el: any) {\r\n    this.checked = !this.checked;\r\n    if (this.checked) {\r\n      // this.noteService.checkedLibraries.push(this.classroomFile);\r\n      this.noteChecked.emit({ checked: true, note: this.note })\r\n    } else {\r\n      this.noteChecked.emit({ checked: false, note: this.note })\r\n    }\r\n  }\r\n\r\n  private openDialogWithComponent(component: any, dialogData?: any, width = 360) {\r\n\r\n    this.generalService.openDialogWithComponent(this.dialogService, component, 400, dialogData, null, (result: any) => {\r\n      console.log(result);\r\n      if (result && result.action === 'shareWith') {\r\n        if (!this.withClassroom && this.authService.isTeacher) {\r\n          this.noteActionsService.dublicateAndShare(this.note, result);\r\n        } else {\r\n          this.sendOrShare(result);\r\n        }\r\n      } else if (result && result.action === 'send') {\r\n        this.sendOrShare(result);\r\n      } else if (result && result.action === 'send-and-share') {\r\n        this.sendOrShare(result);\r\n      }\r\n    });\r\n  }\r\n\r\n  private initcloseNewNoteSelectedListener() {\r\n    this.subs.add(this.noteActionsService.closeExpandedNoteListener.subscribe((shouldClose) => {\r\n      if (shouldClose) {\r\n        this.closedNewNote.emit({});\r\n        this.noteActionsService.setcloseNewNoteSelected(false);\r\n      }\r\n    }));\r\n  }\r\n\r\n  private initNoteUpdateListener() {\r\n    this.subs.add(this.noteActionsService.updateNote.subscribe((noteUpdateSource: NoteUpdateActionSource) => {\r\n      if (!noteUpdateSource) {\r\n        return;\r\n      }\r\n      if (!this.generalService.isNullishObject(noteUpdateSource)) {\r\n        console.log(noteUpdateSource)\r\n        this.processNoteUpdate(noteUpdateSource);\r\n      }\r\n    }));\r\n  }\r\n\r\n  private processNoteUpdate(note: NoteUpdateActionSource) {\r\n    this.note = note.note;\r\n\r\n    switch (note.action) {\r\n      case NoteUpdateAction.UPDATE:\r\n        this.editNote.set(false);\r\n        break;\r\n      case NoteUpdateAction.PIN:\r\n        break;\r\n      case NoteUpdateAction.FAVOURITE:\r\n        if (this.isFavorited()) {\r\n          let index = this.note.favouritedBy.findIndex(\r\n            (el) => el.id === this.authService.getUserId()\r\n          );\r\n          this.note.favouritedBy.splice(index, 1);\r\n        } else {\r\n          let userToAdd = this.authService.getLoggedInUser();\r\n          userToAdd.aspUserId = userToAdd.id;\r\n          this.note.favouritedBy.push(userToAdd);\r\n        }\r\n        break;\r\n    }\r\n\r\n    this.getMenuItemsForItem(this.note);\r\n    this.filteredMenuItems = this.prepareMenuItems();\r\n    this.handleMenuItems();\r\n    this.noteService.setUpdateNoteListener(true);\r\n  }\r\n\r\n  /**\r\n   * Handles filtering of menu items based on user role and context.\r\n   *\r\n   * Checks if currently viewing a classroom note.\r\n   * If so, applies filtering:\r\n   * - For students, removes share, send, delete, pin, duplicate, favourite menu items\r\n   * - For teachers, removes delete, pin, favourite menu items\r\n   *\r\n   * This restricts available actions on classroom notes based on role.\r\n   */\r\n  private handleMenuItems() {\r\n    if (!this.classroom || !this.note) {\r\n      return;\r\n    }\r\n    const removedMenuItems = this.noteService.buildMenuOptionsForRemoval(this.note, this.view, this.classroom);\r\n\r\n    const hasClasroom = !this.generalService.isNullishObject(this.classroom);\r\n    const filterItems = this.filterMenuItems(\r\n      this.prepareMenuItems(),\r\n      removedMenuItems,\r\n      [],\r\n    );\r\n  }\r\n\r\n  initializeQuill() {\r\n    if (!this.editor) {\r\n      return;\r\n    }\r\n\r\n    const quill = this.editor.getQuill(); // Get the Quill instance\r\n\r\n    // Override the default image handler\r\n    quill.getModule('toolbar').addHandler('image', this.imageHandler.bind(this));\r\n  }\r\n\r\n  async imageHandler() {\r\n    const input = document.createElement('input');\r\n    input.setAttribute('type', 'file');\r\n    input.setAttribute('accept', 'image/*');\r\n    input.click();\r\n\r\n    input.onchange = async () => {\r\n      const file = input.files?.[0];\r\n      if (file) {\r\n        // Check file size (5 MB limit)\r\n        if (file.size > 5 * 1024 * 1024) { // 5 MB in bytes\r\n          alert('The image exceeds the 5 MB limit. Please choose a smaller image.');\r\n          return; // Drop the upload\r\n        }\r\n\r\n        try {\r\n          const reader = new FileReader();\r\n          reader.onload = (e) => {\r\n            const imageUrl = e.target?.result as string;\r\n            const range = this.editor!.getQuill().getSelection(true);\r\n            this.editor!.getQuill().insertEmbed(range.index, 'image', imageUrl, 'user');\r\n          };\r\n          reader.readAsDataURL(file);\r\n        } catch (error) {\r\n          console.error('Error reading image:', error);\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  async pasteHandler(event: ClipboardEvent) {\r\n    const items = event.clipboardData?.items;\r\n    if (items) {\r\n      for (let i = 0; i < items.length; i++) {\r\n        const item = items[i];\r\n        if (item.type.indexOf('image') !== -1) {\r\n          event.preventDefault(); // Prevent the default paste behavior\r\n          const file = item.getAsFile();\r\n          if (file) {\r\n            // Check file size (5 MB limit)\r\n            if (file.size > 5 * 1024 * 1024) { // 5 MB in bytes\r\n              alert('The pasted image exceeds the 5 MB limit. Please use a smaller image.');\r\n              return; // Drop the upload\r\n            }\r\n\r\n            try {\r\n              const reader = new FileReader();\r\n              reader.onload = (e) => {\r\n                const imageUrl = e.target?.result as string;\r\n                const range = this.editor!.getQuill().getSelection(true);\r\n                this.editor!.getQuill().insertEmbed(range.index, 'image', imageUrl, 'user');\r\n              };\r\n              reader.readAsDataURL(file);\r\n            } catch (error) {\r\n              console.error('Error reading pasted image:', error);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  onContentChange(content: any) {\r\n    console.log('Original content:', content);\r\n  \r\n    // Parse the HTML content\r\n    const parser = new DOMParser();\r\n    const doc = parser.parseFromString(content.htmlValue, 'text/html');\r\n  \r\n    // Find all <img> tags in the content\r\n    const images = doc.querySelectorAll('img');\r\n    let contentUpdated = false;\r\n  \r\n    images.forEach((img) => {\r\n      const src = img.getAttribute('src');\r\n  \r\n      // Check if the image is a data URL (base64)\r\n      if (src && src.startsWith('data:image')) {\r\n        // Calculate the size of the base64 image\r\n        const base64Length = src.length - (src.indexOf(',') + 1);\r\n        const padding = (src.charAt(src.length - 2) === '=') ? 2 : (src.charAt(src.length - 1) === '=') ? 1 : 0;\r\n        const fileSize = (base64Length * 0.75) - padding; // Size in bytes\r\n  \r\n        // Check if the image exceeds the size limit (5 MB)\r\n        if (fileSize > 5 * 1024 * 1024) { // 5 MB in bytes\r\n          // Remove the image from the content\r\n          img.remove();\r\n          contentUpdated = true;\r\n        }\r\n      }\r\n    });\r\n  \r\n    // If any images were removed, update the content's htmlValue\r\n    if (contentUpdated) {\r\n      const updatedHtml = doc.body.innerHTML;\r\n      console.log('Updated content:', updatedHtml);\r\n  \r\n      // Update the htmlValue property\r\n      content.htmlValue = updatedHtml;\r\n  \r\n      // Notify the user\r\n      alert('One or more images exceeded the 5 MB limit and were removed.');\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- /**\r\n * Renders note details and edit components.\r\n * \r\n * Shows note title, creation/edit dates, favorited state, \r\n * pinned state if shared, shared users, and note text.\r\n * \r\n * For editing, uses form group to manage title and text values.\r\n * Saves edited note on update() call.\r\n * Can save as new note instead of updating original.\r\n * \r\n * Handles create note flow as well with empty form group.\r\n * Saves new note on noteActionsService.addOrUpdateNote() call.\r\n */\r\n */ * Handles create note flow as well with empty form group. *  * Can save as new note instead of updating original. * Saves edited note on update() call. * For editing, uses form group to manage title and text values. *  * pinned state if shared, shared users, and note text. * Shows note title, creation/edit dates, favorited state,  *  * Renders note details and edit components.!-- -->\r\n\r\n<ng-container *ngIf=\"type !== 'create'\">\r\n\r\n    <div class=\"accordion border-round-3xl note\" [class.active]=\"isActive\">\r\n        <div class=\"header w-full\" \r\n            [ngClass]=\"getHeaderClassName()\">\r\n            <div class=\"flex w-full\" (click)=\"toggle($event)\">\r\n                <div class=\"flex align-items-center gap-2\">\r\n                    <div class=\"flex\">\r\n                        <div class=\"circle ml-1\">\r\n                            <i [ngClass]=\"{'pi-chevron-down': !isActive, 'pi-chevron-up': isActive}\"\r\n                                class=\"down-arrow pi\"></i>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"package-header w-100 justify-content-between align-items-center font-xs\">\r\n                        <div class=\"note-title file-path-text\">\r\n                            <div class=\"note-title-text file-path-text\">{{note.title}}</div>\r\n                            <div class=\"note-dates hidden sm:block\">\r\n                                <div class=\"flex align-items-center font-2xs gap-2\">\r\n                                    <span class=\"font-2xs\"><i class=\"pi pi-file-edit font-xs\"></i> {{note.created |\r\n                                        date: 'dd-MM-yyyy'}} </span>\r\n                                    <span class=\"font-2xs\" *ngIf=\"note.edited\"><i class=\"pi pi-pencil font-xs\"></i>\r\n                                        {{note.edited | date: 'dd-MM'}} &nbsp; by {{note.editedBy.firstName}}</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"ml-auto flex align-items-center\">\r\n                <div class=\"note-info\">\r\n                    <div class=\"note-info-top\">\r\n                        <div *ngIf=\"note.pinned\" class=\"note-info-element\">\r\n                            <div class=\"note-info-icon\">\r\n                                <!-- PIN SVG ICON -->\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"11.174\" height=\"14.519\"\r\n                                    viewBox=\"0 0 8.174 11.519\">\r\n                                    <path id=\"pin-18_Converted_-01\" data-name=\"pin-18 [Converted]-01\"\r\n                                        d=\"M4.092,0H4.086A4.088,4.088,0,0,0,.506,6.054l3.583,5.466L7.672,6.054A4.087,4.087,0,0,0,4.092,0Z\"\r\n                                        transform=\"translate(-0.002)\" [style.fill]=\"getPinFillColor()\" />\r\n                                </svg>\r\n                            </div>\r\n                            <div  class=\"note-info-text  hidden sm:block\">\r\n                                Pinned\r\n                            </div>\r\n                        </div>\r\n                        <div *ngIf=\"isFavorited()\" class=\"note-info-element\">\r\n                            <div class=\"note-info-icon\">\r\n                                <i class=\"pi pi-star-fill font-sm\"></i>\r\n                                <!-- <img src=\"/assets/icons/star.svg\"> -->\r\n                            </div>\r\n                            <div class=\"note-info-text  hidden sm:block\">\r\n                                Favourite\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div *ngIf=\"note.sharedWithUsers.length>0 && withClassroom\" class=\"note-info-bottom\"\r\n                        [ngClass]=\"getNoteInfoClassName()\">\r\n                        <ng-container *ngIf=\"isInMine\">\r\n                            <span class=\"line-height-1 pt-1 note-info-width flex justify-content-end gap-1\" [ngClass]=\"{'text-white': isActive}\"> \r\n                            <span class=\"hidden sm:block\">Shared with</span>\r\n                        <span class=\"block sm:hidden\"><i class=\"pi pi-share-alt font-2xs\"></i></span>\r\n                              {{getUsersNoteHasBeenSharedWith(note)}}</span>\r\n                        </ng-container>\r\n                        <ng-container *ngIf=\"!isInMine\">\r\n                            <span class=\"line-height-1 pt-1 note-info-width flex justify-content-end gap-1\" [ngClass]=\"{'text-white': isActive}\"> \r\n                        <span class=\"hidden sm:block\">Shared by</span>\r\n                        <span class=\"block sm:hidden\"><i class=\"pi pi-share-alt font-2xs\"></i></span>\r\n                              {{getNoteCreatedBy(note)}}</span>\r\n                        </ng-container>\r\n                    \r\n                    </div>\r\n                </div>\r\n\r\n                <div *ngIf=\"!showGroupActions\" class=\"note-burger hvr-grow\"\r\n                    (click)=\"!showGroupActions ? toggleMenuItems($event) : null\" style=\"position: relative;\"\r\n                    id=\"note-burger-{{note.id}}\">\r\n                    <div class=\"inner-circle\"></div>\r\n                    <div class=\"inner-circle\"></div>\r\n                    <div class=\"inner-circle\"></div>\r\n                </div>\r\n\r\n                <div *ngIf=\"showGroupActions\" (click)=\"checkNoteItem(note)\" style=\"position: relative;\"\r\n                    class=\"flex align-items-center\">\r\n                    <img *ngIf=\"!checked\" src=\"/assets/icons/library/group-item-empty.svg\" class=\"pointer\"\r\n                        [ngClass]=\"getNoteBurgerClassName()\"\r\n                        style=\"width:2rem; height:2rem\">\r\n                    <img *ngIf=\"checked\"\r\n                        [ngClass]=\"getNoteBurgerClassName()\"\r\n                        src=\"/assets/icons/library/group-item-full.svg\" class=\"pointer\" style=\"width:2rem; height:2rem\">\r\n                    <!-- <div id=\"check-lib-{{libraryFile.filePath}}-1\" #checkLibMenu class=\"inner-circle-group\"></div> -->\r\n                </div>\r\n            </div>\r\n            <!-- <i class=\"pi\" [ngClass]=\"{'pi pi-chevron-down': !isActive, 'pi pi-chevron-up': isActive}\"></i> -->\r\n        </div>\r\n        <div class=\"accordion-content accordion-content-transition\" [ngClass]=\"{'open': isActive}\">\r\n            <div class=\"note-content\">\r\n                <div class=\"note-text p-2\" id='defaultRTE-{{note.id}}'>\r\n                    <div *ngIf=\"!editNote()\" class=\" plain\">\r\n\r\n                        <p-editor #editor [(ngModel)]=\"note.text\" (onTextChange)=\"onContentChange($event)\" [readonly]=\"true\" [style]=\"{'height':'360px'}\">\r\n                            <ng-template pTemplate=\"header\">\r\n                            </ng-template>\r\n                        </p-editor>\r\n\r\n                    </div>\r\n\r\n                    <div *ngIf=\"editNote()\">\r\n\r\n                        <form [formGroup]=\"form\">\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Note Title</div>\r\n                            <input class=\"input-element\" formControlName=\"title\" [(ngModel)]=\"note.title\" type=\"text\">\r\n                            <!-- <div *ngIf=\"ifFieldValid('title')\" class=\"input-error\">* Title is required</div> -->\r\n                        </div>\r\n                        <p-editor #editor [(ngModel)]=\"note.text\" formControlName=\"text\" (onTextChange)=\"onContentChange($event)\"  [style]=\"{'height':'150px'}\">\r\n                            <ng-template pTemplate=\"header\">\r\n                                <ng-container *ngTemplateOutlet=\"quillEditorTemplate\"></ng-container>\r\n                            </ng-template>\r\n                        </p-editor>\r\n                        </form>\r\n                        <div class=\"btns justify-content-center\">\r\n                            <div class=\"light-purple-button general-rounded-btn blue-grad m-t-15\"\r\n                                (click)=\"noteActionsService.addOrUpdateNote(true, classroom)\" style=\"width:300px\"\r\n                                title=\" \"\r\n                                *ngIf=\"note.createdBy.aspUserId!=user.id\"\r\n                                [pTooltip]=\"'This note was not created by you. If you modify it, it will be saved as new and the original will stay as is'\">\r\n                                Save as your note\r\n                            </div>\r\n                            <div class=\"light-purple-button general-rounded-btn blue-grad m-t-15\" style=\"width:200px\"\r\n                                (click)=\"noteActionsService.update(note)\" *ngIf=\"note.createdBy.aspUserId==user.id\">\r\n                                Update <i class=\"pi pi-refresh\"></i>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <p-menu #menu3 popup=\"popup\" [model]=\"filteredMenuItems\" appendTo=\"body\"></p-menu>\r\n\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"type === 'create'\">\r\n    <div class=\"accordion border-round-3xl note\" [class.active]=\"isActive\">\r\n        <div class=\"header w-full\" [ngClass]=\"{'opened': isActive}\">\r\n            <div class=\"\" (click)=\"toggle()\">\r\n                <div class=\"flex align-items-center gap-2\">\r\n                    <div class=\"flex\">\r\n                        <div class=\"circle ml-1\">\r\n                            <i [ngClass]=\"{'pi-chevron-down': !isActive, 'pi-chevron-up': isActive}\"\r\n                                class=\"down-arrow pi\"></i>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"package-header w-100 justify-content-between align-items-center font-xs font-bold\">\r\n                        <div class=\"note-title\">\r\n                            <div class=\"note-title-text\">Create new Note</div>\r\n                            <div class=\"note-dates\">\r\n                                <div class=\"flex align-items-center font-2xs gap-2\">\r\n                                    <!-- <span class=\"font-2xs\"><i class=\"pi pi-file-edit font-xs\"></i> {{note.created | date: 'dd-MM'}} </span> -->\r\n                                    <!-- <span class=\"font-2xs\" *ngIf=\"note.edited\"><i class=\"pi pi-pencil font-xs\"></i> {{note.edited | date: 'dd-MM'}} &nbsp; by {{note.editedBy.firstName}}</span> -->\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"ml-auto flex align-items-center\">\r\n                <div class=\"note-info\">\r\n                    <div class=\"note-info-top\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div *ngIf=\"!showGroupActions\" class=\"note-burger hvr-grow\" (click)=\"closedNewNoteSelected()\"\r\n                    style=\"position: relative;\" id=\"note-burger-{{note.id}}\">\r\n                    <i class=\"pi pi-times\"></i>\r\n                </div>\r\n\r\n\r\n            </div>\r\n            <!-- <i class=\"pi\" [ngClass]=\"{'pi pi-chevron-down': !isActive, 'pi pi-chevron-up': isActive}\"></i> -->\r\n        </div>\r\n        <div class=\"accordion-content accordion-content-transition\" [ngClass]=\"{'open': isActive}\">\r\n            <div class=\"note-content\">\r\n                <div class=\"note-text p-2\">\r\n                    <form [formGroup]=\"form\">\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Note Title</div>\r\n                            <input class=\"input-element\" formControlName=\"title\" type=\"text\">\r\n                            <!-- <div *ngIf=\"ifFieldValid('title')\" class=\"input-error\">* Title is required</div> -->\r\n                        </div>\r\n                        <p-editor [debug]=\"true\" (ngModel)=\"textEditor\" formControlName=\"text\" [style]=\"{'height':'170px'}\">\r\n                            <ng-template pTemplate=\"header\">\r\n                                <ng-container *ngTemplateOutlet=\"quillEditorTemplate\"></ng-container>\r\n                            </ng-template>\r\n                        </p-editor>\r\n                        <div *ngIf=\"showTextError\" class=\"input-error\">* Text is required</div>\r\n                    </form>\r\n                    <div class=\"btns justify-content-center\">\r\n                        <div class=\"light-purple-button general-rounded-btn blue-grad m-t-15 m-1 flex align-items-center justify-content-center gap-2 btn-action\"\r\n                            (click)=\"noteActionsService.addOrUpdateNote(false, classroom)\">\r\n                            Save <span *ngIf=\"inClassroom && role=='Teacher'\">for you</span> <i\r\n                                class=\"pi pi-save\"></i>\r\n                        </div>\r\n                        <div *ngIf=\"this.authService.isStudent\"\r\n                        class=\"light-purple-button general-rounded-btn blue-grad m-t-15  m-1 flex align-items-center justify-content-center gap-2 btn-action\"\r\n                            (click)=\"noteActionsService.addOrUpdateNoteAndShareWithTeacher(note, false, classroom)\">\r\n                            Save & Share <i class=\"pi pi-refresh\"></i>\r\n                        </div>\r\n                        <div class=\"light-purple-button general-rounded-btn blue-grad m-t-15\" style=\"width:200px\"\r\n                            (click)=\"noteActionsService.update(note)\" *ngIf=\"inClassroom && role=='Teacher'\">\r\n                            Save and Share with classroom<i class=\"pi pi-refresh\"></i>\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n\r\n</ng-container>\r\n\r\n\r\n\r\n<ng-template #quillEditorTemplate>\r\n    <div id=\"toolbar-container\">\r\n        <span class=\"ql-formats\">\r\n            <select class=\"ql-font\">\r\n            </select>\r\n            <select class=\"ql-size\"></select>\r\n        </span>\r\n        <span class=\"ql-formats\">\r\n            <button class=\"ql-bold\" title=\"bold\" arial-label=\"bold\"></button>\r\n            <button class=\"ql-italic\" title=\"italic\" arial-label=\"italic\"></button>\r\n            <button class=\"ql-underline\" title=\"underline\" arial-label=\"underline\"></button>\r\n            <button class=\"ql-strike\" title=\"strike\" arial-label=\"strike\"></button>\r\n        </span>\r\n        <span class=\"ql-formats\">\r\n            <select class=\"ql-color\" title=\"color\" arial-label=\"color\"></select>\r\n            <select class=\"ql-background\" title=\"background\" arial-label=\"background\"></select>\r\n        </span>\r\n        <span class=\"ql-formats\">\r\n            <button class=\"ql-header\" value=\"1\" title=\"header 1\" arial-label=\"header 1\"></button>\r\n            <button class=\"ql-header\" value=\"2\" title=\"header 2\" arial-label=\"header 2\"></button>\r\n        <button class=\"ql-blockquote\" title=\"blockquote\" arial-label=\"blockquote\"></button>\r\n        <button class=\"ql-code-block\" title=\"code block\" arial-label=\"code block\"></button>\r\n        </span>\r\n        <span class=\"ql-formats\">\r\n            <button class=\"ql-list\" value=\"ordered\" title=\"ordered list\" arial-label=\"ordered list\"></button>\r\n            <button class=\"ql-list\" value=\"bullet\" title=\"unordered list\" arial-label=\"unordered list\"></button>\r\n            <button class=\"ql-indent\" value=\"-1\" title=\"indent\" arial-label=\"indent\"></button>\r\n            <button class=\"ql-indent\" value=\"+1\" title=\"indent\" arial-label=\"indent\"></button>\r\n        </span>\r\n        <span class=\"ql-formats\">\r\n            <button class=\"ql-direction\" value=\"rtl\" title=\"direction\" arial-label=\"direction\"></button>\r\n            <select class=\"ql-align\" title=\"align\" arial-label=\"align\"></select>\r\n        </span>\r\n        <span class=\"ql-formats\">\r\n            <button class=\"ql-link\" title=\"link\" arial-label=\"link\"></button>\r\n            <button class=\"ql-image\" title=\"image\" arial-label=\"image\"></button>\r\n            <button class=\"ql-video\" title=\"Video\" arial-label=\"video\"></button>\r\n        </span>\r\n    </div>\r\n</ng-template>"], "mappings": ";AAAA,SAA4EA,YAAY,EAAgBC,MAAM,EAAEC,QAAQ,EAAyBC,MAAM,QAAmB,eAAe;AAEzL,SAASC,aAAa,QAA0B,uBAAuB;AAOvE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,OAAO,QAAQ,SAAS;AAEjC,SAASC,IAAI,QAAQ,gBAAgB;AAIrC,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,UAAU,QAAQ,gBAAgB;AAEjF,SAASC,iCAAiC,QAAQ,8GAA8G;AAEhK,SAASC,kBAAkB,EAAEC,gBAAgB,QAAgC,4CAA4C;AAIzH,SAASC,YAAY,QAAQ,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICWrBC,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAE,SAAA,YAAoC;IAC3EF,EAAA,CAAAG,MAAA,GAAqE;;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA5EJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAO,WAAA,OAAAC,MAAA,CAAAC,IAAA,CAAAC,MAAA,2BAAAF,MAAA,CAAAC,IAAA,CAAAE,QAAA,CAAAC,SAAA,KAAqE;;;;;IAYjFZ,EADJ,CAAAC,cAAA,cAAmD,cACnB;;IAExBD,EAAA,CAAAC,cAAA,cAC+B;IAC3BD,EAAA,CAAAE,SAAA,eAEqE;IAE7EF,EADI,CAAAI,YAAA,EAAM,EACJ;;IACNJ,EAAA,CAAAC,cAAA,cAA8C;IAC1CD,EAAA,CAAAG,MAAA,eACJ;IACJH,EADI,CAAAI,YAAA,EAAM,EACJ;;;;IANwCJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAa,WAAA,SAAAL,MAAA,CAAAM,eAAA,GAAgC;;;;;IAQ1Ed,EADJ,CAAAC,cAAA,cAAqD,cACrB;IACxBD,EAAA,CAAAE,SAAA,YAAuC;IAE3CF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA6C;IACzCD,EAAA,CAAAG,MAAA,kBACJ;IACJH,EADI,CAAAI,YAAA,EAAM,EACJ;;;;;IAINJ,EAAA,CAAAe,uBAAA,GAA+B;IAE3Bf,EADA,CAAAC,cAAA,eAAqH,eACvF;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,SAAA,YAAwC;IAAAF,EAAA,CAAAI,YAAA,EAAO;IACvEJ,EAAA,CAAAG,MAAA,GAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAHgCJ,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAAV,MAAA,CAAAW,QAAA,EAAoC;IAGlHnB,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAoB,kBAAA,MAAAZ,MAAA,CAAAa,6BAAA,CAAAb,MAAA,CAAAC,IAAA,MAAuC;;;;;IAE7CT,EAAA,CAAAe,uBAAA,GAAgC;IAEhCf,EADI,CAAAC,cAAA,eAAqH,eAC3F;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9CJ,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,SAAA,YAAwC;IAAAF,EAAA,CAAAI,YAAA,EAAO;IACvEJ,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAH6CJ,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAAV,MAAA,CAAAW,QAAA,EAAoC;IAGlHnB,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAoB,kBAAA,MAAAZ,MAAA,CAAAc,gBAAA,CAAAd,MAAA,CAAAC,IAAA,MAA0B;;;;;IAZpCT,EAAA,CAAAC,cAAA,cACuC;IAOnCD,EANA,CAAAuB,UAAA,IAAAC,kEAAA,0BAA+B,IAAAC,kEAAA,0BAMC;IAOpCzB,EAAA,CAAAI,YAAA,EAAM;;;;IAdFJ,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAAkB,oBAAA,GAAkC;IACnB1B,EAAA,CAAAK,SAAA,EAAc;IAAdL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAmB,QAAA,CAAc;IAMd3B,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAAgB,UAAA,UAAAR,MAAA,CAAAmB,QAAA,CAAe;;;;;;IAUtC3B,EAAA,CAAAC,cAAA,cAEiC;IAD7BD,EAAA,CAAA4B,UAAA,mBAAAC,yEAAAC,MAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAC,GAAA;MAAA,MAAAxB,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,EAAA1B,MAAA,CAAA2B,gBAAA,GAA6B3B,MAAA,CAAA4B,eAAA,CAAAN,MAAA,CAAuB,GAAG,IAAI;IAAA,EAAC;IAI5D9B,EAFA,CAAAE,SAAA,cAAgC,cACA,cACA;IACpCF,EAAA,CAAAI,YAAA,EAAM;;;;IAJFJ,EAAA,CAAAqC,sBAAA,uBAAA7B,MAAA,CAAAC,IAAA,CAAA6B,EAAA,KAA4B;;;;;IAQ5BtC,EAAA,CAAAE,SAAA,cAEoC;;;;IADhCF,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAA+B,sBAAA,GAAoC;;;;;IAExCvC,EAAA,CAAAE,SAAA,cAEoG;;;;IADhGF,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAA+B,sBAAA,GAAoC;;;;;;IAN5CvC,EAAA,CAAAC,cAAA,cACoC;IADND,EAAA,CAAA4B,UAAA,mBAAAY,yEAAA;MAAAxC,EAAA,CAAA+B,aAAA,CAAAU,GAAA;MAAA,MAAAjC,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAAkC,aAAA,CAAAlC,MAAA,CAAAC,IAAA,CAAmB;IAAA,EAAC;IAKvDT,EAHA,CAAAuB,UAAA,IAAAoB,yDAAA,kBAEoC,IAAAC,yDAAA,kBAGgE;IAExG5C,EAAA,CAAAI,YAAA,EAAM;;;;IAPIJ,EAAA,CAAAK,SAAA,EAAc;IAAdL,EAAA,CAAAgB,UAAA,UAAAR,MAAA,CAAAqC,OAAA,CAAc;IAGd7C,EAAA,CAAAK,SAAA,EAAa;IAAbL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAqC,OAAA,CAAa;;;;;;;IAaf7C,EAFJ,CAAAC,cAAA,cAAwC,sBAE8F;IAAhHD,EAAA,CAAA8C,gBAAA,2BAAAC,sFAAAjB,MAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAiB,GAAA;MAAA,MAAAxC,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAAjC,EAAA,CAAAiD,kBAAA,CAAAzC,MAAA,CAAAC,IAAA,CAAAyC,IAAA,EAAApB,MAAA,MAAAtB,MAAA,CAAAC,IAAA,CAAAyC,IAAA,GAAApB,MAAA;MAAA,OAAA9B,EAAA,CAAAkC,WAAA,CAAAJ,MAAA;IAAA,EAAuB;IAAC9B,EAAA,CAAA4B,UAAA,0BAAAuB,qFAAArB,MAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAiB,GAAA;MAAA,MAAAxC,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAgB1B,MAAA,CAAA4C,eAAA,CAAAtB,MAAA,CAAuB;IAAA,EAAC;IAC9E9B,EAAA,CAAAuB,UAAA,IAAA8B,iEAAA,0BAAgC;IAIxCrD,EAFI,CAAAI,YAAA,EAAW,EAET;;;;IALmGJ,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAsD,UAAA,CAAAtD,EAAA,CAAAuD,eAAA,IAAAC,GAAA,EAA4B;IAA/GxD,EAAA,CAAAyD,gBAAA,YAAAjD,MAAA,CAAAC,IAAA,CAAAyC,IAAA,CAAuB;IAA0ClD,EAAA,CAAAgB,UAAA,kBAAiB;;;;;IAiB5FhB,EAAA,CAAA0D,kBAAA,GAAqE;;;;;IAArE1D,EAAA,CAAAuB,UAAA,IAAAoC,gFAAA,2BAAsD;;;;;IAAvC3D,EAAA,CAAAgB,UAAA,qBAAA4C,sBAAA,CAAqC;;;;;;IAKxD5D,EAAA,CAAAC,cAAA,cAIgI;IAH5HD,EAAA,CAAA4B,UAAA,mBAAAiC,gFAAA;MAAA7D,EAAA,CAAA+B,aAAA,CAAA+B,GAAA;MAAA,MAAAtD,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAAuD,kBAAA,CAAAC,eAAA,CAAmC,IAAI,EAAAxD,MAAA,CAAAyD,SAAA,CAAY;IAAA,EAAC;IAI7DjE,EAAA,CAAAG,MAAA,0BACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;IAFFJ,EAAA,CAAAgB,UAAA,4HAA2H;;;;;;IAG/HhB,EAAA,CAAAC,cAAA,cACwF;IAApFD,EAAA,CAAA4B,UAAA,mBAAAsC,gFAAA;MAAAlE,EAAA,CAAA+B,aAAA,CAAAoC,GAAA;MAAA,MAAA3D,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAAuD,kBAAA,CAAAK,MAAA,CAAA5D,MAAA,CAAAC,IAAA,CAA+B;IAAA,EAAC;IACzCT,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAE,SAAA,YAA6B;IACxCF,EAAA,CAAAI,YAAA,EAAM;;;;;;IArBNJ,EAJR,CAAAC,cAAA,UAAwB,eAEK,cACA,cACY;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACnDJ,EAAA,CAAAC,cAAA,gBAA0F;IAArCD,EAAA,CAAA8C,gBAAA,2BAAAuB,mFAAAvC,MAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAuC,GAAA;MAAA,MAAA9D,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAAjC,EAAA,CAAAiD,kBAAA,CAAAzC,MAAA,CAAAC,IAAA,CAAA8D,KAAA,EAAAzC,MAAA,MAAAtB,MAAA,CAAAC,IAAA,CAAA8D,KAAA,GAAAzC,MAAA;MAAA,OAAA9B,EAAA,CAAAkC,WAAA,CAAAJ,MAAA;IAAA,EAAwB;IAEjF9B,EAFI,CAAAI,YAAA,EAA0F,EAExF;IACNJ,EAAA,CAAAC,cAAA,sBAAwI;IAAtHD,EAAA,CAAA8C,gBAAA,2BAAA0B,sFAAA1C,MAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAuC,GAAA;MAAA,MAAA9D,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAAjC,EAAA,CAAAiD,kBAAA,CAAAzC,MAAA,CAAAC,IAAA,CAAAyC,IAAA,EAAApB,MAAA,MAAAtB,MAAA,CAAAC,IAAA,CAAAyC,IAAA,GAAApB,MAAA;MAAA,OAAA9B,EAAA,CAAAkC,WAAA,CAAAJ,MAAA;IAAA,EAAuB;IAAwB9B,EAAA,CAAA4B,UAAA,0BAAA6C,qFAAA3C,MAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAuC,GAAA;MAAA,MAAA9D,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAgB1B,MAAA,CAAA4C,eAAA,CAAAtB,MAAA,CAAuB;IAAA,EAAC;IACrG9B,EAAA,CAAAuB,UAAA,IAAAmD,iEAAA,0BAAgC;IAIpC1E,EADA,CAAAI,YAAA,EAAW,EACJ;IACPJ,EAAA,CAAAC,cAAA,cAAyC;IAQrCD,EAPA,CAAAuB,UAAA,KAAAoD,0DAAA,kBAIgI,KAAAC,0DAAA,kBAIxC;IAIhG5E,EADI,CAAAI,YAAA,EAAM,EACJ;;;;IAzBIJ,EAAA,CAAAK,SAAA,EAAkB;IAAlBL,EAAA,CAAAgB,UAAA,cAAAR,MAAA,CAAAqE,IAAA,CAAkB;IAGiC7E,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAyD,gBAAA,YAAAjD,MAAA,CAAAC,IAAA,CAAA8D,KAAA,CAAwB;IAG0BvE,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAsD,UAAA,CAAAtD,EAAA,CAAAuD,eAAA,IAAAuB,GAAA,EAA4B;IAArH9E,EAAA,CAAAyD,gBAAA,YAAAjD,MAAA,CAAAC,IAAA,CAAAyC,IAAA,CAAuB;IAUhClD,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAC,IAAA,CAAAsE,SAAA,CAAAC,SAAA,IAAAxE,MAAA,CAAAyE,IAAA,CAAA3C,EAAA,CAAuC;IAKGtC,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAC,IAAA,CAAAsE,SAAA,CAAAC,SAAA,IAAAxE,MAAA,CAAAyE,IAAA,CAAA3C,EAAA,CAAuC;;;;;;IAlIlHtC,EAAA,CAAAe,uBAAA,GAAwC;IAK5Bf,EAHR,CAAAC,cAAA,aAAuE,aAE9B,aACiB;IAAzBD,EAAA,CAAA4B,UAAA,mBAAAsD,kEAAApD,MAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAoD,GAAA;MAAA,MAAA3E,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAA4E,MAAA,CAAAtD,MAAA,CAAc;IAAA,EAAC;IAGrC9B,EAFR,CAAAC,cAAA,aAA2C,aACrB,aACW;IACrBD,EAAA,CAAAE,SAAA,YAC8B;IAEtCF,EADI,CAAAI,YAAA,EAAM,EACJ;IAGEJ,EAFR,CAAAC,cAAA,cAAqF,cAC1C,eACS;IAAAD,EAAA,CAAAG,MAAA,IAAc;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGxDJ,EAFR,CAAAC,cAAA,eAAwC,eACgB,gBACzB;IAAAD,EAAA,CAAAE,SAAA,aAAuC;IAACF,EAAA,CAAAG,MAAA,IACtC;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChCJ,EAAA,CAAAuB,UAAA,KAAA8D,oDAAA,mBAA2C;IAOnErF,EALoB,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ,EACJ;IAIEJ,EAFR,CAAAC,cAAA,eAA6C,eAClB,eACQ;IAevBD,EAdA,CAAAuB,UAAA,KAAA+D,mDAAA,kBAAmD,KAAAC,mDAAA,kBAcE;IASzDvF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAuB,UAAA,KAAAiE,mDAAA,kBACuC;IAe3CxF,EAAA,CAAAI,YAAA,EAAM;IAUNJ,EARA,CAAAuB,UAAA,KAAAkE,mDAAA,kBAEiC,KAAAC,mDAAA,kBAOG;IAW5C1F,EAFI,CAAAI,YAAA,EAAM,EAEJ;IAGEJ,EAFR,CAAAC,cAAA,eAA2F,eAC7D,eACiC;IAUnDD,EATA,CAAAuB,UAAA,KAAAoE,mDAAA,kBAAwC,KAAAC,mDAAA,kBAShB;IAgCxC5F,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;IACNJ,EAAA,CAAAE,SAAA,qBAAkF;;;;;IA1IrCF,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAA6F,WAAA,WAAArF,MAAA,CAAAW,QAAA,CAAyB;IAE9DnB,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAAsF,kBAAA,GAAgC;IAKb9F,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA+F,eAAA,KAAAC,GAAA,GAAAxF,MAAA,CAAAW,QAAA,EAAAX,MAAA,CAAAW,QAAA,EAAqE;IAM5BnB,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAiG,iBAAA,CAAAzF,MAAA,CAAAC,IAAA,CAAA8D,KAAA,CAAc;IAGavE,EAAA,CAAAK,SAAA,GACtC;IADsCL,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAO,WAAA,SAAAC,MAAA,CAAAC,IAAA,CAAAyF,OAAA,qBACtC;IACDlG,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAC,IAAA,CAAAC,MAAA,CAAiB;IAY/CV,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAC,IAAA,CAAA0F,MAAA,CAAiB;IAcjBnG,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA4F,WAAA,GAAmB;IAUvBpG,EAAA,CAAAK,SAAA,EAAoD;IAApDL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAC,IAAA,CAAA4F,eAAA,CAAAC,MAAA,QAAA9F,MAAA,CAAA+F,aAAA,CAAoD;IAkBxDvG,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAgB,UAAA,UAAAR,MAAA,CAAA2B,gBAAA,CAAuB;IAQvBnC,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA2B,gBAAA,CAAsB;IAawBnC,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAAuF,GAAA,EAAAhG,MAAA,CAAAW,QAAA,EAA8B;IAEvDnB,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAqC,sBAAA,sBAAA7B,MAAA,CAAAC,IAAA,CAAA6B,EAAA,KAA2B;IAC5CtC,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAAgB,UAAA,UAAAR,MAAA,CAAAiG,QAAA,GAAiB;IASjBzG,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAiG,QAAA,GAAgB;IAiCTzG,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAgB,UAAA,UAAAR,MAAA,CAAAkG,iBAAA,CAA2B;;;;;;IAmC5C1G,EAAA,CAAAC,cAAA,cAC6D;IADDD,EAAA,CAAA4B,UAAA,mBAAA+E,yEAAA;MAAA3G,EAAA,CAAA+B,aAAA,CAAA6E,IAAA;MAAA,MAAApG,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAAqG,qBAAA,EAAuB;IAAA,EAAC;IAEzF7G,EAAA,CAAAE,SAAA,YAA2B;IAC/BF,EAAA,CAAAI,YAAA,EAAM;;;;IAF0BJ,EAAA,CAAAqC,sBAAA,uBAAA7B,MAAA,CAAAC,IAAA,CAAA6B,EAAA,KAA4B;;;;;IAmB5CtC,EAAA,CAAA0D,kBAAA,GAAqE;;;;;IAArE1D,EAAA,CAAAuB,UAAA,IAAAuF,0EAAA,2BAAsD;;;;;IAAvC9G,EAAA,CAAAgB,UAAA,qBAAA4C,sBAAA,CAAqC;;;;;IAG5D5D,EAAA,CAAAC,cAAA,cAA+C;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAK9DJ,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAGpEJ,EAAA,CAAAC,cAAA,cAE4F;IAAxFD,EAAA,CAAA4B,UAAA,mBAAAmF,yEAAA;MAAA/G,EAAA,CAAA+B,aAAA,CAAAiF,IAAA;MAAA,MAAAxG,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAAuD,kBAAA,CAAAkD,kCAAA,CAAAzG,MAAA,CAAAC,IAAA,EAA4D,KAAK,EAAAD,MAAA,CAAAyD,SAAA,CAAY;IAAA,EAAC;IACvFjE,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAE,SAAA,YAA6B;IAC9CF,EAAA,CAAAI,YAAA,EAAM;;;;;;IACNJ,EAAA,CAAAC,cAAA,cACqF;IAAjFD,EAAA,CAAA4B,UAAA,mBAAAsF,yEAAA;MAAAlH,EAAA,CAAA+B,aAAA,CAAAoF,IAAA;MAAA,MAAA3G,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAAuD,kBAAA,CAAAK,MAAA,CAAA5D,MAAA,CAAAC,IAAA,CAA+B;IAAA,EAAC;IACzCT,EAAA,CAAAG,MAAA,qCAA6B;IAAAH,EAAA,CAAAE,SAAA,YAA6B;IAC9DF,EAAA,CAAAI,YAAA,EAAM;;;;;;IAtE9BJ,EAAA,CAAAe,uBAAA,GAAwC;IAG5Bf,EAFR,CAAAC,cAAA,aAAuE,aACP,cACvB;IAAnBD,EAAA,CAAA4B,UAAA,mBAAAwF,kEAAA;MAAApH,EAAA,CAAA+B,aAAA,CAAAsF,IAAA;MAAA,MAAA7G,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAA4E,MAAA,EAAQ;IAAA,EAAC;IAGpBpF,EAFR,CAAAC,cAAA,aAA2C,aACrB,aACW;IACrBD,EAAA,CAAAE,SAAA,YAC8B;IAEtCF,EADI,CAAAI,YAAA,EAAM,EACJ;IAGEJ,EAFR,CAAAC,cAAA,cAA+F,cACnE,eACS;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClDJ,EAAA,CAAAC,cAAA,eAAwB;IACpBD,EAAA,CAAAE,SAAA,eAGM;IAK1BF,EAJgB,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;IAGFJ,EADJ,CAAAC,cAAA,eAA6C,eAClB;IACnBD,EAAA,CAAAE,SAAA,eACM;IACVF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAuB,UAAA,KAAA+F,mDAAA,kBAC6D;IAOrEtH,EAFI,CAAAI,YAAA,EAAM,EAEJ;IAMcJ,EALpB,CAAAC,cAAA,eAA2F,eAC7D,eACK,gBACE,eACI,eACY;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACnDJ,EAAA,CAAAE,SAAA,iBAAiE;IAErEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,oBAAoG;IAA3ED,EAAA,CAAA4B,UAAA,qBAAA2F,0EAAA;MAAAvH,EAAA,CAAA+B,aAAA,CAAAsF,IAAA;MAAA,MAAA7G,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAA1B,MAAA,CAAAgH,UAAA;IAAA,EAAsB;IAC3CxH,EAAA,CAAAuB,UAAA,KAAAkG,2DAAA,0BAAgC;IAGpCzH,EAAA,CAAAI,YAAA,EAAW;IACXJ,EAAA,CAAAuB,UAAA,KAAAmG,mDAAA,kBAA+C;IACnD1H,EAAA,CAAAI,YAAA,EAAO;IAEHJ,EADJ,CAAAC,cAAA,eAAyC,eAE8B;IAA/DD,EAAA,CAAA4B,UAAA,mBAAA+F,mEAAA;MAAA3H,EAAA,CAAA+B,aAAA,CAAAsF,IAAA;MAAA,MAAA7G,MAAA,GAAAR,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAS1B,MAAA,CAAAuD,kBAAA,CAAAC,eAAA,CAAmC,KAAK,EAAAxD,MAAA,CAAAyD,SAAA,CAAY;IAAA,EAAC;IAC9DjE,EAAA,CAAAG,MAAA,cAAK;IAAAH,EAAA,CAAAuB,UAAA,KAAAqG,oDAAA,kBAA6C;IAAe5H,EAAA,CAAAE,SAAA,aACtC;IAC/BF,EAAA,CAAAI,YAAA,EAAM;IAMNJ,EALA,CAAAuB,UAAA,KAAAsG,mDAAA,kBAE4F,KAAAC,mDAAA,kBAIP;IASzG9H,EANgB,CAAAI,YAAA,EAAM,EAEJ,EACJ,EAEJ,EACJ;;;;;IA5EuCJ,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAA6F,WAAA,WAAArF,MAAA,CAAAW,QAAA,CAAyB;IACvCnB,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAA8G,GAAA,EAAAvH,MAAA,CAAAW,QAAA,EAAgC;IAKpCnB,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA+F,eAAA,KAAAC,GAAA,GAAAxF,MAAA,CAAAW,QAAA,EAAAX,MAAA,CAAAW,QAAA,EAAqE;IAwB9EnB,EAAA,CAAAK,SAAA,IAAuB;IAAvBL,EAAA,CAAAgB,UAAA,UAAAR,MAAA,CAAA2B,gBAAA,CAAuB;IASuBnC,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAAuF,GAAA,EAAAhG,MAAA,CAAAW,QAAA,EAA8B;IAGxEnB,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAgB,UAAA,cAAAR,MAAA,CAAAqE,IAAA,CAAkB;IAMmD7E,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAsD,UAAA,CAAAtD,EAAA,CAAAuD,eAAA,KAAAyE,GAAA,EAA4B;IAAzFhI,EAAA,CAAAgB,UAAA,eAAc;IAKlBhB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAyH,aAAA,CAAmB;IAKTjI,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA0H,WAAA,IAAA1H,MAAA,CAAA2H,IAAA,cAAoC;IAG9CnI,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA4H,WAAA,CAAAC,SAAA,CAAgC;IAMSrI,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA0H,WAAA,IAAA1H,MAAA,CAAA2H,IAAA,cAAoC;;;;;IAiBnGnI,EADJ,CAAAC,cAAA,cAA4B,eACC;IAGrBD,EAFA,CAAAE,SAAA,iBACS,iBACwB;IACrCF,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,eAAyB;IAIrBD,EAHA,CAAAE,SAAA,iBAAiE,iBACM,iBACS,iBACT;IAC3EF,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,eAAyB;IAErBD,EADA,CAAAE,SAAA,kBAAoE,kBACe;IACvFF,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,gBAAyB;IAIzBD,EAHI,CAAAE,SAAA,kBAAqF,kBACA,kBACN,kBACA;IACnFF,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,gBAAyB;IAIrBD,EAHA,CAAAE,SAAA,kBAAiG,kBACG,kBAClB,kBACA;IACtFF,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,gBAAyB;IAErBD,EADA,CAAAE,SAAA,kBAA4F,kBACxB;IACxEF,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,gBAAyB;IAGrBD,EAFA,CAAAE,SAAA,kBAAiE,mBACG,mBACA;IAE5EF,EADI,CAAAI,YAAA,EAAO,EACL;;;ADvPV,OAAM,MAAOkI,oBAAoB;EA8C/BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,oBAA0C,EAC1CC,EAAqB,EACrBP,WAAwB,EACxBQ,YAA0B,EAC1BC,mBAAwC,EACxCC,aAA4B,EAC7B/E,kBAAsC;IARrC,KAAAyE,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAP,WAAW,GAAXA,WAAW;IACX,KAAAQ,YAAY,GAAZA,YAAY;IACZ,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAA/E,kBAAkB,GAAlBA,kBAAkB;IArDlB,KAAAtD,IAAI,GAAS,EAAU;IACvB,KAAAkB,QAAQ,GAAY,KAAK;IACzB,KAAA4E,aAAa,GAAY,KAAK;IAC9B,KAAAwC,IAAI,GAAW,EAAE;IAEjB,KAAA9E,SAAS,GAAc,EAAe;IACtC,KAAA9B,gBAAgB,GAAY,KAAK;IAChC,KAAA6G,aAAa,GAAG,IAAI/J,YAAY,EAAE;IAClC,KAAAgK,WAAW,GAAG,IAAIhK,YAAY,EAAE;IAC1C,KAAAiK,UAAU,GAAgB,EAAE;IAC5B,KAAAC,kBAAkB,GAAG,CAAC;IAEf,KAAAC,GAAG,GAAQ,EAAS;IACnB,KAAAC,IAAI,GAAG,IAAI9J,OAAO,EAAE;IAC5B,KAAA+J,UAAU,GAAY,KAAK;IAC3B,KAAA7C,QAAQ,GAAGrH,MAAM,CAAC,KAAK,CAAC;IACxB,KAAAyD,OAAO,GAAY,KAAK;IACjB,KAAA0G,mBAAmB,GAAG;MAC3BC,OAAO,EAAElK,WAAW,CAACmK,MAAM,GAAG;KAC/B;IACD,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,WAAW,GAAW,EAAE;IACjB,KAAA3E,IAAI,GAAS,EAAU;IACvB,KAAAkD,IAAI,GAAW,EAAE;IACjB,KAAA0B,MAAM,GAAY,IAAI;IAIrB,KAAAC,QAAQ,GAAG5K,MAAM,CAACC,QAAQ,CAAC;IAE5B,KAAA4K,KAAK,GAAW;MACrBhB,IAAI,EAAE,QAAQ;MACdiB,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EACpD,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,EACtD,WAAW,EAAE,WAAW,EAAE,GAAG,EAC7B,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EACvD,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM;KAC3C;IACD,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,SAAS,GAAY,KAAK;IACnB,KAAArF,IAAI,GAAqB,IAAIpF,gBAAgB,CAAC,EAAE,CAAC;IACxD,KAAAiH,iBAAiB,GAAe,EAAE;IAclC,KAAAvF,QAAQ,GAAY,KAAK;EAFrB;EAIJiE,MAAMA,CAAA;IACJ,IAAI,CAACjE,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAC9B,IAAI,CAAC0I,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;EAC5B;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACC,gCAAgC,EAAE;IACvC,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACxF,IAAI,GAAG,IAAIpF,gBAAgB,CAAC;MAC/B8E,KAAK,EAAE,IAAI7E,kBAAkB,CAAC,IAAI,EAAE;QAClC4K,UAAU,EAAE,CAAC3K,UAAU,CAAC4K,QAAQ;OACjC,CAAC;MACFrH,IAAI,EAAE,IAAIxD,kBAAkB,CAAC,IAAI,EAAE;QACjC4K,UAAU,EAAE,CAAC3K,UAAU,CAAC4K,QAAQ;OACjC;KACF,CAAC;IAEF,IAAI,CAACtF,IAAI,GAAG,IAAI,CAACmD,WAAW,CAACoC,eAAe,EAAE;IAC9C,IAAI,CAACrC,IAAI,GAAG,IAAI,CAAClD,IAAI,CAACkD,IAAK;IAC3B,IAAI,CAACpE,kBAAkB,CAAC0G,OAAO,CAAC,IAAI,CAAChK,IAAI,CAAC;IAC1C,IAAI,CAACsD,kBAAkB,CAAC2G,WAAW,CAAC,IAAI,CAAC7F,IAAI,CAAC;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA;IAGA,IAAI,IAAI,CAACkE,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAAC3D,MAAM,EAAE;IACf;IACA,IAAI,CAACsB,iBAAiB,GAAG,IAAI,CAACiE,gBAAgB,EAAE;IAChD,IAAI,CAACC,eAAe,EAAE;IAGtB7K,YAAY,CAAC,IAAI,CAAC0G,QAAQ,EAAE;MAC1BqD,QAAQ,EAAE,IAAI,CAACA;KAChB,CAAC,CAACe,SAAS,CAAC;MACXC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACC,eAAe,EAAE;QACxB;MACF;KACD,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACzK,IAAI,CAAC;EACrC;EAEA0K,kBAAkBA,CAAA;IAChB,IAAI,CAACxC,EAAE,CAACyC,aAAa,EAAE;EACzB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChC,IAAI,CAACiC,WAAW,EAAE;IACvB,IAAI,CAAC5C,oBAAoB,CAAC6C,eAAe,CAAC,KAAK,CAAC;EAClD;EAEAC,kBAAkBA,CAAA,GAClB;EAEAC,eAAeA,CAAA,GACf;EAEArJ,eAAeA,CAACsJ,KAAU;IACxB,IAAI,CAACC,KAAK,CAACC,IAAI,CAACF,KAAK,CAAC;EACxB;EAEAf,gBAAgBA,CAAA;IACd;IACA,IAAI,IAAI,CAAC5B,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,MAAM8C,IAAI,GAAG,IAAI;IAEjB,IAAI,CAAC5B,SAAS,GAAG,CACf;MACE3H,EAAE,EAAE,MAAM;MACVwJ,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,uBAAuB;MAC5CC,OAAO,EAAGN,KAAK,IAAI;QAEjBG,IAAI,CAAC1K,QAAQ,GAAG,IAAI;QACpB0K,IAAI,CAACpF,QAAQ,CAACwF,GAAG,CAAC,IAAI,CAAC;MACzB;KACD,EACD;MACE3J,EAAE,EAAE,OAAO;MACXwJ,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,uBAAuB;MAC7CC,OAAO,EAAGN,KAAK,IAAI;QACjB,IAAIG,IAAI,CAACzD,WAAW,CAACC,SAAS,EAAE;UAC9BwD,IAAI,CAACK,WAAW,CAAC;YAAEC,MAAM,EAAE,WAAW;YAAEC,GAAG,EAAE;cAAEnI,SAAS,EAAE4H,IAAI,CAAC5H;YAAS;UAAE,CAAE,EAAE,IAAI,CAAC;QACrF,CAAC,MAAM;UACL,IAAI,CAAC,IAAI,CAACuE,cAAc,CAAC6D,eAAe,CAACR,IAAI,CAAC5H,SAAS,CAAC,IAAI4H,IAAI,CAAC5H,SAAS,CAACqI,iBAAiB,CAAChG,MAAM,KAAK,CAAC,EAAE;YACzG,MAAMiG,IAAI,GAAyB;cACjCC,MAAM,EAAE,IAAI,CAAC/L,IAAI,CAAC6B,EAAE;cACpBmK,WAAW,EAAE,CAACZ,IAAI,CAAC5H,SAAS,CAAC3B,EAAE;cAC/BoK,UAAU,EAAE,CAAEb,IAAI,CAAC5H,SAAS,CAACqI,iBAAiB,CAAC,CAAC,CAAC,CAACtH,SAAU,CAAE2H,QAAQ,EAAE;aACzE;YACD;YACA,IAAI,CAAC5I,kBAAkB,CAAC6I,SAAS,CAACL,IAAI,EAAE,sBAAsB,CAAC;UACjE,CAAC,MAAM;YACLV,IAAI,CAACgB,uBAAuB,CAACjN,iCAAiC,EAC5D;cACEuM,MAAM,EAAE,WAAW;cACnBW,cAAc,EAAE,gBAAgB;cAChCC,WAAW,EAAE,oBAAoB;cACjCC,oBAAoB,EAAEnB,IAAI,CAAC5H;aAC5B,CAAC;UACN;QACF;MACF;KACD,EACD;MACE3B,EAAE,EAAE,WAAW;MACfwJ,KAAK,EAAE,IAAI,CAAC1F,WAAW,EAAE,GAAG,aAAa,GAAG,WAAW;MAAE2F,IAAI,EAAE,kBAAkB;MACjFC,OAAO,EAAGN,KAAK,IAAI;QACjBuB,OAAO,CAACC,GAAG,CAACxB,KAAK,CAAC;QAClB,IAAI,CAAC3H,kBAAkB,CAACoJ,QAAQ,CAAC,IAAI,CAAC1M,IAAI,EAAE,IAAI,CAACwE,IAAI,EAAE,IAAI,CAACmB,WAAW,EAAE,CAAC;MAC5E;KACD,EACD;MACE9D,EAAE,EAAE,KAAK;MACTwJ,KAAK,EAAED,IAAI,CAACpL,IAAI,CAAC0F,MAAM,GAAG,OAAO,GAAG,KAAK;MAAE4F,IAAI,EAAE,wBAAwB;MACzEC,OAAO,EAAGN,KAAK,IAAI;QACjBG,IAAI,CAAC9H,kBAAkB,CAACqJ,GAAG,CAACvB,IAAI,CAACpL,IAAI,CAAC;MACxC;KACD,EACD;MACE6B,EAAE,EAAE,WAAW;MACfwJ,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE,kBAAkB;MAC5CC,OAAO,EAAGN,KAAK,IAAI;QACjBuB,OAAO,CAACC,GAAG,CAACxB,KAAK,CAAC;QAClBG,IAAI,CAAC9H,kBAAkB,CAACsJ,SAAS,CAACxB,IAAI,CAACpL,IAAI,EAAEoL,IAAI,CAAC5H,SAAS,CAAC;MAC9D;KACD,EACD;MACE3B,EAAE,EAAE,MAAM;MACVwJ,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,yBAAyB;MAC9CC,OAAO,EAAGN,KAAK,IAAI;QACjBG,IAAI,CAACgB,uBAAuB,CAACjN,iCAAiC,EAC5D;UACEuM,MAAM,EAAE,MAAM;UACdW,cAAc,EAAE,YAAY;UAC5BC,WAAW,EAAE;SACd,CAAC;MACN;KACD,EACD;MACEzK,EAAE,EAAE,YAAY;MAChBwJ,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE,yBAAyB;MACtDC,OAAO,EAAGN,KAAK,IAAI;QACjBG,IAAI,CAACgB,uBAAuB,CAACjN,iCAAiC,EAC5D;UACEuM,MAAM,EAAE,gBAAgB;UACxBW,cAAc,EAAE,oBAAoB;UACpCC,WAAW,EAAE;SACd,CAAC;MACN;KACD,EACD;MACEzK,EAAE,EAAE,UAAU;MACdwJ,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,4BAA4B;MACrDC,OAAO,EAAGN,KAAK,IAAI;QACjBG,IAAI,CAAC9H,kBAAkB,CAACuJ,QAAQ,CAACzB,IAAI,CAACpL,IAAI,CAAC;MAC7C;KACD,EACD;MACE6B,EAAE,EAAE,SAAS;MACbwJ,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,uBAAuB;MAC/CC,OAAO,EAAGN,KAAK,IAAI;QACjB,IAAI6B,WAAW,GAAU,EAAE;QAC3B,IAAIC,WAAW,GAAW3B,IAAI,CAACpL,IAAI,CAAC4F,eAAe;QACnD,IAAIoH,KAAK,GAAW,EAAE;QAEtB,IAAI5B,IAAI,CAACzD,WAAW,CAACC,SAAS,EAAE;UAC9B,IAAI,IAAI,CAAC1G,QAAQ,EAAE;YACjB4L,WAAW,CAACG,IAAI,CAAC7B,IAAI,CAAC5H,SAAS,CAAC0J,OAAQ,CAAC3I,SAAS,CAAC;UACrD,CAAC,MAAM;YACLuI,WAAW,CAACG,IAAI,CAAC7B,IAAI,CAAC5G,IAAI,CAACD,SAAS,CAAC;UACvC;QACF,CAAC,MAAM;UACL,KAAK,IAAIC,IAAI,IAAIuI,WAAW,EAAE;YAC5BD,WAAW,CAACG,IAAI,CAACzI,IAAI,CAACD,SAAS,CAAC;UAClC;QACF;QAEA,MAAMuH,IAAI,GAAyB;UACjCC,MAAM,EAAEX,IAAI,CAACpL,IAAI,CAAC6B,EAAE;UACpBmK,WAAW,EAAE,CAACZ,IAAI,CAAC5H,SAAS,CAAC3B,EAAE;UAC/BoK,UAAU,EAAEa;SACb;QACD1B,IAAI,CAAC9H,kBAAkB,CAAC6J,WAAW,CAACrB,IAAI,EAAE,wBAAwB,CAAC;QACnE,IAAIV,IAAI,CAACzD,WAAW,CAACyF,SAAS,IAAI,IAAI,CAAClM,QAAQ,IAAI,IAAI,CAAClB,IAAI,CAAC0F,MAAM,EAAE;UACnE0F,IAAI,CAAC9H,kBAAkB,CAACqJ,GAAG,CAAC,IAAI,CAAC3M,IAAI,CAAC;QACxC;MACF;KACD,EACD;MACE6B,EAAE,EAAE,QAAQ;MACZwJ,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,mBAAmB;MAC1CC,OAAO,EAAGN,KAAK,IAAI;QACjBG,IAAI,CAAC9H,kBAAkB,CAAC+J,UAAU,CAACjC,IAAI,CAACpL,IAAI,CAAC;MAC/C;KACD,CACF;IAED,OAAO,IAAI,CAACwJ,SAAS;EACvB;EAGA;;;;;;EAMQiB,mBAAmBA,CAAC6C,IAAU;IACpC,OAAO,IAAI,CAACrH,iBAAiB;EAC/B;EAEAsH,eAAeA,CAAChE,KAAiB,EAAEiE,WAAqB,EAAEC,QAAoB;IAE5E;IACA,MAAMC,gBAAgB,GAAGnE,KAAK,CAACoE,MAAM,CAACL,IAAI,IAAI,CAACE,WAAW,CAACI,QAAQ,CAACN,IAAI,CAACzL,EAAG,CAAC,CAAC;IAE9E;IACA,IAAI,CAACoE,iBAAiB,GAAG,CAAC,GAAGyH,gBAAgB,EAAE,GAAGD,QAAQ,CAAC;IAE3DjB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxG,iBAAiB,CAAC;IACnC,OAAO,IAAI,CAACA,iBAAiB;EAC/B;EACA;;;;;;EAMApF,gBAAgBA,CAACb,IAAU;IACzB,IAAI+M,WAAW,GAAG/M,IAAI,CAACsE,SAAS;IAChC,IAAI0I,KAAK,GAAW,EAAE;IACtBA,KAAK,IAAID,WAAW,CAAC5M,SAAS,GAAG,GAAG;IAEpC,OAAO6M,KAAK;EACd;EAEA;;;;;;EAMApM,6BAA6BA,CAACZ,IAAU;IACtC,IAAI+M,WAAW,GAAW/M,IAAI,CAAC4F,eAAe;IAC9C,IAAIoH,KAAK,GAAW,EAAE;IACtB,KAAK,IAAIxI,IAAI,IAAIuI,WAAW,EAAE;MAC5BC,KAAK,IAAIxI,IAAI,CAACrE,SAAS,GAAG,IAAI;IAChC;IACA6M,KAAK,GAAGA,KAAK,CAACa,SAAS,CAAC,CAAC,EAAEb,KAAK,CAACnH,MAAM,GAAG,CAAC,CAAC;IAE5C,OAAOmH,KAAK;EACd;EAEA;;;;;;;EAOAc,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAC5M,QAAQ,EAAE;MAClB,IAAI,IAAI,CAAClB,IAAI,CAAC4F,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEA;;;EAGAkI,gBAAgBA,CAAA;IACd,KAAK,IAAIvJ,IAAI,IAAI,IAAI,CAACxE,IAAI,CAAC4F,eAAe,EAAE;MAC1C,IAAIoI,YAAY,GAAqB;QACnClK,KAAK,EAAE,aAAa,IAAI,CAAC9D,IAAI,CAAC8D,KAAK,gCAAgC;QACnEmK,WAAW,EAAE,gEAAgE;QAC7EC,YAAY,EAAE1J,IAAI,CAACD,SAAU;QAC7B+D,IAAI,EAAE,MAAM;QACZ6F,MAAM,EAAE;OACT;MACD,IAAI,CAACvF,IAAI,CAACwF,IAAI,GAAG,IAAI,CAAChG,mBAAmB,CAACiG,MAAM,CAACL,YAAY,CAAC,CAACM,IAAI,CAACvP,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqL,SAAS,CAACmE,GAAG,IAAG;QAC3F/B,OAAO,CAACC,GAAG,CAAC8B,GAAG,CAAC;QAChB,IAAI,CAACpG,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EAEA;;;;;EAKAhJ,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC3F,IAAI,CAAC4O,YAAY,CAACC,IAAI,CAAErK,IAAU,IAAKA,IAAI,CAACD,SAAS,IAAI,IAAI,CAACoD,WAAW,CAACmH,SAAS,EAAE,CAAC;EACpG;EAEA;;;;;;;EAOArD,WAAWA,CAACR,KAAU,EAAErD,SAAS,GAAG,KAAK;IACvC,IAAI,CAACtE,kBAAkB,CAACmI,WAAW,CAAC,IAAI,CAACzL,IAAI,EAAEiL,KAAK,EAAErD,SAAS,CAAC;EAClE;EAEAmH,KAAKA,CAAC9D,KAAU;IACd,IAAI,CAAChC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,KAAK;EAC/B;EAEA8F,YAAYA,CAACC,KAAa;IACxB,OAAS,IAAI,CAAC7K,IAAI,CAAC8K,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAO,IAAI,IAAI,CAAC/K,IAAI,CAAC8K,GAAG,CAACD,KAAK,CAAC,EAAEG,OAAO,IAAM,IAAI,CAAC3F,SAAS,IAAI,IAAI,CAACrF,IAAI,CAAC8K,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAQ;EAC/H;EAEAE,WAAWA,CAAA;IACT;EAAA;EAGFjJ,qBAAqBA,CAAA;IACnB,IAAI,CAACmC,aAAa,CAAC+G,IAAI,CAAC,EAAE,CAAC;EAC7B;EAEA;;;;;;EAMAjP,eAAeA,CAAA;IACb,OAAO,IAAI,CAACsF,WAAW,EAAE,IAAK,IAAI,CAAC3F,IAAI,CAAC0F,MAAO,GAAG,SAAS,GAAG,SAAS;EACzE;EAEAL,kBAAkBA,CAAA;IAChB,OAAO;MACL,mBAAmB,EAAE,IAAI,CAAC3E,QAAQ;MAClC,QAAQ,EAAE,IAAI,CAACiF,WAAW,EAAE;MAC5B,YAAY,EAAE,IAAI,CAAC3F,IAAI,CAAC0F,MAAM;MAC9B,UAAU,EAAE,IAAI,CAAC1F,IAAI,CAAC0F,MAAM;MAC5B,sBAAsB,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,IAAI,CAAC3F,IAAI,CAAC0F;KACzD;EACH;EAEAzE,oBAAoBA,CAAA;IAClB,OAAO;MACL,YAAY,EAAE,IAAI,CAACjB,IAAI,CAAC0F,MAAM,IAAI,IAAI,CAACC,WAAW;KACnD;EACH;EAEA;;;;;EAKA7D,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACpB,QAAQ,IAAI,IAAI,CAACiF,WAAW,EAAE,IAAI,IAAI,CAAC3F,IAAI,CAAC0F,MAAM,GAC1D,cAAc,GACd,EAAE;EACR;EAEAzD,aAAaA,CAACsN,EAAO;IACnB,IAAI,CAACnN,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,IAAI,CAACA,OAAO,EAAE;MAChB;MACA,IAAI,CAACoG,WAAW,CAAC8G,IAAI,CAAC;QAAElN,OAAO,EAAE,IAAI;QAAEpC,IAAI,EAAE,IAAI,CAACA;MAAI,CAAE,CAAC;IAC3D,CAAC,MAAM;MACL,IAAI,CAACwI,WAAW,CAAC8G,IAAI,CAAC;QAAElN,OAAO,EAAE,KAAK;QAAEpC,IAAI,EAAE,IAAI,CAACA;MAAI,CAAE,CAAC;IAC5D;EACF;EAEQoM,uBAAuBA,CAACoD,SAAc,EAAEC,UAAgB,EAAEC,KAAK,GAAG,GAAG;IAE3E,IAAI,CAAC3H,cAAc,CAACqE,uBAAuB,CAAC,IAAI,CAAC/D,aAAa,EAAEmH,SAAS,EAAE,GAAG,EAAEC,UAAU,EAAE,IAAI,EAAGE,MAAW,IAAI;MAChHnD,OAAO,CAACC,GAAG,CAACkD,MAAM,CAAC;MACnB,IAAIA,MAAM,IAAIA,MAAM,CAACjE,MAAM,KAAK,WAAW,EAAE;QAC3C,IAAI,CAAC,IAAI,CAAC5F,aAAa,IAAI,IAAI,CAAC6B,WAAW,CAACyF,SAAS,EAAE;UACrD,IAAI,CAAC9J,kBAAkB,CAACsM,iBAAiB,CAAC,IAAI,CAAC5P,IAAI,EAAE2P,MAAM,CAAC;QAC9D,CAAC,MAAM;UACL,IAAI,CAAClE,WAAW,CAACkE,MAAM,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACjE,MAAM,KAAK,MAAM,EAAE;QAC7C,IAAI,CAACD,WAAW,CAACkE,MAAM,CAAC;MAC1B,CAAC,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACjE,MAAM,KAAK,gBAAgB,EAAE;QACvD,IAAI,CAACD,WAAW,CAACkE,MAAM,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ;EAEQhG,gCAAgCA,CAAA;IACtC,IAAI,CAACf,IAAI,CAACiH,GAAG,CAAC,IAAI,CAACvM,kBAAkB,CAACwM,yBAAyB,CAAC1F,SAAS,CAAE2F,WAAW,IAAI;MACxF,IAAIA,WAAW,EAAE;QACf,IAAI,CAACxH,aAAa,CAAC+G,IAAI,CAAC,EAAE,CAAC;QAC3B,IAAI,CAAChM,kBAAkB,CAAC0M,uBAAuB,CAAC,KAAK,CAAC;MACxD;IACF,CAAC,CAAC,CAAC;EACL;EAEQpG,sBAAsBA,CAAA;IAC5B,IAAI,CAAChB,IAAI,CAACiH,GAAG,CAAC,IAAI,CAACvM,kBAAkB,CAAC2M,UAAU,CAAC7F,SAAS,CAAE8F,gBAAwC,IAAI;MACtG,IAAI,CAACA,gBAAgB,EAAE;QACrB;MACF;MACA,IAAI,CAAC,IAAI,CAACnI,cAAc,CAAC6D,eAAe,CAACsE,gBAAgB,CAAC,EAAE;QAC1D1D,OAAO,CAACC,GAAG,CAACyD,gBAAgB,CAAC;QAC7B,IAAI,CAACC,iBAAiB,CAACD,gBAAgB,CAAC;MAC1C;IACF,CAAC,CAAC,CAAC;EACL;EAEQC,iBAAiBA,CAACnQ,IAA4B;IACpD,IAAI,CAACA,IAAI,GAAGA,IAAI,CAACA,IAAI;IAErB,QAAQA,IAAI,CAAC0L,MAAM;MACjB,KAAKrM,gBAAgB,CAAC+Q,MAAM;QAC1B,IAAI,CAACpK,QAAQ,CAACwF,GAAG,CAAC,KAAK,CAAC;QACxB;MACF,KAAKnM,gBAAgB,CAACgR,GAAG;QACvB;MACF,KAAKhR,gBAAgB,CAACiR,SAAS;QAC7B,IAAI,IAAI,CAAC3K,WAAW,EAAE,EAAE;UACtB,IAAI4K,KAAK,GAAG,IAAI,CAACvQ,IAAI,CAAC4O,YAAY,CAAC4B,SAAS,CACzCjB,EAAE,IAAKA,EAAE,CAAC1N,EAAE,KAAK,IAAI,CAAC8F,WAAW,CAACmH,SAAS,EAAE,CAC/C;UACD,IAAI,CAAC9O,IAAI,CAAC4O,YAAY,CAAC6B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACzC,CAAC,MAAM;UACL,IAAIG,SAAS,GAAG,IAAI,CAAC/I,WAAW,CAACoC,eAAe,EAAE;UAClD2G,SAAS,CAACnM,SAAS,GAAGmM,SAAS,CAAC7O,EAAE;UAClC,IAAI,CAAC7B,IAAI,CAAC4O,YAAY,CAAC3B,IAAI,CAACyD,SAAS,CAAC;QACxC;QACA;IACJ;IAEA,IAAI,CAACjG,mBAAmB,CAAC,IAAI,CAACzK,IAAI,CAAC;IACnC,IAAI,CAACiG,iBAAiB,GAAG,IAAI,CAACiE,gBAAgB,EAAE;IAChD,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACnC,WAAW,CAAC2I,qBAAqB,CAAC,IAAI,CAAC;EAC9C;EAEA;;;;;;;;;;EAUQxG,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC3G,SAAS,IAAI,CAAC,IAAI,CAACxD,IAAI,EAAE;MACjC;IACF;IACA,MAAM4Q,gBAAgB,GAAG,IAAI,CAAC5I,WAAW,CAAC6I,0BAA0B,CAAC,IAAI,CAAC7Q,IAAI,EAAE,IAAI,CAAC8Q,IAAI,EAAE,IAAI,CAACtN,SAAS,CAAC;IAE1G,MAAMuN,WAAW,GAAG,CAAC,IAAI,CAAChJ,cAAc,CAAC6D,eAAe,CAAC,IAAI,CAACpI,SAAS,CAAC;IACxE,MAAMwN,WAAW,GAAG,IAAI,CAACzD,eAAe,CACtC,IAAI,CAACrD,gBAAgB,EAAE,EACvB0G,gBAAgB,EAChB,EAAE,CACH;EACH;EAEArG,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC0G,MAAM,EAAE;MAChB;IACF;IAEA,MAAMC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACE,QAAQ,EAAE,CAAC,CAAC;IAEtC;IACAD,KAAK,CAACE,SAAS,CAAC,SAAS,CAAC,CAACC,UAAU,CAAC,OAAO,EAAE,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC9E;EAEMD,YAAYA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MAChB,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAC7CF,KAAK,CAACG,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;MAClCH,KAAK,CAACG,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;MACvCH,KAAK,CAACI,KAAK,EAAE;MAEbJ,KAAK,CAACK,QAAQ,gBAAAN,iBAAA,CAAG,aAAW;QAC1B,MAAMO,IAAI,GAAGN,KAAK,CAACO,KAAK,GAAG,CAAC,CAAC;QAC7B,IAAID,IAAI,EAAE;UACR;UACA,IAAIA,IAAI,CAACE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;YAAE;YACjCC,KAAK,CAAC,kEAAkE,CAAC;YACzE,OAAO,CAAC;UACV;UAEA,IAAI;YACF,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;YAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;cACpB,MAAMC,QAAQ,GAAGD,CAAC,CAACE,MAAM,EAAE9C,MAAgB;cAC3C,MAAM+C,KAAK,GAAGlB,KAAI,CAACP,MAAO,CAACE,QAAQ,EAAE,CAACwB,YAAY,CAAC,IAAI,CAAC;cACxDnB,KAAI,CAACP,MAAO,CAACE,QAAQ,EAAE,CAACyB,WAAW,CAACF,KAAK,CAACnC,KAAK,EAAE,OAAO,EAAEiC,QAAQ,EAAE,MAAM,CAAC;YAC7E,CAAC;YACDJ,MAAM,CAACS,aAAa,CAACb,IAAI,CAAC;UAC5B,CAAC,CAAC,OAAOc,KAAK,EAAE;YACdtG,OAAO,CAACsG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC9C;QACF;MACF,CAAC;IAAC;EACJ;EAEMC,YAAYA,CAAC9H,KAAqB;IAAA,IAAA+H,MAAA;IAAA,OAAAvB,iBAAA;MACtC,MAAMlI,KAAK,GAAG0B,KAAK,CAACgI,aAAa,EAAE1J,KAAK;MACxC,IAAIA,KAAK,EAAE;QACT,KAAK,IAAI2J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3J,KAAK,CAAC1D,MAAM,EAAEqN,CAAC,EAAE,EAAE;UACrC,MAAM5F,IAAI,GAAG/D,KAAK,CAAC2J,CAAC,CAAC;UACrB,IAAI5F,IAAI,CAAChF,IAAI,CAAC6K,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;YACrClI,KAAK,CAACmI,cAAc,EAAE,CAAC,CAAC;YACxB,MAAMpB,IAAI,GAAG1E,IAAI,CAAC+F,SAAS,EAAE;YAC7B,IAAIrB,IAAI,EAAE;cACR;cACA,IAAIA,IAAI,CAACE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;gBAAE;gBACjCC,KAAK,CAAC,sEAAsE,CAAC;gBAC7E,OAAO,CAAC;cACV;cAEA,IAAI;gBACF,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;gBAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;kBACpB,MAAMC,QAAQ,GAAGD,CAAC,CAACE,MAAM,EAAE9C,MAAgB;kBAC3C,MAAM+C,KAAK,GAAGM,MAAI,CAAC/B,MAAO,CAACE,QAAQ,EAAE,CAACwB,YAAY,CAAC,IAAI,CAAC;kBACxDK,MAAI,CAAC/B,MAAO,CAACE,QAAQ,EAAE,CAACyB,WAAW,CAACF,KAAK,CAACnC,KAAK,EAAE,OAAO,EAAEiC,QAAQ,EAAE,MAAM,CAAC;gBAC7E,CAAC;gBACDJ,MAAM,CAACS,aAAa,CAACb,IAAI,CAAC;cAC5B,CAAC,CAAC,OAAOc,KAAK,EAAE;gBACdtG,OAAO,CAACsG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;cACrD;YACF;UACF;QACF;MACF;IAAC;EACH;EAEAnQ,eAAeA,CAAC2Q,OAAY;IAC1B9G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6G,OAAO,CAAC;IAEzC;IACA,MAAMC,MAAM,GAAG,IAAIC,SAAS,EAAE;IAC9B,MAAMC,GAAG,GAAGF,MAAM,CAACG,eAAe,CAACJ,OAAO,CAACK,SAAS,EAAE,WAAW,CAAC;IAElE;IACA,MAAMC,MAAM,GAAGH,GAAG,CAACI,gBAAgB,CAAC,KAAK,CAAC;IAC1C,IAAIC,cAAc,GAAG,KAAK;IAE1BF,MAAM,CAACG,OAAO,CAAEC,GAAG,IAAI;MACrB,MAAMC,GAAG,GAAGD,GAAG,CAACE,YAAY,CAAC,KAAK,CAAC;MAEnC;MACA,IAAID,GAAG,IAAIA,GAAG,CAACE,UAAU,CAAC,YAAY,CAAC,EAAE;QACvC;QACA,MAAMC,YAAY,GAAGH,GAAG,CAACpO,MAAM,IAAIoO,GAAG,CAACd,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxD,MAAMkB,OAAO,GAAIJ,GAAG,CAACK,MAAM,CAACL,GAAG,CAACpO,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAI,CAAC,GAAIoO,GAAG,CAACK,MAAM,CAACL,GAAG,CAACpO,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAI,CAAC,GAAG,CAAC;QACvG,MAAM0O,QAAQ,GAAIH,YAAY,GAAG,IAAI,GAAIC,OAAO,CAAC,CAAC;QAElD;QACA,IAAIE,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;UAAE;UAChC;UACAP,GAAG,CAACQ,MAAM,EAAE;UACZV,cAAc,GAAG,IAAI;QACvB;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAIA,cAAc,EAAE;MAClB,MAAMW,WAAW,GAAGhB,GAAG,CAACiB,IAAI,CAACC,SAAS;MACtCnI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgI,WAAW,CAAC;MAE5C;MACAnB,OAAO,CAACK,SAAS,GAAGc,WAAW;MAE/B;MACAtC,KAAK,CAAC,8DAA8D,CAAC;IACvE;EACF;EAAC,QAAAyC,CAAA,G;qBA9oBU/M,oBAAoB,EAAAtI,EAAA,CAAAsV,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxV,EAAA,CAAAsV,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1V,EAAA,CAAAsV,iBAAA,CAAAK,EAAA,CAAAC,oBAAA,GAAA5V,EAAA,CAAAsV,iBAAA,CAAAtV,EAAA,CAAA6V,iBAAA,GAAA7V,EAAA,CAAAsV,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA/V,EAAA,CAAAsV,iBAAA,CAAAU,EAAA,CAAAC,YAAA,GAAAjW,EAAA,CAAAsV,iBAAA,CAAAY,EAAA,CAAAC,mBAAA,GAAAnW,EAAA,CAAAsV,iBAAA,CAAAc,EAAA,CAAA/W,aAAA,GAAAW,EAAA,CAAAsV,iBAAA,CAAAe,EAAA,CAAAxW,kBAAA;EAAA;EAAA,QAAAyW,EAAA,G;UAApBhO,oBAAoB;IAAAiO,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;qCAHpB,CAACrX,aAAa,EAAEQ,kBAAkB,CAAC,GAAAG,EAAA,CAAA4W,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCoNhD1W,EAnOA,CAAAuB,UAAA,IAAA2V,4CAAA,4BAAwC,IAAAC,4CAAA,4BAgJA,IAAAC,2CAAA,iCAAApX,EAAA,CAAAqX,sBAAA,CAmFN;;;QAnOnBrX,EAAA,CAAAgB,UAAA,SAAA2V,GAAA,CAAA5N,IAAA,cAAuB;QAgJvB/I,EAAA,CAAAK,SAAA,EAAuB;QAAvBL,EAAA,CAAAgB,UAAA,SAAA2V,GAAA,CAAA5N,IAAA,cAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}