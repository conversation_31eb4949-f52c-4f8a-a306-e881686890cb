{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { inject, Injector, signal } from '@angular/core';\nimport { ButtonModule } from \"primeng/button\";\nimport { ChatService } from \"src/app/core/services/chat.service\";\nimport { LibraryModule } from \"src/app/modules/library/library.module\";\nimport { SharedModule } from \"src/app/shared/shared.module\";\nimport { toObservable } from '@angular/core/rxjs-interop';\nimport { LibraryService } from \"src/app/core/services/library.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"primeng/button\";\nimport * as i3 from \"../../../../shared/block-viewer/block-viewer.component\";\nimport * as i4 from \"../../../library/library/library.component\";\nconst ChatUploadFileDialogComponent_Defer_2_DepsFn = () => [i2.ButtonDirective, i3.BlockViewerComponent, i4.LibraryComponent];\nfunction ChatUploadFileDialogComponent_Defer_0_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵelement(2, \"div\", 6);\n    i0.ɵɵelementStart(3, \"div\", 7)(4, \"span\", 8);\n    i0.ɵɵtext(5, \" Please select files to send to chat. \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ChatUploadFileDialogComponent_Defer_0_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ChatUploadFileDialogComponent_Defer_0_Conditional_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubmitFilesToUpload());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"rounded\", true);\n  }\n}\nfunction ChatUploadFileDialogComponent_Defer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-block-viewer\", 0);\n    i0.ɵɵlistener(\"closeDialogEvent\", function ChatUploadFileDialogComponent_Defer_0_Template_app_block_viewer_closeDialogEvent_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDialogClose());\n    });\n    i0.ɵɵelement(1, \"app-library\");\n    i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"div\", 3);\n    i0.ɵɵtemplate(5, ChatUploadFileDialogComponent_Defer_0_Conditional_5_Template, 6, 0, \"div\", 4)(6, ChatUploadFileDialogComponent_Defer_0_Conditional_6_Template, 1, 1);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"header\", \"Send from Library to Chat\")(\"headerClass\", \"justify-content-center my-0\")(\"headerTextClass\", \"font-base font-semibold justify-content-center capitalize\")(\"showCloseDialogIcon\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵconditional(5, ctx_r1.checkedFilesToUpload().length === 0 ? 5 : 6);\n  }\n}\nfunction ChatUploadFileDialogComponent_DeferPlaceholder_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ChatUploadFileDialogComponent {\n  constructor(ref, config) {\n    this.ref = ref;\n    this.config = config;\n    this.chatService = inject(ChatService);\n    this.libraryService = inject(LibraryService);\n    this.dialogData = {};\n    this.showUploadFile = false;\n    this.checkedFilesToUpload = signal([]);\n    this.injector = inject(Injector);\n  }\n  ngOnInit() {\n    this.chatService.showUploadFile.set(true);\n    this.dialogData = this.config.data.dialogData;\n    console.log(this.dialogData);\n    this.checkedFilesToUpload.set([]);\n    toObservable(this.libraryService.checkedLibraryFiles, {\n      injector: this.injector\n    }).subscribe({\n      next: data => {\n        console.log(data);\n        this.checkedFilesToUpload.set(data);\n      }\n    });\n  }\n  onSubmitFilesToUpload() {\n    this.ref.close(this.checkedFilesToUpload());\n    this.checkedFilesToUpload.set([]);\n    this.ref.destroy();\n    this.chatService.showUploadFile.set(false);\n  }\n  onDialogClose(data) {\n    alert(data);\n    this.chatService.showUploadFile.set(false);\n    this.libraryService.checkedLibraryFiles.set([]);\n    this.checkedFilesToUpload.set([]);\n    this.ref.close(this.ref);\n    this.ref.destroy();\n  }\n  ngOnDestroy() {\n    this.chatService.showUploadFile.set(false);\n    this.checkedFilesToUpload.set([]);\n    this.ref.destroy();\n  }\n  static #_ = this.ɵfac = function ChatUploadFileDialogComponent_Factory(t) {\n    return new (t || ChatUploadFileDialogComponent)(i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatUploadFileDialogComponent,\n    selectors: [[\"app-chat-upload-file-dialog\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 4,\n    vars: 0,\n    consts: [[\"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"border-radius-bottom-10 mb-0\", \"headerBlockClass\", \"py-1 border-round-lg bg-cover \", \"containerClass\", \"bg-white px-3 py-2 relative\", 3, \"closeDialogEvent\", \"header\", \"headerClass\", \"headerTextClass\", \"showCloseDialogIcon\"], [1, \"dialog-footer\", \"fixed\", \"bottom-0\", \"left-0\", \"w-full\", \"h-3rem\"], [1, \"relative\", \"h-full\"], [1, \"surface-section\", \"h-full\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"surface-section\", \"ng-star-inserted\"], [1, \"blue-bg-gradient-1\", \"text-white\", \"p-1\", \"px-3\", \"flex\", \"justify-content-center\", \"align-items-center\", \"flex-wrap\", \"shadow-2\", 2, \"border-radius\", \"8px\"], [1, \"font-bold\"], [1, \"align-items-center\", \"flex\"], [1, \"line-height-3\", \"hidden\", \"lg:block\"], [\"pButton\", \"\", \"pRipple\", \"\", \"severity\", \"info\", \"type\", \"button\", \"label\", \"Send to Chat\", \"icon\", \"pi pi-file\", \"iconPos\", \"right\", \"styleClass\", \"\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", 3, \"click\", \"rounded\"]],\n    template: function ChatUploadFileDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ChatUploadFileDialogComponent_Defer_0_Template, 7, 5)(1, ChatUploadFileDialogComponent_DeferPlaceholder_1_Template, 2, 0);\n        i0.ɵɵdefer(2, 0, ChatUploadFileDialogComponent_Defer_2_DepsFn, null, 1);\n        i0.ɵɵdeferOnViewport(0, -1);\n      }\n    },\n    dependencies: [CommonModule, ButtonModule, SharedModule, LibraryModule],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jaGF0L2NvbXBvbmVudHMvY2hhdC11cGxvYWQtZmlsZS1kaWFsb2cvY2hhdC11cGxvYWQtZmlsZS1kaWFsb2cuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "inject", "Injector", "signal", "ButtonModule", "ChatService", "LibraryModule", "SharedModule", "toObservable", "LibraryService", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ChatUploadFileDialogComponent_Defer_0_Conditional_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onSubmitFilesToUpload", "ɵɵproperty", "ChatUploadFileDialogComponent_Defer_0_Template_app_block_viewer_closeDialogEvent_0_listener", "_r1", "onDialogClose", "ɵɵtemplate", "ChatUploadFileDialogComponent_Defer_0_Conditional_5_Template", "ChatUploadFileDialogComponent_Defer_0_Conditional_6_Template", "ɵɵadvance", "ɵɵconditional", "checkedFilesToUpload", "length", "ChatUploadFileDialogComponent", "constructor", "ref", "config", "chatService", "libraryService", "dialogData", "showUploadFile", "injector", "ngOnInit", "set", "data", "console", "log", "checkedLibraryFiles", "subscribe", "next", "close", "destroy", "alert", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "DynamicDialogRef", "DynamicDialogConfig", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatUploadFileDialogComponent_Template", "rf", "ctx", "ChatUploadFileDialogComponent_Defer_0_Template", "ChatUploadFileDialogComponent_DeferPlaceholder_1_Template", "ɵɵdefer", "ChatUploadFileDialogComponent_Defer_2_DepsFn", "ɵɵdeferOnViewport", "styles"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\chat\\components\\chat-upload-file-dialog\\chat-upload-file-dialog.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\chat\\components\\chat-upload-file-dialog\\chat-upload-file-dialog.component.html"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\r\nimport { ChangeDetectionStrategy, Component, inject, Injector, signal } from '@angular/core';\r\nimport { ButtonModule } from \"primeng/button\";\r\nimport { DynamicDialogRef, DynamicDialogConfig } from \"primeng/dynamicdialog\";\r\nimport { ChatService } from \"src/app/core/services/chat.service\";\r\nimport { LibraryModule } from \"src/app/modules/library/library.module\";\r\nimport { SingleLibraryComponent } from \"src/app/modules/library/library/components/single-library/single-library.component\";\r\nimport { SharedModule } from \"src/app/shared/shared.module\";\r\nimport { toObservable } from '@angular/core/rxjs-interop';\r\nimport { LibraryService } from \"src/app/core/services/library.service\";\r\n\r\n@Component({\r\n    selector: 'app-chat-upload-file-dialog',\r\n    standalone: true,\r\n    imports: [\r\n        CommonModule,\r\n        ButtonModule,\r\n        SharedModule,\r\n        LibraryModule,\r\n    ],\r\n    templateUrl: './chat-upload-file-dialog.component.html',\r\n    styleUrl: './chat-upload-file-dialog.component.css',\r\n})\r\nexport class ChatUploadFileDialogComponent {\r\n    chatService = inject(ChatService);\r\n    libraryService = inject(LibraryService);\r\n    dialogData: any = {};\r\n    showUploadFile = false;\r\n    checkedFilesToUpload = signal([] as any[]);\r\n    private injector = inject(Injector);\r\n    constructor(\r\n        private ref: DynamicDialogRef,\r\n        private config: DynamicDialogConfig,\r\n    ) { }\r\n\r\n    ngOnInit() {\r\n        this.chatService.showUploadFile.set(true);\r\n        this.dialogData = this.config.data.dialogData;\r\n        console.log(this.dialogData);\r\n        this.checkedFilesToUpload.set([]);\r\n\r\n        toObservable(this.libraryService.checkedLibraryFiles, {\r\n            injector: this.injector\r\n          }).subscribe({\r\n            next: (data) => {\r\n              console.log(data);\r\n              this.checkedFilesToUpload.set(data);\r\n            }\r\n          });\r\n    }\r\n\r\n    onSubmitFilesToUpload() {\r\n        this.ref.close(this.checkedFilesToUpload());\r\n        this.checkedFilesToUpload.set([]);\r\n        this.ref.destroy();\r\n        this.chatService.showUploadFile.set(false);\r\n    }\r\n\r\n    onDialogClose(data?: any) {\r\n        alert(data);\r\n        this.chatService.showUploadFile.set(false);\r\n        this.libraryService.checkedLibraryFiles.set([]);\r\n        this.checkedFilesToUpload.set([]);\r\n        this.ref.close(this.ref);\r\n        this.ref.destroy();\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        this.chatService.showUploadFile.set(false);\r\n        this.checkedFilesToUpload.set([]);\r\n        this.ref.destroy();\r\n    }\r\n\r\n\r\n}\r\n", "@defer(on viewport) {\r\n<app-block-viewer [header]=\"'Send from Library to Chat'\"\r\n    headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\" blockClass=\"border-radius-bottom-10 mb-0\"\r\n    headerBlockClass=\"py-1 border-round-lg bg-cover \" containerClass=\"bg-white px-3 py-2 relative\"\r\n    [headerClass]=\"'justify-content-center my-0'\"\r\n    [headerTextClass]=\"'font-base font-semibold justify-content-center capitalize'\" [showCloseDialogIcon]=\"true\"\r\n    (closeDialogEvent)=\"onDialogClose()\">\r\n\r\n\r\n    <app-library></app-library>\r\n\r\n\r\n   \r\n    <div class=\"dialog-footer fixed bottom-0 left-0 w-full h-3rem\">\r\n\r\n      \r\n        <div class=\"relative  h-full\">\r\n            <div class=\"surface-section h-full flex align-items-center justify-content-center\">\r\n\r\n                @if (checkedFilesToUpload().length === 0) {\r\n                    <div class=\" surface-section ng-star-inserted\">\r\n                        <div class=\"blue-bg-gradient-1 text-white p-1 px-3 flex justify-content-center align-items-center flex-wrap shadow-2\"\r\n                            style=\"border-radius: 8px;\">\r\n                            <div class=\"font-bold\"></div>\r\n                            <div class=\"align-items-center flex\"><span class=\"line-height-3 hidden lg:block\">\r\n                                Please select files to send to chat.\r\n                            </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                } @else {\r\n                    <button pButton pRipple [rounded]=\"true\" severity=\"info\" type=\"button\" label=\"Send to Chat\"\r\n                    icon=\"pi pi-file\" iconPos=\"right\"\r\n                    class=\"p-button-sm p-button-raised p-button-rounded blue-gradient-btn\" styleClass=\"\"\r\n                    (click)=\"onSubmitFilesToUpload()\"></button>\r\n                }\r\n\r\n               \r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n</app-block-viewer>\r\n}@placeholder {\r\n<span>loading...</span>\r\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAA6CC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AAC5F,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,aAAa,QAAQ,wCAAwC;AAEtE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,cAAc,QAAQ,uCAAuC;;;;;;;;;ICY9CC,EADJ,CAAAC,cAAA,aAA+C,aAEX;IAC5BD,EAAA,CAAAE,SAAA,aAA6B;IACQF,EAArC,CAAAC,cAAA,aAAqC,cAA4C;IAC7ED,EAAA,CAAAG,MAAA,6CACJ;IAGRH,EAHQ,CAAAI,YAAA,EAAO,EACD,EACJ,EACJ;;;;;;IAENJ,EAAA,CAAAC,cAAA,gBAGkC;IAAlCD,EAAA,CAAAK,UAAA,mBAAAC,qFAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,qBAAA,EAAuB;IAAA,EAAC;IAACZ,EAAA,CAAAI,YAAA,EAAS;;;IAHnBJ,EAAA,CAAAa,UAAA,iBAAgB;;;;;;IA9B5Db,EAAA,CAAAC,cAAA,0BAKyC;IAArCD,EAAA,CAAAK,UAAA,8BAAAS,4FAAA;MAAAd,EAAA,CAAAO,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAoBF,MAAA,CAAAO,aAAA,EAAe;IAAA,EAAC;IAGpChB,EAAA,CAAAE,SAAA,kBAA2B;IAQnBF,EAJR,CAAAC,cAAA,aAA+D,aAG7B,aACyD;IAa7ED,EAXF,CAAAiB,UAAA,IAAAC,4DAAA,iBAA2C,IAAAC,4DAAA,OAWlC;IAYzBnB,EAJY,CAAAI,YAAA,EAAM,EACJ,EACJ,EAES;;;;IArCiEJ,EAJlE,CAAAa,UAAA,uCAAsC,8CAGP,gFACkC,6BAA6B;IAchGb,EAAA,CAAAoB,SAAA,GAgBC;IAhBDpB,EAAA,CAAAqB,aAAA,IAAAZ,MAAA,CAAAa,oBAAA,GAAAC,MAAA,eAgBC;;;;;IASjBvB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;ADrBvB,OAAM,MAAOoB,6BAA6B;EAOtCC,YACYC,GAAqB,EACrBC,MAA2B;IAD3B,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IARlB,KAAAC,WAAW,GAAGrC,MAAM,CAACI,WAAW,CAAC;IACjC,KAAAkC,cAAc,GAAGtC,MAAM,CAACQ,cAAc,CAAC;IACvC,KAAA+B,UAAU,GAAQ,EAAE;IACpB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAT,oBAAoB,GAAG7B,MAAM,CAAC,EAAW,CAAC;IAClC,KAAAuC,QAAQ,GAAGzC,MAAM,CAACC,QAAQ,CAAC;EAI/B;EAEJyC,QAAQA,CAAA;IACJ,IAAI,CAACL,WAAW,CAACG,cAAc,CAACG,GAAG,CAAC,IAAI,CAAC;IACzC,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACH,MAAM,CAACQ,IAAI,CAACL,UAAU;IAC7CM,OAAO,CAACC,GAAG,CAAC,IAAI,CAACP,UAAU,CAAC;IAC5B,IAAI,CAACR,oBAAoB,CAACY,GAAG,CAAC,EAAE,CAAC;IAEjCpC,YAAY,CAAC,IAAI,CAAC+B,cAAc,CAACS,mBAAmB,EAAE;MAClDN,QAAQ,EAAE,IAAI,CAACA;KAChB,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGL,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACb,oBAAoB,CAACY,GAAG,CAACC,IAAI,CAAC;MACrC;KACD,CAAC;EACR;EAEAvB,qBAAqBA,CAAA;IACjB,IAAI,CAACc,GAAG,CAACe,KAAK,CAAC,IAAI,CAACnB,oBAAoB,EAAE,CAAC;IAC3C,IAAI,CAACA,oBAAoB,CAACY,GAAG,CAAC,EAAE,CAAC;IACjC,IAAI,CAACR,GAAG,CAACgB,OAAO,EAAE;IAClB,IAAI,CAACd,WAAW,CAACG,cAAc,CAACG,GAAG,CAAC,KAAK,CAAC;EAC9C;EAEAlB,aAAaA,CAACmB,IAAU;IACpBQ,KAAK,CAACR,IAAI,CAAC;IACX,IAAI,CAACP,WAAW,CAACG,cAAc,CAACG,GAAG,CAAC,KAAK,CAAC;IAC1C,IAAI,CAACL,cAAc,CAACS,mBAAmB,CAACJ,GAAG,CAAC,EAAE,CAAC;IAC/C,IAAI,CAACZ,oBAAoB,CAACY,GAAG,CAAC,EAAE,CAAC;IACjC,IAAI,CAACR,GAAG,CAACe,KAAK,CAAC,IAAI,CAACf,GAAG,CAAC;IACxB,IAAI,CAACA,GAAG,CAACgB,OAAO,EAAE;EACtB;EAEAE,WAAWA,CAAA;IACP,IAAI,CAAChB,WAAW,CAACG,cAAc,CAACG,GAAG,CAAC,KAAK,CAAC;IAC1C,IAAI,CAACZ,oBAAoB,CAACY,GAAG,CAAC,EAAE,CAAC;IACjC,IAAI,CAACR,GAAG,CAACgB,OAAO,EAAE;EACtB;EAAC,QAAAG,CAAA,G;qBAhDQrB,6BAA6B,EAAAxB,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAE,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7B1B,6BAA6B;IAAA2B,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAArD,EAAA,CAAAsD,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCoBzC5D,EA3CD,CAAAiB,UAAA,IAAA6C,8CAAA,OA6CC,IAAAC,yDAAA;QA7CD/D,EAAA,CAAAgE,OAAA,OAAAC,4CAAA,UA6CC;QA7CMjE,EAAA,CAAAkE,iBAAA,OAAW;;;mBDeV5E,YAAY,EACZI,YAAY,EACZG,YAAY,EACZD,aAAa;IAAAuE,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}