{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../classroom/packages/packages.component\";\nexport class PackageHistoryComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function PackageHistoryComponent_Factory(t) {\n    return new (t || PackageHistoryComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PackageHistoryComponent,\n    selectors: [[\"app-package-history\"]],\n    decls: 1,\n    vars: 0,\n    template: function PackageHistoryComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-packages\");\n      }\n    },\n    dependencies: [i1.PackagesComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["PackageHistoryComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "template", "PackageHistoryComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\package-history\\package-history.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\package-history\\package-history.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-package-history',\r\n  templateUrl: './package-history.component.html',\r\n  styleUrls: ['./package-history.component.scss']\r\n})\r\nexport class PackageHistoryComponent implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<app-packages></app-packages>"], "mappings": ";;AAOA,OAAM,MAAOA,uBAAuB;EAElCC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,uBAAuB;EAAA;EAAA,QAAAI,EAAA,G;UAAvBJ,uBAAuB;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPpCE,EAAA,CAAAC,SAAA,mBAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}