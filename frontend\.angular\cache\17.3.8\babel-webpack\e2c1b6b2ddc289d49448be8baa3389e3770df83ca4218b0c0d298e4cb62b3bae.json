{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CertificateComponent } from './certificate.component';\nimport { TabViewModule } from 'primeng/tabview';\nimport { TableModule } from 'primeng/table';\nimport { CarouselModule } from 'primeng/carousel';\nimport { DialogModule } from 'primeng/dialog';\nimport { ButtonModule } from 'primeng/button';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport class CertificateModule {\n  static #_ = this.ɵfac = function CertificateModule_Factory(t) {\n    return new (t || CertificateModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CertificateModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, TabViewModule, TableModule, CarouselModule, DialogModule, ButtonModule, SharedModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CertificateModule, {\n    declarations: [CertificateComponent],\n    imports: [CommonModule, TabViewModule, TableModule, CarouselModule, DialogModule, ButtonModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "CertificateComponent", "TabViewModule", "TableModule", "CarouselModule", "DialogModule", "ButtonModule", "SharedModule", "CertificateModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\certificate\\certificate.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { CertificateComponent } from './certificate.component';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { TableModule } from 'primeng/table';\r\nimport { CarouselModule } from 'primeng/carousel';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n@NgModule({\r\n  declarations: [\r\n    CertificateComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    TabViewModule,\r\n    TableModule,\r\n    CarouselModule,\r\n    DialogModule,\r\n    ButtonModule,\r\n    SharedModule\r\n  ]\r\n})\r\nexport class CertificateModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,8BAA8B;;AAe3D,OAAM,MAAOC,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAT1BX,YAAY,EACZE,aAAa,EACbC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,YAAY;EAAA;;;2EAGHC,iBAAiB;IAAAI,YAAA,GAZ1BX,oBAAoB;IAAAY,OAAA,GAGpBb,YAAY,EACZE,aAAa,EACbC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}