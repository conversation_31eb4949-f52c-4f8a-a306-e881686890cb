{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport { RateValue } from 'src/app/core/models/classroom.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"src/app/core/services/rating-and-report.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"src/app/core/services/general.service\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"../../../shared/classroom/rate-question-item/rate-question-item.component\";\nimport * as i8 from \"primeng/button\";\nfunction ClassroomRateBoxComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-rate-question-item\", 12);\n    i0.ɵɵlistener(\"ratingSelected\", function ClassroomRateBoxComponent_div_8_Template_app_rate_question_item_ratingSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRatingSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const question_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"questionIndexNumber\", i_r4 + 1)(\"questionText\", question_r3)(\"questionAttr\", ctx_r1.questionAttributes[i_r4]);\n  }\n}\nexport let ClassroomRateBoxComponent = /*#__PURE__*/(() => {\n  class ClassroomRateBoxComponent {\n    constructor(location, ratingService, classroomService, authService, generalService, toastService) {\n      this.location = location;\n      this.ratingService = ratingService;\n      this.classroomService = classroomService;\n      this.authService = authService;\n      this.generalService = generalService;\n      this.toastService = toastService;\n      this.subs = new SubSink();\n      this.questions = ['Was the tutor friendly?', 'Was the tutor polite?', 'Was the tutor focused on the lesson?', 'Did the tutor explain the learning process thoroughly?', 'Did the tutor take your learning preferences and needs into consideration?', 'Did the tutor show interest in your language concerns?', 'Did the tutor answer all your questions efficiently?', 'Did the tutor evaluate your level correctly?', 'Was it easy to plan a schedule for future lessons?'];\n      this.questionAttributes = ['friendly', 'polite', 'focused', 'explain', 'preferences', 'interest', 'efficiently', 'evaluate', 'schedule'];\n      this.classroom = {};\n      this.currentTeacherRating = {\n        classroomId: null,\n        teacherId: null,\n        friendly: RateValue.NEUTRAL,\n        polite: RateValue.NEUTRAL,\n        focused: RateValue.NEUTRAL,\n        explain: RateValue.NEUTRAL,\n        preferences: RateValue.NEUTRAL,\n        interest: RateValue.NEUTRAL,\n        efficiently: RateValue.NEUTRAL,\n        evaluate: RateValue.NEUTRAL,\n        schedule: RateValue.NEUTRAL,\n        level: RateValue.NEUTRAL\n      };\n    }\n    ngOnInit() {\n      this.classroom = this.classroomService.getSelectedClassroom();\n      console.log(this.classroom);\n      this.currentTeacherRating.teacherId = this.classroom.teacher?.aspUserId;\n      this.currentTeacherRating.classroomId = this.classroom.id;\n      this.ratingService.getTeacherRatingEvaluations(this.classroom.id).pipe(take(1)).subscribe(res => {\n        // this.toastr.success(\"Your trial rating has been submitted\")\n        console.log(res);\n      });\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    onConfirmSelection(event) {\n      this.location.back();\n    }\n    onSubmitRate() {\n      const rating = this.currentTeacherRating;\n      const toastMessage = {\n        severity: 'success',\n        summary: '',\n        detail: 'Classroom rated successfully.'\n      };\n      const subscription = this.ratingService.submitTeacherTrialRating(rating).pipe(take(1)).subscribe(res => {\n        this.ratingService.setUpdateListener(true);\n        this.toastService.setShowToastmessage(toastMessage);\n        this.generalService.navigateToLessonDetails(this.classroom, this.authService.getUserRole());\n      });\n      this.subs.add(subscription);\n    }\n    onRatingSelected(event) {\n      console.log(event);\n      this.setCurrentTeacherRatingValue(event);\n    }\n    setCurrentTeacherRatingValue(rating) {\n      this.currentTeacherRating[rating.attr] = this.getRateFromRateValue(rating.number);\n      console.log(this.currentTeacherRating);\n    }\n    getRateFromRateValue(rateValue) {\n      switch (rateValue) {\n        case 1:\n          return RateValue.VERY_BAD;\n        case 2:\n          return RateValue.BAD;\n        case 3:\n          return RateValue.NEUTRAL;\n        case 4:\n          return RateValue.GOOD;\n        case 5:\n          return RateValue.VERY_GOOD;\n        default:\n          return RateValue.NEUTRAL;\n      }\n    }\n    static #_ = this.ɵfac = function ClassroomRateBoxComponent_Factory(t) {\n      return new (t || ClassroomRateBoxComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.RatingAndReportService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.GeneralService), i0.ɵɵdirectiveInject(i6.ToastService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassroomRateBoxComponent,\n      selectors: [[\"app-classroom-rate-box\"]],\n      decls: 15,\n      vars: 1,\n      consts: [[1, \"border-round-xl\"], [1, \"\"], [1, \"border-round-xl\", \"overflow-hidden\"], [1, \"px-2\", \"lg:px-4\", \"py-3\", \"block-gradient\"], [1, \"text-center\", \"text-sm\", \"my-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"flex\", \"flex-column\"], [1, \"grid\", \"align-items-center\"], [1, \"col-12\", \"md:col-5\"], [1, \"col-12\", \"md:col-7\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"gap-3\", \"justify-content-center\", \"md:justify-content-end\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Submit\", \"icon\", \"pi pi-check-circle\", \"iconPos\", \"right\", 1, \"p-button-md\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", 3, \"click\"], [3, \"ratingSelected\", \"questionIndexNumber\", \"questionText\", \"questionAttr\"]],\n      template: function ClassroomRateBoxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"b\");\n          i0.ɵɵtext(6, \"STUDENT QUESTIONNAIRE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" PLEASE RATE YOUR TEACHER IN EACH OF THE FOLLOWING CATEGORIES \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, ClassroomRateBoxComponent_div_8_Template, 2, 3, \"div\", 5);\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7);\n          i0.ɵɵelement(11, \"div\", 8);\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function ClassroomRateBoxComponent_Template_button_click_14_listener() {\n            return ctx.onSubmitRate();\n          });\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.questions);\n        }\n      },\n      dependencies: [i1.NgForOf, i7.RateQuestionItemComponent, i8.ButtonDirective],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.gradient-header[_ngcontent-%COMP%]{min-height:60px;border-radius:8px;background-image:linear-gradient(90deg,#5d62e9,#6163ea 16%,#6765ec 31%,#6c68ee 45%,#716af0,#776ef2 67%,#7c71f4 75%,#8275f5 84%,#8778f7,#8c7bf9);box-shadow:0 3px 6px #00000029}.rate-input-text[_ngcontent-%COMP%]{background:transparent;border:1px solid #707070}.radio-label[_ngcontent-%COMP%]{font-size:clamp(.88rem,.07vw + .86rem,1rem)}\"]\n    });\n  }\n  return ClassroomRateBoxComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}