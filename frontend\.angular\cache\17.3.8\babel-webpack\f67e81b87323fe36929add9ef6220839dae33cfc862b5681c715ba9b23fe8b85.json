{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, isDevMode, Component, ChangeDetectionStrategy, Optional, Inject, ViewChild, Input, HostBinding, Output, HostListener, NgModule } from '@angular/core';\nimport * as i4 from '@angular/platform-browser';\nimport { HAMMER_LOADER } from '@angular/platform-browser';\nimport { takeUntil, first } from 'rxjs/operators';\nimport { merge, fromEvent } from 'rxjs';\nimport * as i5 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"wrapper\"];\nconst _c1 = [\"sourceImage\"];\nfunction ImageCropperComponent_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 5, 1);\n    i0.ɵɵlistener(\"load\", function ImageCropperComponent_img_2_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.imageLoadedInView());\n    })(\"mousedown\", function ImageCropperComponent_img_2_Template_img_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Drag));\n    })(\"touchstart\", function ImageCropperComponent_img_2_Template_img_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Drag));\n    })(\"error\", function ImageCropperComponent_img_2_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadImageError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"visibility\", ctx_r1.imageVisible ? \"visible\" : \"hidden\")(\"transform\", ctx_r1.safeTransformStyle);\n    i0.ɵɵclassProp(\"ngx-ic-draggable\", !ctx_r1.disabled && ctx_r1.allowMoveImage);\n    i0.ɵɵproperty(\"src\", ctx_r1.safeImgDataUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"alt\", ctx_r1.imageAltText);\n  }\n}\nfunction ImageCropperComponent_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 9);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"topleft\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"topleft\"));\n    });\n    i0.ɵɵelement(2, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵelement(4, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 12);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"topright\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"topright\"));\n    });\n    i0.ɵɵelement(6, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 13);\n    i0.ɵɵelement(8, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"bottomright\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"bottomright\"));\n    });\n    i0.ɵɵelement(10, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 15);\n    i0.ɵɵelement(12, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 16);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_13_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"bottomleft\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_13_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"bottomleft\"));\n    });\n    i0.ɵɵelement(14, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 17);\n    i0.ɵɵelement(16, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 18);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"top\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"top\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 19);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_18_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"right\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_18_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"right\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 20);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_19_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"bottom\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_19_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"bottom\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 21);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_20_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"left\"));\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_20_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Resize, \"left\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ImageCropperComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"keydown\", function ImageCropperComponent_div_4_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.keyboardAccess($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_Template_div_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Move));\n    })(\"touchstart\", function ImageCropperComponent_div_4_Template_div_touchstart_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.startMove($event, ctx_r1.moveTypes.Move));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ImageCropperComponent_div_4_ng_container_2_Template, 21, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.cropper.y1, \"px\")(\"left\", ctx_r1.cropper.x1, \"px\")(\"width\", ctx_r1.cropper.x2 - ctx_r1.cropper.x1, \"px\")(\"height\", ctx_r1.cropper.y2 - ctx_r1.cropper.y1, \"px\")(\"margin-left\", ctx_r1.alignImage === \"center\" ? ctx_r1.marginLeft : null)(\"visibility\", ctx_r1.imageVisible ? \"visible\" : \"hidden\");\n    i0.ɵɵclassProp(\"ngx-ic-round\", ctx_r1.roundCropper);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.cropperFrameAriaLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hideResizeSquares);\n  }\n}\nclass CropperSettings {\n  constructor() {\n    // From options\n    this.format = 'png';\n    this.output = 'blob';\n    this.maintainAspectRatio = true;\n    this.transform = {};\n    this.aspectRatio = 1;\n    this.resetCropOnAspectRatioChange = true;\n    this.resizeToWidth = 0;\n    this.resizeToHeight = 0;\n    this.cropperMinWidth = 0;\n    this.cropperMinHeight = 0;\n    this.cropperMaxHeight = 0;\n    this.cropperMaxWidth = 0;\n    this.cropperStaticWidth = 0;\n    this.cropperStaticHeight = 0;\n    this.canvasRotation = 0;\n    this.initialStepSize = 3;\n    this.roundCropper = false;\n    this.onlyScaleDown = false;\n    this.imageQuality = 92;\n    this.autoCrop = true;\n    this.backgroundColor = null;\n    this.containWithinAspectRatio = false;\n    this.hideResizeSquares = false;\n    this.alignImage = 'center';\n    this.cropperFrameAriaLabel = 'Crop photo';\n    // Internal\n    this.cropperScaledMinWidth = 20;\n    this.cropperScaledMinHeight = 20;\n    this.cropperScaledMaxWidth = 20;\n    this.cropperScaledMaxHeight = 20;\n    this.stepSize = this.initialStepSize;\n  }\n  setOptions(options) {\n    Object.keys(options).filter(k => k in this).forEach(k => this[k] = options[k]);\n    this.validateOptions();\n  }\n  setOptionsFromChanges(changes) {\n    Object.keys(changes).filter(k => k in this).forEach(k => this[k] = changes[k].currentValue);\n    this.validateOptions();\n  }\n  validateOptions() {\n    if (this.maintainAspectRatio && !this.aspectRatio) {\n      throw new Error('`aspectRatio` should > 0 when `maintainAspectRatio` is enabled');\n    }\n  }\n}\nvar MoveTypes = /*#__PURE__*/function (MoveTypes) {\n  MoveTypes[\"Drag\"] = \"drag\";\n  MoveTypes[\"Move\"] = \"move\";\n  MoveTypes[\"Resize\"] = \"resize\";\n  MoveTypes[\"Pinch\"] = \"pinch\";\n  return MoveTypes;\n}(MoveTypes || {});\nfunction getPositionForKey(key) {\n  switch (key) {\n    case 'ArrowUp':\n      return 'top';\n    case 'ArrowRight':\n      return 'right';\n    case 'ArrowDown':\n      return 'bottom';\n    case 'ArrowLeft':\n    default:\n      return 'left';\n  }\n}\nfunction getInvertedPositionForKey(key) {\n  switch (key) {\n    case 'ArrowUp':\n      return 'bottom';\n    case 'ArrowRight':\n      return 'left';\n    case 'ArrowDown':\n      return 'top';\n    case 'ArrowLeft':\n    default:\n      return 'right';\n  }\n}\nfunction getEventForKey(key, stepSize) {\n  switch (key) {\n    case 'ArrowUp':\n      return {\n        clientX: 0,\n        clientY: stepSize * -1\n      };\n    case 'ArrowRight':\n      return {\n        clientX: stepSize,\n        clientY: 0\n      };\n    case 'ArrowDown':\n      return {\n        clientX: 0,\n        clientY: stepSize\n      };\n    case 'ArrowLeft':\n    default:\n      return {\n        clientX: stepSize * -1,\n        clientY: 0\n      };\n  }\n}\n\n/*\n * Hermite resize - fast image resize/resample using Hermite filter.\n * https://github.com/viliusle/Hermite-resize\n */\nfunction resizeCanvas(canvas, width, height) {\n  const width_source = canvas.width;\n  const height_source = canvas.height;\n  width = Math.round(width);\n  height = Math.round(height);\n  const ratio_w = width_source / width;\n  const ratio_h = height_source / height;\n  const ratio_w_half = Math.ceil(ratio_w / 2);\n  const ratio_h_half = Math.ceil(ratio_h / 2);\n  const ctx = canvas.getContext('2d');\n  if (ctx) {\n    const img = ctx.getImageData(0, 0, width_source, height_source);\n    const img2 = ctx.createImageData(width, height);\n    const data = img.data;\n    const data2 = img2.data;\n    for (let j = 0; j < height; j++) {\n      for (let i = 0; i < width; i++) {\n        const x2 = (i + j * width) * 4;\n        const center_y = j * ratio_h;\n        let weight = 0;\n        let weights = 0;\n        let weights_alpha = 0;\n        let gx_r = 0;\n        let gx_g = 0;\n        let gx_b = 0;\n        let gx_a = 0;\n        const xx_start = Math.floor(i * ratio_w);\n        const yy_start = Math.floor(j * ratio_h);\n        let xx_stop = Math.ceil((i + 1) * ratio_w);\n        let yy_stop = Math.ceil((j + 1) * ratio_h);\n        xx_stop = Math.min(xx_stop, width_source);\n        yy_stop = Math.min(yy_stop, height_source);\n        for (let yy = yy_start; yy < yy_stop; yy++) {\n          const dy = Math.abs(center_y - yy) / ratio_h_half;\n          const center_x = i * ratio_w;\n          const w0 = dy * dy; //pre-calc part of w\n          for (let xx = xx_start; xx < xx_stop; xx++) {\n            const dx = Math.abs(center_x - xx) / ratio_w_half;\n            const w = Math.sqrt(w0 + dx * dx);\n            if (w >= 1) {\n              //pixel too far\n              continue;\n            }\n            //hermite filter\n            weight = 2 * w * w * w - 3 * w * w + 1;\n            const pos_x = 4 * (xx + yy * width_source);\n            //alpha\n            gx_a += weight * data[pos_x + 3];\n            weights_alpha += weight;\n            //colors\n            if (data[pos_x + 3] < 255) weight = weight * data[pos_x + 3] / 250;\n            gx_r += weight * data[pos_x];\n            gx_g += weight * data[pos_x + 1];\n            gx_b += weight * data[pos_x + 2];\n            weights += weight;\n          }\n        }\n        data2[x2] = gx_r / weights;\n        data2[x2 + 1] = gx_g / weights;\n        data2[x2 + 2] = gx_b / weights;\n        data2[x2 + 3] = gx_a / weights_alpha;\n      }\n    }\n    canvas.width = width;\n    canvas.height = height;\n    //draw\n    ctx.putImageData(img2, 0, 0);\n  }\n}\nfunction percentage(percent, totalValue) {\n  return percent / 100 * totalValue;\n}\nlet CropService = /*#__PURE__*/(() => {\n  class CropService {\n    crop(loadedImage, cropper, settings, output, maxSize) {\n      const imagePosition = this.getImagePosition(loadedImage, cropper, settings, maxSize);\n      const width = imagePosition.x2 - imagePosition.x1;\n      const height = imagePosition.y2 - imagePosition.y1;\n      const cropCanvas = document.createElement('canvas');\n      cropCanvas.width = width;\n      cropCanvas.height = height;\n      const ctx = cropCanvas.getContext('2d');\n      if (!ctx) {\n        return null;\n      }\n      if (settings.backgroundColor != null) {\n        ctx.fillStyle = settings.backgroundColor;\n        ctx.fillRect(0, 0, width, height);\n      }\n      const scaleX = (settings.transform.scale || 1) * (settings.transform.flipH ? -1 : 1);\n      const scaleY = (settings.transform.scale || 1) * (settings.transform.flipV ? -1 : 1);\n      const {\n        translateH,\n        translateV\n      } = this.getCanvasTranslate(loadedImage, settings, maxSize);\n      const transformedImage = loadedImage.transformed;\n      ctx.setTransform(scaleX, 0, 0, scaleY, transformedImage.size.width / 2 + translateH, transformedImage.size.height / 2 + translateV);\n      ctx.translate(-imagePosition.x1 / scaleX, -imagePosition.y1 / scaleY);\n      ctx.rotate((settings.transform.rotate || 0) * Math.PI / 180);\n      ctx.drawImage(transformedImage.image, -transformedImage.size.width / 2, -transformedImage.size.height / 2);\n      const result = {\n        width,\n        height,\n        imagePosition,\n        cropperPosition: {\n          ...cropper\n        }\n      };\n      if (settings.containWithinAspectRatio) {\n        result.offsetImagePosition = this.getOffsetImagePosition(loadedImage, cropper, settings, maxSize);\n      }\n      const resizeRatio = this.getResizeRatio(width, height, settings);\n      if (resizeRatio !== 1) {\n        result.width = Math.round(width * resizeRatio);\n        result.height = settings.maintainAspectRatio ? Math.round(result.width / settings.aspectRatio) : Math.round(height * resizeRatio);\n        resizeCanvas(cropCanvas, result.width, result.height);\n      }\n      if (output === 'blob') {\n        return this.cropToBlob(result, cropCanvas, settings);\n      } else {\n        result.base64 = cropCanvas.toDataURL('image/' + settings.format, this.getQuality(settings));\n        return result;\n      }\n    }\n    cropToBlob(output, cropCanvas, settings) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        output.blob = yield new Promise(resolve => cropCanvas.toBlob(resolve, 'image/' + settings.format, _this.getQuality(settings)));\n        if (output.blob) {\n          output.objectUrl = URL.createObjectURL(output.blob);\n        }\n        return output;\n      })();\n    }\n    getCanvasTranslate(loadedImage, settings, maxSize) {\n      if (settings.transform.translateUnit === 'px') {\n        const ratio = this.getRatio(loadedImage, maxSize);\n        return {\n          translateH: (settings.transform.translateH || 0) * ratio,\n          translateV: (settings.transform.translateV || 0) * ratio\n        };\n      } else {\n        return {\n          translateH: settings.transform.translateH ? percentage(settings.transform.translateH, loadedImage.transformed.size.width) : 0,\n          translateV: settings.transform.translateV ? percentage(settings.transform.translateV, loadedImage.transformed.size.height) : 0\n        };\n      }\n    }\n    getRatio(loadedImage, maxSize) {\n      return loadedImage.transformed.size.width / maxSize.width;\n    }\n    getImagePosition(loadedImage, cropper, settings, maxSize) {\n      const ratio = this.getRatio(loadedImage, maxSize);\n      const out = {\n        x1: Math.round(cropper.x1 * ratio),\n        y1: Math.round(cropper.y1 * ratio),\n        x2: Math.round(cropper.x2 * ratio),\n        y2: Math.round(cropper.y2 * ratio)\n      };\n      if (!settings.containWithinAspectRatio) {\n        out.x1 = Math.max(out.x1, 0);\n        out.y1 = Math.max(out.y1, 0);\n        out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n        out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n      }\n      return out;\n    }\n    getOffsetImagePosition(loadedImage, cropper, settings, maxSize) {\n      const canvasRotation = settings.canvasRotation + loadedImage.exifTransform.rotate;\n      const ratio = this.getRatio(loadedImage, maxSize);\n      let offsetX;\n      let offsetY;\n      if (canvasRotation % 2) {\n        offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.height) / 2;\n        offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.width) / 2;\n      } else {\n        offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.width) / 2;\n        offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.height) / 2;\n      }\n      const out = {\n        x1: Math.round(cropper.x1 * ratio) - offsetX,\n        y1: Math.round(cropper.y1 * ratio) - offsetY,\n        x2: Math.round(cropper.x2 * ratio) - offsetX,\n        y2: Math.round(cropper.y2 * ratio) - offsetY\n      };\n      if (!settings.containWithinAspectRatio) {\n        out.x1 = Math.max(out.x1, 0);\n        out.y1 = Math.max(out.y1, 0);\n        out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n        out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n      }\n      return out;\n    }\n    getResizeRatio(width, height, settings) {\n      const ratioWidth = settings.resizeToWidth / width;\n      const ratioHeight = settings.resizeToHeight / height;\n      const ratios = new Array();\n      if (settings.resizeToWidth > 0) {\n        ratios.push(ratioWidth);\n      }\n      if (settings.resizeToHeight > 0) {\n        ratios.push(ratioHeight);\n      }\n      const result = ratios.length === 0 ? 1 : Math.min(...ratios);\n      if (result > 1 && !settings.onlyScaleDown) {\n        return result;\n      }\n      return Math.min(result, 1);\n    }\n    getQuality(settings) {\n      return Math.min(1, Math.max(0, settings.imageQuality / 100));\n    }\n  }\n  CropService.ɵfac = function CropService_Factory(t) {\n    return new (t || CropService)();\n  };\n  CropService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CropService,\n    factory: CropService.ɵfac,\n    providedIn: 'root'\n  });\n  return CropService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CropperPositionService = /*#__PURE__*/(() => {\n  class CropperPositionService {\n    resetCropperPosition(sourceImage, cropperPosition, settings, maxSize) {\n      if (!sourceImage?.nativeElement) {\n        return;\n      }\n      if (settings.cropperStaticHeight && settings.cropperStaticWidth) {\n        cropperPosition.x1 = 0;\n        cropperPosition.x2 = maxSize.width > settings.cropperStaticWidth ? settings.cropperStaticWidth : maxSize.width;\n        cropperPosition.y1 = 0;\n        cropperPosition.y2 = maxSize.height > settings.cropperStaticHeight ? settings.cropperStaticHeight : maxSize.height;\n      } else {\n        const cropperWidth = Math.min(settings.cropperScaledMaxWidth, maxSize.width);\n        const cropperHeight = Math.min(settings.cropperScaledMaxHeight, maxSize.height);\n        if (!settings.maintainAspectRatio) {\n          cropperPosition.x1 = 0;\n          cropperPosition.x2 = cropperWidth;\n          cropperPosition.y1 = 0;\n          cropperPosition.y2 = cropperHeight;\n        } else if (maxSize.width / settings.aspectRatio < maxSize.height) {\n          cropperPosition.x1 = 0;\n          cropperPosition.x2 = cropperWidth;\n          const cropperHeightWithAspectRatio = cropperWidth / settings.aspectRatio;\n          cropperPosition.y1 = (maxSize.height - cropperHeightWithAspectRatio) / 2;\n          cropperPosition.y2 = cropperPosition.y1 + cropperHeightWithAspectRatio;\n        } else {\n          cropperPosition.y1 = 0;\n          cropperPosition.y2 = cropperHeight;\n          const cropperWidthWithAspectRatio = cropperHeight * settings.aspectRatio;\n          cropperPosition.x1 = (maxSize.width - cropperWidthWithAspectRatio) / 2;\n          cropperPosition.x2 = cropperPosition.x1 + cropperWidthWithAspectRatio;\n        }\n      }\n    }\n    move(event, moveStart, cropperPosition) {\n      const diffX = this.getClientX(event) - moveStart.clientX;\n      const diffY = this.getClientY(event) - moveStart.clientY;\n      cropperPosition.x1 = moveStart.x1 + diffX;\n      cropperPosition.y1 = moveStart.y1 + diffY;\n      cropperPosition.x2 = moveStart.x2 + diffX;\n      cropperPosition.y2 = moveStart.y2 + diffY;\n    }\n    resize(event, moveStart, cropperPosition, maxSize, settings) {\n      const moveX = this.getClientX(event) - moveStart.clientX;\n      const moveY = this.getClientY(event) - moveStart.clientY;\n      switch (moveStart.position) {\n        case 'left':\n          cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n          break;\n        case 'topleft':\n          cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n          cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n          break;\n        case 'top':\n          cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n          break;\n        case 'topright':\n          cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n          cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n          break;\n        case 'right':\n          cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n          break;\n        case 'bottomright':\n          cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n          cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n          break;\n        case 'bottom':\n          cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n          break;\n        case 'bottomleft':\n          cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n          cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n          break;\n        case 'center':\n          const scale = event.scale;\n          const newWidth = Math.min(Math.max(settings.cropperScaledMinWidth, Math.abs(moveStart.x2 - moveStart.x1) * scale), settings.cropperScaledMaxWidth);\n          const newHeight = Math.min(Math.max(settings.cropperScaledMinHeight, Math.abs(moveStart.y2 - moveStart.y1) * scale), settings.cropperScaledMaxHeight);\n          cropperPosition.x1 = moveStart.clientX - newWidth / 2;\n          cropperPosition.x2 = moveStart.clientX + newWidth / 2;\n          cropperPosition.y1 = moveStart.clientY - newHeight / 2;\n          cropperPosition.y2 = moveStart.clientY + newHeight / 2;\n          if (cropperPosition.x1 < 0) {\n            cropperPosition.x2 -= cropperPosition.x1;\n            cropperPosition.x1 = 0;\n          } else if (cropperPosition.x2 > maxSize.width) {\n            cropperPosition.x1 -= cropperPosition.x2 - maxSize.width;\n            cropperPosition.x2 = maxSize.width;\n          }\n          if (cropperPosition.y1 < 0) {\n            cropperPosition.y2 -= cropperPosition.y1;\n            cropperPosition.y1 = 0;\n          } else if (cropperPosition.y2 > maxSize.height) {\n            cropperPosition.y1 -= cropperPosition.y2 - maxSize.height;\n            cropperPosition.y2 = maxSize.height;\n          }\n          break;\n      }\n      if (settings.maintainAspectRatio) {\n        this.checkAspectRatio(moveStart.position, cropperPosition, maxSize, settings);\n      }\n    }\n    checkAspectRatio(position, cropperPosition, maxSize, settings) {\n      let overflowX = 0;\n      let overflowY = 0;\n      switch (position) {\n        case 'top':\n          cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n          overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n          overflowY = Math.max(0 - cropperPosition.y1, 0);\n          if (overflowX > 0 || overflowY > 0) {\n            cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n            cropperPosition.y1 += overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n          }\n          break;\n        case 'bottom':\n          cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n          overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n          overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n          if (overflowX > 0 || overflowY > 0) {\n            cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n            cropperPosition.y2 -= overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n          }\n          break;\n        case 'topleft':\n          cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n          overflowX = Math.max(0 - cropperPosition.x1, 0);\n          overflowY = Math.max(0 - cropperPosition.y1, 0);\n          if (overflowX > 0 || overflowY > 0) {\n            cropperPosition.x1 += overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n            cropperPosition.y1 += overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n          }\n          break;\n        case 'topright':\n          cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n          overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n          overflowY = Math.max(0 - cropperPosition.y1, 0);\n          if (overflowX > 0 || overflowY > 0) {\n            cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n            cropperPosition.y1 += overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n          }\n          break;\n        case 'right':\n        case 'bottomright':\n          cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n          overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n          overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n          if (overflowX > 0 || overflowY > 0) {\n            cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n            cropperPosition.y2 -= overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n          }\n          break;\n        case 'left':\n        case 'bottomleft':\n          cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n          overflowX = Math.max(0 - cropperPosition.x1, 0);\n          overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n          if (overflowX > 0 || overflowY > 0) {\n            cropperPosition.x1 += overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n            cropperPosition.y2 -= overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n          }\n          break;\n        case 'center':\n          cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n          cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n          const overflowX1 = Math.max(0 - cropperPosition.x1, 0);\n          const overflowX2 = Math.max(cropperPosition.x2 - maxSize.width, 0);\n          const overflowY1 = Math.max(cropperPosition.y2 - maxSize.height, 0);\n          const overflowY2 = Math.max(0 - cropperPosition.y1, 0);\n          if (overflowX1 > 0 || overflowX2 > 0 || overflowY1 > 0 || overflowY2 > 0) {\n            cropperPosition.x1 += overflowY1 * settings.aspectRatio > overflowX1 ? overflowY1 * settings.aspectRatio : overflowX1;\n            cropperPosition.x2 -= overflowY2 * settings.aspectRatio > overflowX2 ? overflowY2 * settings.aspectRatio : overflowX2;\n            cropperPosition.y1 += overflowY2 * settings.aspectRatio > overflowX2 ? overflowY2 : overflowX2 / settings.aspectRatio;\n            cropperPosition.y2 -= overflowY1 * settings.aspectRatio > overflowX1 ? overflowY1 : overflowX1 / settings.aspectRatio;\n          }\n          break;\n      }\n    }\n    getClientX(event) {\n      return event.touches?.[0].clientX || event.clientX || 0;\n    }\n    getClientY(event) {\n      return event.touches?.[0].clientY || event.clientY || 0;\n    }\n  }\n  CropperPositionService.ɵfac = function CropperPositionService_Factory(t) {\n    return new (t || CropperPositionService)();\n  };\n  CropperPositionService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CropperPositionService,\n    factory: CropperPositionService.ɵfac,\n    providedIn: 'root'\n  });\n  return CropperPositionService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// Black 2x1 JPEG, with the following meta information set:\n// - EXIF Orientation: 6 (Rotated 90° CCW)\n// Source: https://github.com/blueimp/JavaScript-Load-Image\nconst testAutoOrientationImageURL = 'data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAA' + 'AAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBA' + 'QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE' + 'BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/x' + 'ABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAA' + 'AAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==';\nfunction supportsAutomaticRotation() {\n  return new Promise(resolve => {\n    const img = new Image();\n    img.onload = () => {\n      // Check if browser supports automatic image orientation:\n      const supported = img.width === 1 && img.height === 2;\n      resolve(supported);\n    };\n    img.src = testAutoOrientationImageURL;\n  });\n}\nfunction getTransformationsFromExifData(exifRotationOrArrayBuffer) {\n  if (typeof exifRotationOrArrayBuffer === 'object') {\n    exifRotationOrArrayBuffer = getExifRotation(exifRotationOrArrayBuffer);\n  }\n  switch (exifRotationOrArrayBuffer) {\n    case 2:\n      return {\n        rotate: 0,\n        flip: true\n      };\n    case 3:\n      return {\n        rotate: 2,\n        flip: false\n      };\n    case 4:\n      return {\n        rotate: 2,\n        flip: true\n      };\n    case 5:\n      return {\n        rotate: 1,\n        flip: true\n      };\n    case 6:\n      return {\n        rotate: 1,\n        flip: false\n      };\n    case 7:\n      return {\n        rotate: 3,\n        flip: true\n      };\n    case 8:\n      return {\n        rotate: 3,\n        flip: false\n      };\n    default:\n      return {\n        rotate: 0,\n        flip: false\n      };\n  }\n}\nfunction getExifRotation(arrayBuffer) {\n  const view = new DataView(arrayBuffer);\n  if (view.getUint16(0, false) !== 0xFFD8) {\n    return -2;\n  }\n  const length = view.byteLength;\n  let offset = 2;\n  while (offset < length) {\n    if (view.getUint16(offset + 2, false) <= 8) return -1;\n    const marker = view.getUint16(offset, false);\n    offset += 2;\n    if (marker == 0xFFE1) {\n      if (view.getUint32(offset += 2, false) !== 0x45786966) {\n        return -1;\n      }\n      const little = view.getUint16(offset += 6, false) == 0x4949;\n      offset += view.getUint32(offset + 4, little);\n      const tags = view.getUint16(offset, little);\n      offset += 2;\n      for (let i = 0; i < tags; i++) {\n        if (view.getUint16(offset + i * 12, little) == 0x0112) {\n          return view.getUint16(offset + i * 12 + 8, little);\n        }\n      }\n    } else if ((marker & 0xFF00) !== 0xFF00) {\n      break;\n    } else {\n      offset += view.getUint16(offset, false);\n    }\n  }\n  return -1;\n}\nlet LoadImageService = /*#__PURE__*/(() => {\n  class LoadImageService {\n    constructor() {\n      this.autoRotateSupported = supportsAutomaticRotation();\n    }\n    loadImageFile(file, cropperSettings) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const arrayBuffer = yield file.arrayBuffer();\n        return yield _this2.checkImageTypeAndLoadImageFromArrayBuffer(arrayBuffer, file.type, cropperSettings);\n      })();\n    }\n    checkImageTypeAndLoadImageFromArrayBuffer(arrayBuffer, imageType, cropperSettings) {\n      if (!this.isValidImageType(imageType)) {\n        return Promise.reject(new Error('Invalid image type'));\n      }\n      return this.loadImageFromArrayBuffer(arrayBuffer, cropperSettings, imageType);\n    }\n    isValidImageType(type) {\n      return /image\\/(png|jpg|jpeg|bmp|gif|tiff|svg|webp|x-icon|vnd.microsoft.icon)/.test(type);\n    }\n    loadImageFromURL(url, cropperSettings) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const res = yield fetch(url);\n        const blob = yield res.blob();\n        const buffer = yield blob.arrayBuffer();\n        return yield _this3.loadImageFromArrayBuffer(buffer, cropperSettings, blob.type);\n      })();\n    }\n    loadBase64Image(imageBase64, cropperSettings) {\n      const arrayBuffer = this.base64ToArrayBuffer(imageBase64);\n      return this.loadImageFromArrayBuffer(arrayBuffer, cropperSettings);\n    }\n    base64ToArrayBuffer(imageBase64) {\n      imageBase64 = imageBase64.replace(/^data:([^;]+);base64,/gmi, '');\n      const binaryString = atob(imageBase64);\n      const len = binaryString.length;\n      const bytes = new Uint8Array(len);\n      for (let i = 0; i < len; i++) {\n        bytes[i] = binaryString.charCodeAt(i);\n      }\n      return bytes.buffer;\n    }\n    loadImageFromArrayBuffer(arrayBuffer, cropperSettings, imageType) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        const res = yield new Promise( /*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (resolve, reject) {\n            try {\n              const blob = new Blob([arrayBuffer], imageType ? {\n                type: imageType\n              } : undefined);\n              const objectUrl = URL.createObjectURL(blob);\n              const originalImage = new Image();\n              const isSvg = imageType === 'image/svg+xml';\n              const originalImageSize = isSvg ? yield _this4.getSvgImageSize(blob) : undefined;\n              originalImage.onload = () => resolve({\n                originalImage,\n                originalImageSize,\n                originalObjectUrl: objectUrl,\n                originalArrayBuffer: arrayBuffer\n              });\n              originalImage.onerror = reject;\n              originalImage.src = objectUrl;\n            } catch (e) {\n              reject(e);\n            }\n          });\n          return function (_x, _x2) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n        return yield _this4.transformImageFromArrayBuffer(res, cropperSettings, res.originalImageSize != null);\n      })();\n    }\n    getSvgImageSize(blob) {\n      return _asyncToGenerator(function* () {\n        const parser = new DOMParser();\n        const doc = parser.parseFromString(yield blob.text(), 'image/svg+xml');\n        const svgElement = doc.querySelector('svg');\n        if (!svgElement) {\n          throw Error('Failed to parse SVG image');\n        }\n        const widthAttr = svgElement.getAttribute('width');\n        const heightAttr = svgElement.getAttribute('height');\n        if (widthAttr && heightAttr) {\n          return null;\n        }\n        const viewBoxAttr = svgElement.getAttribute('viewBox') || svgElement.getAttribute('viewbox');\n        if (viewBoxAttr) {\n          const viewBox = viewBoxAttr.split(' ');\n          return {\n            width: +viewBox[2],\n            height: +viewBox[3]\n          };\n        }\n        throw Error('Failed to load SVG image. SVG must have width + height or viewBox definition.');\n      })();\n    }\n    transformImageFromArrayBuffer(res, cropperSettings, forceTransform = false) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        const autoRotate = yield _this5.autoRotateSupported;\n        const exifTransform = getTransformationsFromExifData(autoRotate ? -1 : res.originalArrayBuffer);\n        if (!res.originalImage || !res.originalImage.complete) {\n          return Promise.reject(new Error('No image loaded'));\n        }\n        const loadedImage = {\n          original: {\n            objectUrl: res.originalObjectUrl,\n            image: res.originalImage,\n            size: res.originalImageSize ?? {\n              width: res.originalImage.naturalWidth,\n              height: res.originalImage.naturalHeight\n            }\n          },\n          exifTransform\n        };\n        return _this5.transformLoadedImage(loadedImage, cropperSettings, forceTransform);\n      })();\n    }\n    transformLoadedImage(loadedImage, cropperSettings, forceTransform = false) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        const canvasRotation = cropperSettings.canvasRotation + loadedImage.exifTransform.rotate;\n        const originalSize = loadedImage.original.size;\n        if (!forceTransform && canvasRotation === 0 && !loadedImage.exifTransform.flip && !cropperSettings.containWithinAspectRatio) {\n          return {\n            original: {\n              objectUrl: loadedImage.original.objectUrl,\n              image: loadedImage.original.image,\n              size: {\n                ...originalSize\n              }\n            },\n            transformed: {\n              objectUrl: loadedImage.original.objectUrl,\n              image: loadedImage.original.image,\n              size: {\n                ...originalSize\n              }\n            },\n            exifTransform: loadedImage.exifTransform\n          };\n        }\n        const transformedSize = _this6.getTransformedSize(originalSize, loadedImage.exifTransform, cropperSettings);\n        const canvas = document.createElement('canvas');\n        canvas.width = transformedSize.width;\n        canvas.height = transformedSize.height;\n        const ctx = canvas.getContext('2d');\n        ctx?.setTransform(loadedImage.exifTransform.flip ? -1 : 1, 0, 0, 1, canvas.width / 2, canvas.height / 2);\n        ctx?.rotate(Math.PI * (canvasRotation / 2));\n        ctx?.drawImage(loadedImage.original.image, -originalSize.width / 2, -originalSize.height / 2);\n        const blob = yield new Promise(resolve => canvas.toBlob(resolve, cropperSettings.format));\n        if (!blob) {\n          throw new Error('Failed to get Blob for transformed image.');\n        }\n        const objectUrl = URL.createObjectURL(blob);\n        const transformedImage = yield _this6.loadImageFromObjectUrl(objectUrl);\n        return {\n          original: {\n            objectUrl: loadedImage.original.objectUrl,\n            image: loadedImage.original.image,\n            size: {\n              ...originalSize\n            }\n          },\n          transformed: {\n            objectUrl: objectUrl,\n            image: transformedImage,\n            size: {\n              width: transformedImage.width,\n              height: transformedImage.height\n            }\n          },\n          exifTransform: loadedImage.exifTransform\n        };\n      })();\n    }\n    loadImageFromObjectUrl(objectUrl) {\n      return new Promise((resolve, reject) => {\n        const image = new Image();\n        image.onload = () => resolve(image);\n        image.onerror = reject;\n        image.src = objectUrl;\n      });\n    }\n    getTransformedSize(originalSize, exifTransform, cropperSettings) {\n      const canvasRotation = cropperSettings.canvasRotation + exifTransform.rotate;\n      if (cropperSettings.containWithinAspectRatio) {\n        if (canvasRotation % 2) {\n          const minWidthToContain = originalSize.width * cropperSettings.aspectRatio;\n          const minHeightToContain = originalSize.height / cropperSettings.aspectRatio;\n          return {\n            width: Math.max(originalSize.height, minWidthToContain),\n            height: Math.max(originalSize.width, minHeightToContain)\n          };\n        } else {\n          const minWidthToContain = originalSize.height * cropperSettings.aspectRatio;\n          const minHeightToContain = originalSize.width / cropperSettings.aspectRatio;\n          return {\n            width: Math.max(originalSize.width, minWidthToContain),\n            height: Math.max(originalSize.height, minHeightToContain)\n          };\n        }\n      }\n      if (canvasRotation % 2) {\n        return {\n          height: originalSize.width,\n          width: originalSize.height\n        };\n      }\n      return {\n        width: originalSize.width,\n        height: originalSize.height\n      };\n    }\n  }\n  LoadImageService.ɵfac = function LoadImageService_Factory(t) {\n    return new (t || LoadImageService)();\n  };\n  LoadImageService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LoadImageService,\n    factory: LoadImageService.ɵfac,\n    providedIn: 'root'\n  });\n  return LoadImageService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ImageCropperComponent = /*#__PURE__*/(() => {\n  class ImageCropperComponent {\n    constructor(cropService, cropperPositionService, loadImageService, sanitizer, cd, zone, hammerLoader) {\n      this.cropService = cropService;\n      this.cropperPositionService = cropperPositionService;\n      this.loadImageService = loadImageService;\n      this.sanitizer = sanitizer;\n      this.cd = cd;\n      this.zone = zone;\n      this.hammerLoader = hammerLoader;\n      this.settings = new CropperSettings();\n      this.setImageMaxSizeRetries = 0;\n      this.resizedWhileHidden = false;\n      this.marginLeft = '0px';\n      this.maxSize = {\n        width: 0,\n        height: 0\n      };\n      this.moveTypes = MoveTypes;\n      this.imageVisible = false;\n      this.cropperFrameAriaLabel = this.settings.cropperFrameAriaLabel;\n      this.output = this.settings.output;\n      this.format = this.settings.format;\n      this.transform = {};\n      this.maintainAspectRatio = this.settings.maintainAspectRatio;\n      this.aspectRatio = this.settings.aspectRatio;\n      this.resetCropOnAspectRatioChange = this.settings.resetCropOnAspectRatioChange;\n      this.resizeToWidth = this.settings.resizeToWidth;\n      this.resizeToHeight = this.settings.resizeToHeight;\n      this.cropperMinWidth = this.settings.cropperMinWidth;\n      this.cropperMinHeight = this.settings.cropperMinHeight;\n      this.cropperMaxHeight = this.settings.cropperMaxHeight;\n      this.cropperMaxWidth = this.settings.cropperMaxWidth;\n      this.cropperStaticWidth = this.settings.cropperStaticWidth;\n      this.cropperStaticHeight = this.settings.cropperStaticHeight;\n      this.canvasRotation = this.settings.canvasRotation;\n      this.initialStepSize = this.settings.initialStepSize;\n      this.roundCropper = this.settings.roundCropper;\n      this.onlyScaleDown = this.settings.onlyScaleDown;\n      this.imageQuality = this.settings.imageQuality;\n      this.autoCrop = this.settings.autoCrop;\n      this.backgroundColor = this.settings.backgroundColor;\n      this.containWithinAspectRatio = this.settings.containWithinAspectRatio;\n      this.hideResizeSquares = this.settings.hideResizeSquares;\n      this.allowMoveImage = false;\n      this.cropper = {\n        x1: -100,\n        y1: -100,\n        x2: 10000,\n        y2: 10000\n      };\n      this.alignImage = this.settings.alignImage;\n      this.disabled = false;\n      this.hidden = false;\n      this.imageCropped = new EventEmitter();\n      this.startCropImage = new EventEmitter();\n      this.imageLoaded = new EventEmitter();\n      this.cropperReady = new EventEmitter();\n      this.loadImageFailed = new EventEmitter();\n      this.transformChange = new EventEmitter();\n      this.reset();\n    }\n    ngOnChanges(changes) {\n      this.onChangesUpdateSettings(changes);\n      this.onChangesInputImage(changes);\n      if (this.loadedImage?.original.image.complete && (changes['containWithinAspectRatio'] || changes['canvasRotation'])) {\n        this.loadImageService.transformLoadedImage(this.loadedImage, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n      }\n      if (changes['cropper'] || changes['maintainAspectRatio'] || changes['aspectRatio']) {\n        this.setMaxSize();\n        this.setCropperScaledMinSize();\n        this.setCropperScaledMaxSize();\n        if (this.maintainAspectRatio && (this.resetCropOnAspectRatioChange || !this.aspectRatioIsCorrect()) && (changes['maintainAspectRatio'] || changes['aspectRatio'])) {\n          this.resetCropperPosition();\n        } else if (changes['cropper']) {\n          this.checkCropperPosition(false);\n          this.doAutoCrop();\n        }\n      }\n      if (changes['transform']) {\n        this.transform = this.transform || {};\n        this.setCssTransform();\n        this.doAutoCrop();\n      }\n      if (changes['hidden'] && this.resizedWhileHidden && !this.hidden) {\n        setTimeout(() => {\n          this.onResize();\n          this.resizedWhileHidden = false;\n        });\n      }\n    }\n    onChangesUpdateSettings(changes) {\n      this.settings.setOptionsFromChanges(changes);\n      if (this.settings.cropperStaticHeight && this.settings.cropperStaticWidth) {\n        this.hideResizeSquares = true;\n        this.settings.setOptions({\n          hideResizeSquares: true,\n          cropperMinWidth: this.settings.cropperStaticWidth,\n          cropperMinHeight: this.settings.cropperStaticHeight,\n          cropperMaxHeight: this.settings.cropperStaticHeight,\n          cropperMaxWidth: this.settings.cropperStaticWidth,\n          maintainAspectRatio: false\n        });\n      }\n    }\n    onChangesInputImage(changes) {\n      if (changes['imageChangedEvent'] || changes['imageURL'] || changes['imageBase64'] || changes['imageFile']) {\n        this.reset();\n      }\n      if (changes['imageChangedEvent'] && this.isValidImageChangedEvent()) {\n        this.loadImageFile(this.imageChangedEvent.target.files[0]);\n      }\n      if (changes['imageURL'] && this.imageURL) {\n        this.loadImageFromURL(this.imageURL);\n      }\n      if (changes['imageBase64'] && this.imageBase64) {\n        this.loadBase64Image(this.imageBase64);\n      }\n      if (changes['imageFile'] && this.imageFile) {\n        this.loadImageFile(this.imageFile);\n      }\n    }\n    isValidImageChangedEvent() {\n      return this.imageChangedEvent?.target?.files?.length > 0;\n    }\n    setCssTransform() {\n      const translateUnit = this.transform?.translateUnit || '%';\n      this.safeTransformStyle = this.sanitizer.bypassSecurityTrustStyle(`translate(${this.transform.translateH || 0}${translateUnit}, ${this.transform.translateV || 0}${translateUnit})` + ' scaleX(' + (this.transform.scale || 1) * (this.transform.flipH ? -1 : 1) + ')' + ' scaleY(' + (this.transform.scale || 1) * (this.transform.flipV ? -1 : 1) + ')' + ' rotate(' + (this.transform.rotate || 0) + 'deg)');\n    }\n    ngOnInit() {\n      this.settings.stepSize = this.initialStepSize;\n      this.activatePinchGesture();\n    }\n    reset() {\n      this.imageVisible = false;\n      this.loadedImage = undefined;\n      this.safeImgDataUrl = 'data:image/png;base64,iVBORw0KGg' + 'oAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQYV2NgAAIAAAU' + 'AAarVyFEAAAAASUVORK5CYII=';\n      this.moveStart = {\n        active: false,\n        type: null,\n        position: null,\n        x1: 0,\n        y1: 0,\n        x2: 0,\n        y2: 0,\n        clientX: 0,\n        clientY: 0\n      };\n      this.maxSize = {\n        width: 0,\n        height: 0\n      };\n      this.cropper.x1 = -100;\n      this.cropper.y1 = -100;\n      this.cropper.x2 = 10000;\n      this.cropper.y2 = 10000;\n    }\n    loadImageFile(file) {\n      this.loadImageService.loadImageFile(file, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n    }\n    loadBase64Image(imageBase64) {\n      this.loadImageService.loadBase64Image(imageBase64, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n    }\n    loadImageFromURL(url) {\n      this.loadImageService.loadImageFromURL(url, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n    }\n    setLoadedImage(loadedImage) {\n      this.loadedImage = loadedImage;\n      this.safeImgDataUrl = this.sanitizer.bypassSecurityTrustResourceUrl(loadedImage.transformed.objectUrl);\n      this.cd.markForCheck();\n    }\n    loadImageError(error) {\n      console.error(error);\n      this.loadImageFailed.emit();\n    }\n    imageLoadedInView() {\n      if (this.loadedImage != null) {\n        this.imageLoaded.emit(this.loadedImage);\n        this.setImageMaxSizeRetries = 0;\n        setTimeout(() => this.checkImageMaxSizeRecursively());\n      }\n    }\n    checkImageMaxSizeRecursively() {\n      if (this.setImageMaxSizeRetries > 40) {\n        this.loadImageFailed.emit();\n      } else if (this.sourceImageLoaded()) {\n        this.setMaxSize();\n        this.setCropperScaledMinSize();\n        this.setCropperScaledMaxSize();\n        this.resetCropperPosition();\n        this.cropperReady.emit({\n          ...this.maxSize\n        });\n        this.cd.markForCheck();\n      } else {\n        this.setImageMaxSizeRetries++;\n        setTimeout(() => this.checkImageMaxSizeRecursively(), 50);\n      }\n    }\n    sourceImageLoaded() {\n      return this.sourceImage?.nativeElement?.offsetWidth > 0;\n    }\n    onResize() {\n      if (!this.loadedImage) {\n        return;\n      }\n      if (this.hidden) {\n        this.resizedWhileHidden = true;\n      } else {\n        const oldMaxSize = {\n          ...this.maxSize\n        };\n        this.setMaxSize();\n        this.resizeCropperPosition(oldMaxSize);\n        this.setCropperScaledMinSize();\n        this.setCropperScaledMaxSize();\n      }\n    }\n    activatePinchGesture() {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        // Loads HammerJS via angular APIs if configured\n        yield _this7.hammerLoader?.();\n        const Hammer = window?.['Hammer'] || null;\n        if (Hammer) {\n          const hammer = new Hammer(_this7.wrapper.nativeElement);\n          hammer.get('pinch').set({\n            enable: true\n          });\n          hammer.on('pinchmove', _this7.onPinch.bind(_this7));\n          hammer.on('pinchend', _this7.pinchStop.bind(_this7));\n          hammer.on('pinchstart', _this7.startPinch.bind(_this7));\n        } else if (isDevMode()) {\n          console.warn('[NgxImageCropper] Could not find HammerJS - Pinch Gesture won\\'t work');\n        }\n      })();\n    }\n    resizeCropperPosition(oldMaxSize) {\n      if (oldMaxSize.width !== this.maxSize.width || oldMaxSize.height !== this.maxSize.height) {\n        this.cropper.x1 = this.cropper.x1 * this.maxSize.width / oldMaxSize.width;\n        this.cropper.x2 = this.cropper.x2 * this.maxSize.width / oldMaxSize.width;\n        this.cropper.y1 = this.cropper.y1 * this.maxSize.height / oldMaxSize.height;\n        this.cropper.y2 = this.cropper.y2 * this.maxSize.height / oldMaxSize.height;\n      }\n    }\n    resetCropperPosition() {\n      this.cropperPositionService.resetCropperPosition(this.sourceImage, this.cropper, this.settings, this.maxSize);\n      this.doAutoCrop();\n      this.imageVisible = true;\n    }\n    keyboardAccess(event) {\n      this.changeKeyboardStepSize(event);\n      this.keyboardMoveCropper(event);\n    }\n    changeKeyboardStepSize(event) {\n      const key = +event.key;\n      if (key >= 1 && key <= 9) {\n        this.settings.stepSize = key;\n      }\n    }\n    keyboardMoveCropper(event) {\n      const keyboardWhiteList = ['ArrowUp', 'ArrowDown', 'ArrowRight', 'ArrowLeft'];\n      if (!keyboardWhiteList.includes(event.key)) {\n        return;\n      }\n      const moveType = event.shiftKey ? MoveTypes.Resize : MoveTypes.Move;\n      const position = event.altKey ? getInvertedPositionForKey(event.key) : getPositionForKey(event.key);\n      const moveEvent = getEventForKey(event.key, this.settings.stepSize);\n      event.preventDefault();\n      event.stopPropagation();\n      this.startMove({\n        clientX: 0,\n        clientY: 0\n      }, moveType, position);\n      this.handleMouseMove(moveEvent);\n      this.handleMouseUp();\n    }\n    startMove(event, moveType, position = null) {\n      if (this.disabled || this.moveStart?.active && this.moveStart?.type === MoveTypes.Pinch || moveType === MoveTypes.Drag && !this.allowMoveImage) {\n        return;\n      }\n      if (event.preventDefault) {\n        event.preventDefault();\n      }\n      this.moveStart = {\n        active: true,\n        type: moveType,\n        position,\n        transform: {\n          ...this.transform\n        },\n        clientX: this.cropperPositionService.getClientX(event),\n        clientY: this.cropperPositionService.getClientY(event),\n        ...this.cropper\n      };\n      this.initMouseMove();\n    }\n    initMouseMove() {\n      merge(fromEvent(document, 'mousemove'), fromEvent(document, 'touchmove')).pipe(takeUntil(merge(fromEvent(document, 'mouseup'), fromEvent(document, 'touchend')).pipe(first()))).subscribe({\n        next: event => this.zone.run(() => {\n          this.handleMouseMove(event);\n          this.cd.markForCheck();\n        }),\n        complete: () => this.zone.run(() => {\n          this.handleMouseUp();\n          this.cd.markForCheck();\n        })\n      });\n    }\n    startPinch(event) {\n      if (!this.safeImgDataUrl) {\n        return;\n      }\n      if (event.preventDefault) {\n        event.preventDefault();\n      }\n      this.moveStart = {\n        active: true,\n        type: MoveTypes.Pinch,\n        position: 'center',\n        clientX: this.cropper.x1 + (this.cropper.x2 - this.cropper.x1) / 2,\n        clientY: this.cropper.y1 + (this.cropper.y2 - this.cropper.y1) / 2,\n        ...this.cropper\n      };\n    }\n    handleMouseMove(event) {\n      if (this.moveStart.active) {\n        if (event.stopPropagation) {\n          event.stopPropagation();\n        }\n        if (event.preventDefault) {\n          event.preventDefault();\n        }\n        if (this.moveStart.type === MoveTypes.Move) {\n          this.cropperPositionService.move(event, this.moveStart, this.cropper);\n          this.checkCropperPosition(true);\n        } else if (this.moveStart.type === MoveTypes.Resize) {\n          if (!this.cropperStaticWidth && !this.cropperStaticHeight) {\n            this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n          }\n          this.checkCropperPosition(false);\n        } else if (this.moveStart.type === MoveTypes.Drag) {\n          const diffX = this.cropperPositionService.getClientX(event) - this.moveStart.clientX;\n          const diffY = this.cropperPositionService.getClientY(event) - this.moveStart.clientY;\n          this.transform = {\n            ...this.transform,\n            translateH: (this.moveStart.transform?.translateH || 0) + diffX,\n            translateV: (this.moveStart.transform?.translateV || 0) + diffY\n          };\n          this.setCssTransform();\n        }\n      }\n    }\n    onPinch(event) {\n      if (this.moveStart.active) {\n        if (event.stopPropagation) {\n          event.stopPropagation();\n        }\n        if (event.preventDefault) {\n          event.preventDefault();\n        }\n        if (this.moveStart.type === MoveTypes.Pinch) {\n          this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n          this.checkCropperPosition(false);\n        }\n        this.cd.markForCheck();\n      }\n    }\n    setMaxSize() {\n      if (this.sourceImage) {\n        const sourceImageStyle = getComputedStyle(this.sourceImage.nativeElement);\n        this.maxSize.width = parseFloat(sourceImageStyle.width);\n        this.maxSize.height = parseFloat(sourceImageStyle.height);\n        this.marginLeft = this.sanitizer.bypassSecurityTrustStyle('calc(50% - ' + this.maxSize.width / 2 + 'px)');\n      }\n    }\n    setCropperScaledMinSize() {\n      if (this.loadedImage?.transformed?.image) {\n        this.setCropperScaledMinWidth();\n        this.setCropperScaledMinHeight();\n      } else {\n        this.settings.cropperScaledMinWidth = 20;\n        this.settings.cropperScaledMinHeight = 20;\n      }\n    }\n    setCropperScaledMinWidth() {\n      this.settings.cropperScaledMinWidth = this.cropperMinWidth > 0 ? Math.max(20, this.cropperMinWidth / this.loadedImage.transformed.image.width * this.maxSize.width) : 20;\n    }\n    setCropperScaledMinHeight() {\n      if (this.maintainAspectRatio) {\n        this.settings.cropperScaledMinHeight = Math.max(20, this.settings.cropperScaledMinWidth / this.aspectRatio);\n      } else if (this.cropperMinHeight > 0) {\n        this.settings.cropperScaledMinHeight = Math.max(20, this.cropperMinHeight / this.loadedImage.transformed.image.height * this.maxSize.height);\n      } else {\n        this.settings.cropperScaledMinHeight = 20;\n      }\n    }\n    setCropperScaledMaxSize() {\n      if (this.loadedImage?.transformed?.image) {\n        const ratio = this.loadedImage.transformed.size.width / this.maxSize.width;\n        this.settings.cropperScaledMaxWidth = this.cropperMaxWidth > 20 ? this.cropperMaxWidth / ratio : this.maxSize.width;\n        this.settings.cropperScaledMaxHeight = this.cropperMaxHeight > 20 ? this.cropperMaxHeight / ratio : this.maxSize.height;\n        if (this.maintainAspectRatio) {\n          if (this.settings.cropperScaledMaxWidth > this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n            this.settings.cropperScaledMaxWidth = this.settings.cropperScaledMaxHeight * this.aspectRatio;\n          } else if (this.settings.cropperScaledMaxWidth < this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n            this.settings.cropperScaledMaxHeight = this.settings.cropperScaledMaxWidth / this.aspectRatio;\n          }\n        }\n      } else {\n        this.settings.cropperScaledMaxWidth = this.maxSize.width;\n        this.settings.cropperScaledMaxHeight = this.maxSize.height;\n      }\n    }\n    checkCropperPosition(maintainSize = false) {\n      if (this.cropper.x1 < 0) {\n        this.cropper.x2 -= maintainSize ? this.cropper.x1 : 0;\n        this.cropper.x1 = 0;\n      }\n      if (this.cropper.y1 < 0) {\n        this.cropper.y2 -= maintainSize ? this.cropper.y1 : 0;\n        this.cropper.y1 = 0;\n      }\n      if (this.cropper.x2 > this.maxSize.width) {\n        this.cropper.x1 -= maintainSize ? this.cropper.x2 - this.maxSize.width : 0;\n        this.cropper.x2 = this.maxSize.width;\n      }\n      if (this.cropper.y2 > this.maxSize.height) {\n        this.cropper.y1 -= maintainSize ? this.cropper.y2 - this.maxSize.height : 0;\n        this.cropper.y2 = this.maxSize.height;\n      }\n    }\n    handleMouseUp() {\n      if (this.moveStart.active) {\n        this.moveStart.active = false;\n        if (this.moveStart?.type === MoveTypes.Drag) {\n          this.transformChange.emit(this.transform);\n        } else {\n          this.doAutoCrop();\n        }\n      }\n    }\n    pinchStop() {\n      if (this.moveStart.active) {\n        this.moveStart.active = false;\n        this.doAutoCrop();\n      }\n    }\n    doAutoCrop() {\n      if (this.autoCrop) {\n        void this.crop();\n      }\n    }\n    crop(output = this.settings.output) {\n      if (this.loadedImage?.transformed?.image != null) {\n        this.startCropImage.emit();\n        if (output === 'blob') {\n          return this.cropToBlob();\n        } else if (output === 'base64') {\n          return this.cropToBase64();\n        }\n      }\n      return null;\n    }\n    cropToBlob() {\n      var _this8 = this;\n      return new Promise((resolve, reject) => this.zone.run( /*#__PURE__*/_asyncToGenerator(function* () {\n        const result = yield _this8.cropService.crop(_this8.loadedImage, _this8.cropper, _this8.settings, 'blob', _this8.maxSize);\n        if (result) {\n          _this8.imageCropped.emit(result);\n          resolve(result);\n        } else {\n          reject('Crop image failed');\n        }\n      })));\n    }\n    cropToBase64() {\n      const result = this.cropService.crop(this.loadedImage, this.cropper, this.settings, 'base64', this.maxSize);\n      if (result) {\n        this.imageCropped.emit(result);\n        return result;\n      }\n      return null;\n    }\n    aspectRatioIsCorrect() {\n      const currentCropAspectRatio = (this.cropper.x2 - this.cropper.x1) / (this.cropper.y2 - this.cropper.y1);\n      return currentCropAspectRatio === this.aspectRatio;\n    }\n  }\n  ImageCropperComponent.ɵfac = function ImageCropperComponent_Factory(t) {\n    return new (t || ImageCropperComponent)(i0.ɵɵdirectiveInject(CropService), i0.ɵɵdirectiveInject(CropperPositionService), i0.ɵɵdirectiveInject(LoadImageService), i0.ɵɵdirectiveInject(i4.DomSanitizer), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(HAMMER_LOADER, 8));\n  };\n  ImageCropperComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ImageCropperComponent,\n    selectors: [[\"image-cropper\"]],\n    viewQuery: function ImageCropperComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sourceImage = _t.first);\n      }\n    },\n    hostVars: 6,\n    hostBindings: function ImageCropperComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function ImageCropperComponent_resize_HostBindingHandler() {\n          return ctx.onResize();\n        }, false, i0.ɵɵresolveWindow);\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"text-align\", ctx.alignImage);\n        i0.ɵɵclassProp(\"disabled\", ctx.disabled)(\"ngx-ix-hidden\", ctx.hidden);\n      }\n    },\n    inputs: {\n      imageChangedEvent: \"imageChangedEvent\",\n      imageURL: \"imageURL\",\n      imageBase64: \"imageBase64\",\n      imageFile: \"imageFile\",\n      imageAltText: \"imageAltText\",\n      cropperFrameAriaLabel: \"cropperFrameAriaLabel\",\n      output: \"output\",\n      format: \"format\",\n      transform: \"transform\",\n      maintainAspectRatio: \"maintainAspectRatio\",\n      aspectRatio: \"aspectRatio\",\n      resetCropOnAspectRatioChange: \"resetCropOnAspectRatioChange\",\n      resizeToWidth: \"resizeToWidth\",\n      resizeToHeight: \"resizeToHeight\",\n      cropperMinWidth: \"cropperMinWidth\",\n      cropperMinHeight: \"cropperMinHeight\",\n      cropperMaxHeight: \"cropperMaxHeight\",\n      cropperMaxWidth: \"cropperMaxWidth\",\n      cropperStaticWidth: \"cropperStaticWidth\",\n      cropperStaticHeight: \"cropperStaticHeight\",\n      canvasRotation: \"canvasRotation\",\n      initialStepSize: \"initialStepSize\",\n      roundCropper: \"roundCropper\",\n      onlyScaleDown: \"onlyScaleDown\",\n      imageQuality: \"imageQuality\",\n      autoCrop: \"autoCrop\",\n      backgroundColor: \"backgroundColor\",\n      containWithinAspectRatio: \"containWithinAspectRatio\",\n      hideResizeSquares: \"hideResizeSquares\",\n      allowMoveImage: \"allowMoveImage\",\n      cropper: \"cropper\",\n      alignImage: \"alignImage\",\n      disabled: \"disabled\",\n      hidden: \"hidden\"\n    },\n    outputs: {\n      imageCropped: \"imageCropped\",\n      startCropImage: \"startCropImage\",\n      imageLoaded: \"imageLoaded\",\n      cropperReady: \"cropperReady\",\n      loadImageFailed: \"loadImageFailed\",\n      transformChange: \"transformChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 5,\n    vars: 10,\n    consts: [[\"wrapper\", \"\"], [\"sourceImage\", \"\"], [\"class\", \"ngx-ic-source-image\", \"role\", \"presentation\", 3, \"src\", \"visibility\", \"transform\", \"ngx-ic-draggable\", \"load\", \"mousedown\", \"touchstart\", \"error\", 4, \"ngIf\"], [1, \"ngx-ic-overlay\"], [\"class\", \"ngx-ic-cropper\", \"tabindex\", \"0\", 3, \"ngx-ic-round\", \"top\", \"left\", \"width\", \"height\", \"margin-left\", \"visibility\", \"keydown\", 4, \"ngIf\"], [\"role\", \"presentation\", 1, \"ngx-ic-source-image\", 3, \"load\", \"mousedown\", \"touchstart\", \"error\", \"src\"], [\"tabindex\", \"0\", 1, \"ngx-ic-cropper\", 3, \"keydown\"], [\"role\", \"presentation\", 1, \"ngx-ic-move\", 3, \"mousedown\", \"touchstart\"], [4, \"ngIf\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize\", \"ngx-ic-topleft\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-square\"], [1, \"ngx-ic-resize\", \"ngx-ic-top\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize\", \"ngx-ic-topright\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize\", \"ngx-ic-right\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize\", \"ngx-ic-bottomright\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize\", \"ngx-ic-bottom\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize\", \"ngx-ic-bottomleft\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize\", \"ngx-ic-left\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize-bar\", \"ngx-ic-top\", 3, \"mousedown\", \"touchstart\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize-bar\", \"ngx-ic-right\", 3, \"mousedown\", \"touchstart\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize-bar\", \"ngx-ic-bottom\", 3, \"mousedown\", \"touchstart\"], [\"role\", \"presentation\", 1, \"ngx-ic-resize-bar\", \"ngx-ic-left\", 3, \"mousedown\", \"touchstart\"]],\n    template: function ImageCropperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", null, 0);\n        i0.ɵɵtemplate(2, ImageCropperComponent_img_2_Template, 2, 8, \"img\", 2);\n        i0.ɵɵelement(3, \"div\", 3);\n        i0.ɵɵtemplate(4, ImageCropperComponent_div_4_Template, 3, 16, \"div\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"background\", ctx.imageVisible && ctx.backgroundColor);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.safeImgDataUrl);\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"width\", ctx.maxSize.width, \"px\")(\"height\", ctx.maxSize.height, \"px\")(\"margin-left\", ctx.alignImage === \"center\" ? ctx.marginLeft : null);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.imageVisible);\n      }\n    },\n    dependencies: [i5.NgIf],\n    styles: [\"[_nghost-%COMP%]{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}[_nghost-%COMP%] > div[_ngcontent-%COMP%]{width:100%;position:relative}[_nghost-%COMP%] > div[_ngcontent-%COMP%]   img.ngx-ic-source-image[_ngcontent-%COMP%]{max-width:100%;max-height:100%;transform-origin:center}[_nghost-%COMP%] > div[_ngcontent-%COMP%]   img.ngx-ic-source-image.ngx-ic-draggable[_ngcontent-%COMP%]{user-drag:none;-webkit-user-drag:none;user-select:none;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;cursor:grab}[_nghost-%COMP%]   .ngx-ic-overlay[_ngcontent-%COMP%]{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{outline-width:100vh}}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:after{position:absolute;content:\\\"\\\";inset:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:focus   .ngx-ic-move[_ngcontent-%COMP%]{border-color:#1e90ff;border-width:2px}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]   .ngx-ic-square[_ngcontent-%COMP%]{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topleft[_ngcontent-%COMP%]{top:-12px;left:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-top[_ngcontent-%COMP%]{top:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topright[_ngcontent-%COMP%]{top:-12px;right:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-right[_ngcontent-%COMP%]{top:calc(50% - 12px);right:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomright[_ngcontent-%COMP%]{bottom:-12px;right:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomleft[_ngcontent-%COMP%]{bottom:-12px;left:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-left[_ngcontent-%COMP%]{top:calc(50% - 12px);left:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%]{position:absolute;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-top[_ngcontent-%COMP%]{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-right[_ngcontent-%COMP%]{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-left[_ngcontent-%COMP%]{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]{outline-color:transparent}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{border-radius:100%}.disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{display:none}.ngx-ix-hidden[_nghost-%COMP%]{display:none}\"],\n    changeDetection: 0\n  });\n  return ImageCropperComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ImageCropperModule = /*#__PURE__*/(() => {\n  class ImageCropperModule {}\n  ImageCropperModule.ɵfac = function ImageCropperModule_Factory(t) {\n    return new (t || ImageCropperModule)();\n  };\n  ImageCropperModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ImageCropperModule\n  });\n  ImageCropperModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return ImageCropperModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction base64ToFile(base64Image) {\n  const split = base64Image.split(',');\n  const type = split[0].replace('data:', '').replace(';base64', '');\n  const byteString = atob(split[1]);\n  const ab = new ArrayBuffer(byteString.length);\n  const ia = new Uint8Array(ab);\n  for (let i = 0; i < byteString.length; i += 1) {\n    ia[i] = byteString.charCodeAt(i);\n  }\n  return new Blob([ab], {\n    type\n  });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CropService, CropperSettings, ImageCropperComponent, ImageCropperModule, LoadImageService, base64ToFile, resizeCanvas };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "isDevMode", "Component", "ChangeDetectionStrategy", "Optional", "Inject", "ViewChild", "Input", "HostBinding", "Output", "HostListener", "NgModule", "i4", "HAMMER_LOADER", "takeUntil", "first", "merge", "fromEvent", "i5", "CommonModule", "_c0", "_c1", "ImageCropperComponent_img_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "ImageCropperComponent_img_2_Template_img_load_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "imageLoadedInView", "ImageCropperComponent_img_2_Template_img_mousedown_0_listener", "$event", "startMove", "moveTypes", "Drag", "ImageCropperComponent_img_2_Template_img_touchstart_0_listener", "ImageCropperComponent_img_2_Template_img_error_0_listener", "loadImageError", "ɵɵelementEnd", "ɵɵstyleProp", "imageVisible", "safeTransformStyle", "ɵɵclassProp", "disabled", "allowMoveImage", "ɵɵproperty", "safeImgDataUrl", "ɵɵsanitizeUrl", "ɵɵattribute", "imageAltText", "ImageCropperComponent_div_4_ng_container_2_Template", "_r4", "ɵɵelementContainerStart", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_1_listener", "Resize", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_1_listener", "ɵɵelement", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_5_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_5_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_9_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_9_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_13_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_13_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_17_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_17_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_18_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_18_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_19_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_19_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_20_listener", "ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_20_listener", "ɵɵelementContainerEnd", "ImageCropperComponent_div_4_Template", "_r3", "ImageCropperComponent_div_4_Template_div_keydown_0_listener", "keyboardAccess", "ImageCropperComponent_div_4_Template_div_mousedown_1_listener", "Move", "ImageCropperComponent_div_4_Template_div_touchstart_1_listener", "ɵɵtemplate", "cropper", "y1", "x1", "x2", "y2", "alignImage", "marginLeft", "roundCropper", "cropperFrameAriaLabel", "ɵɵadvance", "hideResizeSquares", "CropperSettings", "constructor", "format", "output", "maintainAspectRatio", "transform", "aspectRatio", "resetCropOnAspectRatioChange", "resizeToWidth", "resizeToHeight", "cropper<PERSON><PERSON><PERSON><PERSON><PERSON>", "cropperMinHeight", "cropperMaxHeight", "cropperMaxWidth", "cropperStaticWidth", "cropperStaticHeight", "canvasRotation", "initialStepSize", "onlyScaleDown", "imageQuality", "autoCrop", "backgroundColor", "containWithinAspectRatio", "cropperScaledMinWidth", "cropperScaledMinHeight", "cropperScaledMaxWidth", "cropperScaledMaxHeight", "stepSize", "setOptions", "options", "Object", "keys", "filter", "k", "for<PERSON>ach", "validateOptions", "setOptionsFromChanges", "changes", "currentValue", "Error", "MoveTypes", "getPositionForKey", "key", "getInvertedPositionForKey", "getEventForKey", "clientX", "clientY", "resizeCanvas", "canvas", "width", "height", "width_source", "height_source", "Math", "round", "ratio_w", "ratio_h", "ratio_w_half", "ceil", "ratio_h_half", "getContext", "img", "getImageData", "img2", "createImageData", "data", "data2", "j", "i", "center_y", "weight", "weights", "weights_alpha", "gx_r", "gx_g", "gx_b", "gx_a", "xx_start", "floor", "yy_start", "xx_stop", "yy_stop", "min", "yy", "dy", "abs", "center_x", "w0", "xx", "dx", "w", "sqrt", "pos_x", "putImageData", "percentage", "percent", "totalValue", "CropService", "crop", "loadedImage", "settings", "maxSize", "imagePosition", "getImagePosition", "cropCanvas", "document", "createElement", "fillStyle", "fillRect", "scaleX", "scale", "flipH", "scaleY", "flipV", "translateH", "translateV", "getCanvasTranslate", "transformedImage", "transformed", "setTransform", "size", "translate", "rotate", "PI", "drawImage", "image", "result", "cropperPosition", "offsetImagePosition", "getOffsetImagePosition", "resizeRatio", "getResizeRatio", "cropToBlob", "base64", "toDataURL", "getQuality", "_this", "_asyncToGenerator", "blob", "Promise", "resolve", "toBlob", "objectUrl", "URL", "createObjectURL", "translateUnit", "ratio", "getRatio", "out", "max", "exifTransform", "offsetX", "offsetY", "original", "ratioWidth", "ratioHeight", "ratios", "Array", "push", "length", "ɵfac", "CropService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "CropperPositionService", "resetCropperPosition", "sourceImage", "nativeElement", "cropper<PERSON>idth", "cropperHeight", "cropperHeightWithAspectRatio", "cropperWidthWithAspectRatio", "move", "event", "moveStart", "diffX", "getClientX", "diffY", "getClientY", "resize", "moveX", "moveY", "position", "newWidth", "newHeight", "checkAspectRatio", "overflowX", "overflowY", "overflowX1", "overflowX2", "overflowY1", "overflowY2", "touches", "CropperPositionService_Factory", "testAutoOrientationImageURL", "supportsAutomaticRotation", "Image", "onload", "supported", "src", "getTransformationsFromExifData", "exifRotationOrArrayBuffer", "getExifRotation", "flip", "arrayBuffer", "view", "DataView", "getUint16", "byteLength", "offset", "marker", "getUint32", "little", "tags", "LoadImageService", "autoRotateSupported", "loadImageFile", "file", "cropperSettings", "_this2", "checkImageTypeAndLoadImageFromArrayBuffer", "type", "imageType", "isValidImageType", "reject", "loadImageFromArrayBuffer", "test", "loadImageFromURL", "url", "_this3", "res", "fetch", "buffer", "loadBase64Image", "imageBase64", "base64ToArrayBuffer", "replace", "binaryString", "atob", "len", "bytes", "Uint8Array", "charCodeAt", "_this4", "_ref", "Blob", "undefined", "originalImage", "isSvg", "originalImageSize", "getSvgImageSize", "originalObjectUrl", "original<PERSON><PERSON>y<PERSON>uffer", "onerror", "e", "_x", "_x2", "apply", "arguments", "transformImageFromArrayBuffer", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc", "parseFromString", "text", "svgElement", "querySelector", "widthAttr", "getAttribute", "heightAttr", "viewBoxAttr", "viewBox", "split", "forceTransform", "_this5", "autoRotate", "complete", "naturalWidth", "naturalHeight", "transformLoadedImage", "_this6", "originalSize", "transformedSize", "getTransformedSize", "loadImageFromObjectUrl", "minWidthToContain", "minHeightToContain", "LoadImageService_Factory", "ImageCropperComponent", "cropService", "cropperPositionService", "loadImageService", "sanitizer", "cd", "zone", "<PERSON><PERSON><PERSON><PERSON>", "setImageMaxSizeRetries", "resizedWhileHidden", "hidden", "imageCropped", "startCropImage", "imageLoaded", "cropperReady", "loadImageFailed", "transformChange", "reset", "ngOnChanges", "onChangesUpdateSettings", "onChangesInputImage", "then", "setLoadedImage", "catch", "err", "setMaxSize", "setCropperScaledMinSize", "setCropperScaledMaxSize", "aspectRatioIsCorrect", "checkCropperPosition", "doAutoCrop", "setCssTransform", "setTimeout", "onResize", "isValidImageChangedEvent", "imageChangedEvent", "target", "files", "imageURL", "imageFile", "bypassSecurityTrustStyle", "ngOnInit", "activatePinchGesture", "active", "bypassSecurityTrustResourceUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "console", "emit", "checkImageMaxSizeRecursively", "sourceImageLoaded", "offsetWidth", "oldMaxSize", "resizeCropperPosition", "_this7", "Hammer", "window", "hammer", "wrapper", "get", "set", "enable", "on", "onPinch", "bind", "pinchStop", "startPinch", "warn", "changeKeyboardStepSize", "keyboardMoveCropper", "keyboardWhiteList", "includes", "moveType", "shift<PERSON>ey", "altKey", "moveEvent", "preventDefault", "stopPropagation", "handleMouseMove", "handleMouseUp", "Pinch", "initMouseMove", "pipe", "subscribe", "next", "run", "sourceImageStyle", "getComputedStyle", "parseFloat", "setCropperScaledMinWidth", "setCropperScaledMinHeight", "maintainSize", "cropToBase64", "_this8", "currentCropAspectRatio", "ImageCropperComponent_Factory", "ɵɵdirectiveInject", "Dom<PERSON><PERSON><PERSON>zer", "ChangeDetectorRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "ImageCropperComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "ImageCropperComponent_HostBindings", "ImageCropperComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "ImageCropperComponent_Template", "dependencies", "NgIf", "styles", "changeDetection", "ImageCropperModule", "ImageCropperModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "base64ToFile", "base64Image", "byteString", "ab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ia"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/ngx-image-cropper/fesm2020/ngx-image-cropper.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, isDevMode, Component, ChangeDetectionStrategy, Optional, Inject, ViewChild, Input, HostBinding, Output, HostListener, NgModule } from '@angular/core';\nimport * as i4 from '@angular/platform-browser';\nimport { HAMMER_LOADER } from '@angular/platform-browser';\nimport { takeUntil, first } from 'rxjs/operators';\nimport { merge, fromEvent } from 'rxjs';\nimport * as i5 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass CropperSettings {\n    constructor() {\n        // From options\n        this.format = 'png';\n        this.output = 'blob';\n        this.maintainAspectRatio = true;\n        this.transform = {};\n        this.aspectRatio = 1;\n        this.resetCropOnAspectRatioChange = true;\n        this.resizeToWidth = 0;\n        this.resizeToHeight = 0;\n        this.cropperMinWidth = 0;\n        this.cropperMinHeight = 0;\n        this.cropperMaxHeight = 0;\n        this.cropperMaxWidth = 0;\n        this.cropperStaticWidth = 0;\n        this.cropperStaticHeight = 0;\n        this.canvasRotation = 0;\n        this.initialStepSize = 3;\n        this.roundCropper = false;\n        this.onlyScaleDown = false;\n        this.imageQuality = 92;\n        this.autoCrop = true;\n        this.backgroundColor = null;\n        this.containWithinAspectRatio = false;\n        this.hideResizeSquares = false;\n        this.alignImage = 'center';\n        this.cropperFrameAriaLabel = 'Crop photo';\n        // Internal\n        this.cropperScaledMinWidth = 20;\n        this.cropperScaledMinHeight = 20;\n        this.cropperScaledMaxWidth = 20;\n        this.cropperScaledMaxHeight = 20;\n        this.stepSize = this.initialStepSize;\n    }\n    setOptions(options) {\n        Object.keys(options)\n            .filter((k) => k in this)\n            .forEach((k) => this[k] = options[k]);\n        this.validateOptions();\n    }\n    setOptionsFromChanges(changes) {\n        Object.keys(changes)\n            .filter((k) => k in this)\n            .forEach((k) => this[k] = changes[k].currentValue);\n        this.validateOptions();\n    }\n    validateOptions() {\n        if (this.maintainAspectRatio && !this.aspectRatio) {\n            throw new Error('`aspectRatio` should > 0 when `maintainAspectRatio` is enabled');\n        }\n    }\n}\n\nvar MoveTypes;\n(function (MoveTypes) {\n    MoveTypes[\"Drag\"] = \"drag\";\n    MoveTypes[\"Move\"] = \"move\";\n    MoveTypes[\"Resize\"] = \"resize\";\n    MoveTypes[\"Pinch\"] = \"pinch\";\n})(MoveTypes || (MoveTypes = {}));\n\nfunction getPositionForKey(key) {\n    switch (key) {\n        case 'ArrowUp':\n            return 'top';\n        case 'ArrowRight':\n            return 'right';\n        case 'ArrowDown':\n            return 'bottom';\n        case 'ArrowLeft':\n        default:\n            return 'left';\n    }\n}\nfunction getInvertedPositionForKey(key) {\n    switch (key) {\n        case 'ArrowUp':\n            return 'bottom';\n        case 'ArrowRight':\n            return 'left';\n        case 'ArrowDown':\n            return 'top';\n        case 'ArrowLeft':\n        default:\n            return 'right';\n    }\n}\nfunction getEventForKey(key, stepSize) {\n    switch (key) {\n        case 'ArrowUp':\n            return { clientX: 0, clientY: stepSize * -1 };\n        case 'ArrowRight':\n            return { clientX: stepSize, clientY: 0 };\n        case 'ArrowDown':\n            return { clientX: 0, clientY: stepSize };\n        case 'ArrowLeft':\n        default:\n            return { clientX: stepSize * -1, clientY: 0 };\n    }\n}\n\n/*\n * Hermite resize - fast image resize/resample using Hermite filter.\n * https://github.com/viliusle/Hermite-resize\n */\nfunction resizeCanvas(canvas, width, height) {\n    const width_source = canvas.width;\n    const height_source = canvas.height;\n    width = Math.round(width);\n    height = Math.round(height);\n    const ratio_w = width_source / width;\n    const ratio_h = height_source / height;\n    const ratio_w_half = Math.ceil(ratio_w / 2);\n    const ratio_h_half = Math.ceil(ratio_h / 2);\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n        const img = ctx.getImageData(0, 0, width_source, height_source);\n        const img2 = ctx.createImageData(width, height);\n        const data = img.data;\n        const data2 = img2.data;\n        for (let j = 0; j < height; j++) {\n            for (let i = 0; i < width; i++) {\n                const x2 = (i + j * width) * 4;\n                const center_y = j * ratio_h;\n                let weight = 0;\n                let weights = 0;\n                let weights_alpha = 0;\n                let gx_r = 0;\n                let gx_g = 0;\n                let gx_b = 0;\n                let gx_a = 0;\n                const xx_start = Math.floor(i * ratio_w);\n                const yy_start = Math.floor(j * ratio_h);\n                let xx_stop = Math.ceil((i + 1) * ratio_w);\n                let yy_stop = Math.ceil((j + 1) * ratio_h);\n                xx_stop = Math.min(xx_stop, width_source);\n                yy_stop = Math.min(yy_stop, height_source);\n                for (let yy = yy_start; yy < yy_stop; yy++) {\n                    const dy = Math.abs(center_y - yy) / ratio_h_half;\n                    const center_x = i * ratio_w;\n                    const w0 = dy * dy; //pre-calc part of w\n                    for (let xx = xx_start; xx < xx_stop; xx++) {\n                        const dx = Math.abs(center_x - xx) / ratio_w_half;\n                        const w = Math.sqrt(w0 + dx * dx);\n                        if (w >= 1) {\n                            //pixel too far\n                            continue;\n                        }\n                        //hermite filter\n                        weight = 2 * w * w * w - 3 * w * w + 1;\n                        const pos_x = 4 * (xx + yy * width_source);\n                        //alpha\n                        gx_a += weight * data[pos_x + 3];\n                        weights_alpha += weight;\n                        //colors\n                        if (data[pos_x + 3] < 255)\n                            weight = weight * data[pos_x + 3] / 250;\n                        gx_r += weight * data[pos_x];\n                        gx_g += weight * data[pos_x + 1];\n                        gx_b += weight * data[pos_x + 2];\n                        weights += weight;\n                    }\n                }\n                data2[x2] = gx_r / weights;\n                data2[x2 + 1] = gx_g / weights;\n                data2[x2 + 2] = gx_b / weights;\n                data2[x2 + 3] = gx_a / weights_alpha;\n            }\n        }\n        canvas.width = width;\n        canvas.height = height;\n        //draw\n        ctx.putImageData(img2, 0, 0);\n    }\n}\n\nfunction percentage(percent, totalValue) {\n    return (percent / 100) * totalValue;\n}\n\nclass CropService {\n    crop(loadedImage, cropper, settings, output, maxSize) {\n        const imagePosition = this.getImagePosition(loadedImage, cropper, settings, maxSize);\n        const width = imagePosition.x2 - imagePosition.x1;\n        const height = imagePosition.y2 - imagePosition.y1;\n        const cropCanvas = document.createElement('canvas');\n        cropCanvas.width = width;\n        cropCanvas.height = height;\n        const ctx = cropCanvas.getContext('2d');\n        if (!ctx) {\n            return null;\n        }\n        if (settings.backgroundColor != null) {\n            ctx.fillStyle = settings.backgroundColor;\n            ctx.fillRect(0, 0, width, height);\n        }\n        const scaleX = (settings.transform.scale || 1) * (settings.transform.flipH ? -1 : 1);\n        const scaleY = (settings.transform.scale || 1) * (settings.transform.flipV ? -1 : 1);\n        const { translateH, translateV } = this.getCanvasTranslate(loadedImage, settings, maxSize);\n        const transformedImage = loadedImage.transformed;\n        ctx.setTransform(scaleX, 0, 0, scaleY, transformedImage.size.width / 2 + translateH, transformedImage.size.height / 2 + translateV);\n        ctx.translate(-imagePosition.x1 / scaleX, -imagePosition.y1 / scaleY);\n        ctx.rotate((settings.transform.rotate || 0) * Math.PI / 180);\n        ctx.drawImage(transformedImage.image, -transformedImage.size.width / 2, -transformedImage.size.height / 2);\n        const result = {\n            width, height,\n            imagePosition,\n            cropperPosition: { ...cropper }\n        };\n        if (settings.containWithinAspectRatio) {\n            result.offsetImagePosition = this.getOffsetImagePosition(loadedImage, cropper, settings, maxSize);\n        }\n        const resizeRatio = this.getResizeRatio(width, height, settings);\n        if (resizeRatio !== 1) {\n            result.width = Math.round(width * resizeRatio);\n            result.height = settings.maintainAspectRatio\n                ? Math.round(result.width / settings.aspectRatio)\n                : Math.round(height * resizeRatio);\n            resizeCanvas(cropCanvas, result.width, result.height);\n        }\n        if (output === 'blob') {\n            return this.cropToBlob(result, cropCanvas, settings);\n        }\n        else {\n            result.base64 = cropCanvas.toDataURL('image/' + settings.format, this.getQuality(settings));\n            return result;\n        }\n    }\n    async cropToBlob(output, cropCanvas, settings) {\n        output.blob = await new Promise(resolve => cropCanvas.toBlob(resolve, 'image/' + settings.format, this.getQuality(settings)));\n        if (output.blob) {\n            output.objectUrl = URL.createObjectURL(output.blob);\n        }\n        return output;\n    }\n    getCanvasTranslate(loadedImage, settings, maxSize) {\n        if (settings.transform.translateUnit === 'px') {\n            const ratio = this.getRatio(loadedImage, maxSize);\n            return {\n                translateH: (settings.transform.translateH || 0) * ratio,\n                translateV: (settings.transform.translateV || 0) * ratio\n            };\n        }\n        else {\n            return {\n                translateH: settings.transform.translateH ? percentage(settings.transform.translateH, loadedImage.transformed.size.width) : 0,\n                translateV: settings.transform.translateV ? percentage(settings.transform.translateV, loadedImage.transformed.size.height) : 0\n            };\n        }\n    }\n    getRatio(loadedImage, maxSize) {\n        return loadedImage.transformed.size.width / maxSize.width;\n    }\n    getImagePosition(loadedImage, cropper, settings, maxSize) {\n        const ratio = this.getRatio(loadedImage, maxSize);\n        const out = {\n            x1: Math.round(cropper.x1 * ratio),\n            y1: Math.round(cropper.y1 * ratio),\n            x2: Math.round(cropper.x2 * ratio),\n            y2: Math.round(cropper.y2 * ratio)\n        };\n        if (!settings.containWithinAspectRatio) {\n            out.x1 = Math.max(out.x1, 0);\n            out.y1 = Math.max(out.y1, 0);\n            out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n            out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n        }\n        return out;\n    }\n    getOffsetImagePosition(loadedImage, cropper, settings, maxSize) {\n        const canvasRotation = settings.canvasRotation + loadedImage.exifTransform.rotate;\n        const ratio = this.getRatio(loadedImage, maxSize);\n        let offsetX;\n        let offsetY;\n        if (canvasRotation % 2) {\n            offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.height) / 2;\n            offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.width) / 2;\n        }\n        else {\n            offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.width) / 2;\n            offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.height) / 2;\n        }\n        const out = {\n            x1: Math.round(cropper.x1 * ratio) - offsetX,\n            y1: Math.round(cropper.y1 * ratio) - offsetY,\n            x2: Math.round(cropper.x2 * ratio) - offsetX,\n            y2: Math.round(cropper.y2 * ratio) - offsetY\n        };\n        if (!settings.containWithinAspectRatio) {\n            out.x1 = Math.max(out.x1, 0);\n            out.y1 = Math.max(out.y1, 0);\n            out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n            out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n        }\n        return out;\n    }\n    getResizeRatio(width, height, settings) {\n        const ratioWidth = settings.resizeToWidth / width;\n        const ratioHeight = settings.resizeToHeight / height;\n        const ratios = new Array();\n        if (settings.resizeToWidth > 0) {\n            ratios.push(ratioWidth);\n        }\n        if (settings.resizeToHeight > 0) {\n            ratios.push(ratioHeight);\n        }\n        const result = ratios.length === 0 ? 1 : Math.min(...ratios);\n        if (result > 1 && !settings.onlyScaleDown) {\n            return result;\n        }\n        return Math.min(result, 1);\n    }\n    getQuality(settings) {\n        return Math.min(1, Math.max(0, settings.imageQuality / 100));\n    }\n}\nCropService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: CropService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nCropService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: CropService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: CropService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass CropperPositionService {\n    resetCropperPosition(sourceImage, cropperPosition, settings, maxSize) {\n        if (!sourceImage?.nativeElement) {\n            return;\n        }\n        if (settings.cropperStaticHeight && settings.cropperStaticWidth) {\n            cropperPosition.x1 = 0;\n            cropperPosition.x2 = maxSize.width > settings.cropperStaticWidth ?\n                settings.cropperStaticWidth : maxSize.width;\n            cropperPosition.y1 = 0;\n            cropperPosition.y2 = maxSize.height > settings.cropperStaticHeight ?\n                settings.cropperStaticHeight : maxSize.height;\n        }\n        else {\n            const cropperWidth = Math.min(settings.cropperScaledMaxWidth, maxSize.width);\n            const cropperHeight = Math.min(settings.cropperScaledMaxHeight, maxSize.height);\n            if (!settings.maintainAspectRatio) {\n                cropperPosition.x1 = 0;\n                cropperPosition.x2 = cropperWidth;\n                cropperPosition.y1 = 0;\n                cropperPosition.y2 = cropperHeight;\n            }\n            else if (maxSize.width / settings.aspectRatio < maxSize.height) {\n                cropperPosition.x1 = 0;\n                cropperPosition.x2 = cropperWidth;\n                const cropperHeightWithAspectRatio = cropperWidth / settings.aspectRatio;\n                cropperPosition.y1 = (maxSize.height - cropperHeightWithAspectRatio) / 2;\n                cropperPosition.y2 = cropperPosition.y1 + cropperHeightWithAspectRatio;\n            }\n            else {\n                cropperPosition.y1 = 0;\n                cropperPosition.y2 = cropperHeight;\n                const cropperWidthWithAspectRatio = cropperHeight * settings.aspectRatio;\n                cropperPosition.x1 = (maxSize.width - cropperWidthWithAspectRatio) / 2;\n                cropperPosition.x2 = cropperPosition.x1 + cropperWidthWithAspectRatio;\n            }\n        }\n    }\n    move(event, moveStart, cropperPosition) {\n        const diffX = this.getClientX(event) - moveStart.clientX;\n        const diffY = this.getClientY(event) - moveStart.clientY;\n        cropperPosition.x1 = moveStart.x1 + diffX;\n        cropperPosition.y1 = moveStart.y1 + diffY;\n        cropperPosition.x2 = moveStart.x2 + diffX;\n        cropperPosition.y2 = moveStart.y2 + diffY;\n    }\n    resize(event, moveStart, cropperPosition, maxSize, settings) {\n        const moveX = this.getClientX(event) - moveStart.clientX;\n        const moveY = this.getClientY(event) - moveStart.clientY;\n        switch (moveStart.position) {\n            case 'left':\n                cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n                break;\n            case 'topleft':\n                cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n                cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n                break;\n            case 'top':\n                cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n                break;\n            case 'topright':\n                cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n                cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n                break;\n            case 'right':\n                cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n                break;\n            case 'bottomright':\n                cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n                cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n                break;\n            case 'bottom':\n                cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n                break;\n            case 'bottomleft':\n                cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n                cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n                break;\n            case 'center':\n                const scale = event.scale;\n                const newWidth = Math.min(Math.max(settings.cropperScaledMinWidth, (Math.abs(moveStart.x2 - moveStart.x1)) * scale), settings.cropperScaledMaxWidth);\n                const newHeight = Math.min(Math.max(settings.cropperScaledMinHeight, (Math.abs(moveStart.y2 - moveStart.y1)) * scale), settings.cropperScaledMaxHeight);\n                cropperPosition.x1 = moveStart.clientX - newWidth / 2;\n                cropperPosition.x2 = moveStart.clientX + newWidth / 2;\n                cropperPosition.y1 = moveStart.clientY - newHeight / 2;\n                cropperPosition.y2 = moveStart.clientY + newHeight / 2;\n                if (cropperPosition.x1 < 0) {\n                    cropperPosition.x2 -= cropperPosition.x1;\n                    cropperPosition.x1 = 0;\n                }\n                else if (cropperPosition.x2 > maxSize.width) {\n                    cropperPosition.x1 -= (cropperPosition.x2 - maxSize.width);\n                    cropperPosition.x2 = maxSize.width;\n                }\n                if (cropperPosition.y1 < 0) {\n                    cropperPosition.y2 -= cropperPosition.y1;\n                    cropperPosition.y1 = 0;\n                }\n                else if (cropperPosition.y2 > maxSize.height) {\n                    cropperPosition.y1 -= (cropperPosition.y2 - maxSize.height);\n                    cropperPosition.y2 = maxSize.height;\n                }\n                break;\n        }\n        if (settings.maintainAspectRatio) {\n            this.checkAspectRatio(moveStart.position, cropperPosition, maxSize, settings);\n        }\n    }\n    checkAspectRatio(position, cropperPosition, maxSize, settings) {\n        let overflowX = 0;\n        let overflowY = 0;\n        switch (position) {\n            case 'top':\n                cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y1 += (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'bottom':\n                cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y2 -= (overflowY * settings.aspectRatio) > overflowX ? overflowY : (overflowX / settings.aspectRatio);\n                }\n                break;\n            case 'topleft':\n                cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(0 - cropperPosition.x1, 0);\n                overflowY = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x1 += (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y1 += (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'topright':\n                cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y1 += (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'right':\n            case 'bottomright':\n                cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y2 -= (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'left':\n            case 'bottomleft':\n                cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(0 - cropperPosition.x1, 0);\n                overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x1 += (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y2 -= (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'center':\n                cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n                cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                const overflowX1 = Math.max(0 - cropperPosition.x1, 0);\n                const overflowX2 = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                const overflowY1 = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                const overflowY2 = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX1 > 0 || overflowX2 > 0 || overflowY1 > 0 || overflowY2 > 0) {\n                    cropperPosition.x1 += (overflowY1 * settings.aspectRatio) > overflowX1 ? (overflowY1 * settings.aspectRatio) : overflowX1;\n                    cropperPosition.x2 -= (overflowY2 * settings.aspectRatio) > overflowX2 ? (overflowY2 * settings.aspectRatio) : overflowX2;\n                    cropperPosition.y1 += (overflowY2 * settings.aspectRatio) > overflowX2 ? overflowY2 : overflowX2 / settings.aspectRatio;\n                    cropperPosition.y2 -= (overflowY1 * settings.aspectRatio) > overflowX1 ? overflowY1 : overflowX1 / settings.aspectRatio;\n                }\n                break;\n        }\n    }\n    getClientX(event) {\n        return event.touches?.[0].clientX || event.clientX || 0;\n    }\n    getClientY(event) {\n        return event.touches?.[0].clientY || event.clientY || 0;\n    }\n}\nCropperPositionService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: CropperPositionService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nCropperPositionService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: CropperPositionService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: CropperPositionService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n// Black 2x1 JPEG, with the following meta information set:\n// - EXIF Orientation: 6 (Rotated 90° CCW)\n// Source: https://github.com/blueimp/JavaScript-Load-Image\nconst testAutoOrientationImageURL = 'data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAA' +\n    'AAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBA' +\n    'QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE' +\n    'BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/x' +\n    'ABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAA' +\n    'AAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==';\nfunction supportsAutomaticRotation() {\n    return new Promise((resolve) => {\n        const img = new Image();\n        img.onload = () => {\n            // Check if browser supports automatic image orientation:\n            const supported = img.width === 1 && img.height === 2;\n            resolve(supported);\n        };\n        img.src = testAutoOrientationImageURL;\n    });\n}\nfunction getTransformationsFromExifData(exifRotationOrArrayBuffer) {\n    if (typeof exifRotationOrArrayBuffer === 'object') {\n        exifRotationOrArrayBuffer = getExifRotation(exifRotationOrArrayBuffer);\n    }\n    switch (exifRotationOrArrayBuffer) {\n        case 2:\n            return { rotate: 0, flip: true };\n        case 3:\n            return { rotate: 2, flip: false };\n        case 4:\n            return { rotate: 2, flip: true };\n        case 5:\n            return { rotate: 1, flip: true };\n        case 6:\n            return { rotate: 1, flip: false };\n        case 7:\n            return { rotate: 3, flip: true };\n        case 8:\n            return { rotate: 3, flip: false };\n        default:\n            return { rotate: 0, flip: false };\n    }\n}\nfunction getExifRotation(arrayBuffer) {\n    const view = new DataView(arrayBuffer);\n    if (view.getUint16(0, false) !== 0xFFD8) {\n        return -2;\n    }\n    const length = view.byteLength;\n    let offset = 2;\n    while (offset < length) {\n        if (view.getUint16(offset + 2, false) <= 8)\n            return -1;\n        const marker = view.getUint16(offset, false);\n        offset += 2;\n        if (marker == 0xFFE1) {\n            if (view.getUint32(offset += 2, false) !== 0x45786966) {\n                return -1;\n            }\n            const little = view.getUint16(offset += 6, false) == 0x4949;\n            offset += view.getUint32(offset + 4, little);\n            const tags = view.getUint16(offset, little);\n            offset += 2;\n            for (let i = 0; i < tags; i++) {\n                if (view.getUint16(offset + (i * 12), little) == 0x0112) {\n                    return view.getUint16(offset + (i * 12) + 8, little);\n                }\n            }\n        }\n        else if ((marker & 0xFF00) !== 0xFF00) {\n            break;\n        }\n        else {\n            offset += view.getUint16(offset, false);\n        }\n    }\n    return -1;\n}\n\nclass LoadImageService {\n    constructor() {\n        this.autoRotateSupported = supportsAutomaticRotation();\n    }\n    async loadImageFile(file, cropperSettings) {\n        const arrayBuffer = await file.arrayBuffer();\n        return await this.checkImageTypeAndLoadImageFromArrayBuffer(arrayBuffer, file.type, cropperSettings);\n    }\n    checkImageTypeAndLoadImageFromArrayBuffer(arrayBuffer, imageType, cropperSettings) {\n        if (!this.isValidImageType(imageType)) {\n            return Promise.reject(new Error('Invalid image type'));\n        }\n        return this.loadImageFromArrayBuffer(arrayBuffer, cropperSettings, imageType);\n    }\n    isValidImageType(type) {\n        return /image\\/(png|jpg|jpeg|bmp|gif|tiff|svg|webp|x-icon|vnd.microsoft.icon)/.test(type);\n    }\n    async loadImageFromURL(url, cropperSettings) {\n        const res = await fetch(url);\n        const blob = await res.blob();\n        const buffer = await blob.arrayBuffer();\n        return await this.loadImageFromArrayBuffer(buffer, cropperSettings, blob.type);\n    }\n    loadBase64Image(imageBase64, cropperSettings) {\n        const arrayBuffer = this.base64ToArrayBuffer(imageBase64);\n        return this.loadImageFromArrayBuffer(arrayBuffer, cropperSettings);\n    }\n    base64ToArrayBuffer(imageBase64) {\n        imageBase64 = imageBase64.replace(/^data:([^;]+);base64,/gmi, '');\n        const binaryString = atob(imageBase64);\n        const len = binaryString.length;\n        const bytes = new Uint8Array(len);\n        for (let i = 0; i < len; i++) {\n            bytes[i] = binaryString.charCodeAt(i);\n        }\n        return bytes.buffer;\n    }\n    async loadImageFromArrayBuffer(arrayBuffer, cropperSettings, imageType) {\n        const res = await new Promise(async (resolve, reject) => {\n            try {\n                const blob = new Blob([arrayBuffer], imageType ? { type: imageType } : undefined);\n                const objectUrl = URL.createObjectURL(blob);\n                const originalImage = new Image();\n                const isSvg = imageType === 'image/svg+xml';\n                const originalImageSize = isSvg ? await this.getSvgImageSize(blob) : undefined;\n                originalImage.onload = () => resolve({\n                    originalImage,\n                    originalImageSize,\n                    originalObjectUrl: objectUrl,\n                    originalArrayBuffer: arrayBuffer\n                });\n                originalImage.onerror = reject;\n                originalImage.src = objectUrl;\n            }\n            catch (e) {\n                reject(e);\n            }\n        });\n        return await this.transformImageFromArrayBuffer(res, cropperSettings, res.originalImageSize != null);\n    }\n    async getSvgImageSize(blob) {\n        const parser = new DOMParser();\n        const doc = parser.parseFromString(await blob.text(), 'image/svg+xml');\n        const svgElement = doc.querySelector('svg');\n        if (!svgElement) {\n            throw Error('Failed to parse SVG image');\n        }\n        const widthAttr = svgElement.getAttribute('width');\n        const heightAttr = svgElement.getAttribute('height');\n        if (widthAttr && heightAttr) {\n            return null;\n        }\n        const viewBoxAttr = svgElement.getAttribute('viewBox')\n            || svgElement.getAttribute('viewbox');\n        if (viewBoxAttr) {\n            const viewBox = viewBoxAttr.split(' ');\n            return {\n                width: +viewBox[2],\n                height: +viewBox[3]\n            };\n        }\n        throw Error('Failed to load SVG image. SVG must have width + height or viewBox definition.');\n    }\n    async transformImageFromArrayBuffer(res, cropperSettings, forceTransform = false) {\n        const autoRotate = await this.autoRotateSupported;\n        const exifTransform = getTransformationsFromExifData(autoRotate ? -1 : res.originalArrayBuffer);\n        if (!res.originalImage || !res.originalImage.complete) {\n            return Promise.reject(new Error('No image loaded'));\n        }\n        const loadedImage = {\n            original: {\n                objectUrl: res.originalObjectUrl,\n                image: res.originalImage,\n                size: res.originalImageSize ?? {\n                    width: res.originalImage.naturalWidth,\n                    height: res.originalImage.naturalHeight\n                }\n            },\n            exifTransform\n        };\n        return this.transformLoadedImage(loadedImage, cropperSettings, forceTransform);\n    }\n    async transformLoadedImage(loadedImage, cropperSettings, forceTransform = false) {\n        const canvasRotation = cropperSettings.canvasRotation + loadedImage.exifTransform.rotate;\n        const originalSize = loadedImage.original.size;\n        if (!forceTransform && canvasRotation === 0 && !loadedImage.exifTransform.flip && !cropperSettings.containWithinAspectRatio) {\n            return {\n                original: {\n                    objectUrl: loadedImage.original.objectUrl,\n                    image: loadedImage.original.image,\n                    size: { ...originalSize }\n                },\n                transformed: {\n                    objectUrl: loadedImage.original.objectUrl,\n                    image: loadedImage.original.image,\n                    size: { ...originalSize }\n                },\n                exifTransform: loadedImage.exifTransform\n            };\n        }\n        const transformedSize = this.getTransformedSize(originalSize, loadedImage.exifTransform, cropperSettings);\n        const canvas = document.createElement('canvas');\n        canvas.width = transformedSize.width;\n        canvas.height = transformedSize.height;\n        const ctx = canvas.getContext('2d');\n        ctx?.setTransform(loadedImage.exifTransform.flip ? -1 : 1, 0, 0, 1, canvas.width / 2, canvas.height / 2);\n        ctx?.rotate(Math.PI * (canvasRotation / 2));\n        ctx?.drawImage(loadedImage.original.image, -originalSize.width / 2, -originalSize.height / 2);\n        const blob = await new Promise(resolve => canvas.toBlob(resolve, cropperSettings.format));\n        if (!blob) {\n            throw new Error('Failed to get Blob for transformed image.');\n        }\n        const objectUrl = URL.createObjectURL(blob);\n        const transformedImage = await this.loadImageFromObjectUrl(objectUrl);\n        return {\n            original: {\n                objectUrl: loadedImage.original.objectUrl,\n                image: loadedImage.original.image,\n                size: { ...originalSize }\n            },\n            transformed: {\n                objectUrl: objectUrl,\n                image: transformedImage,\n                size: {\n                    width: transformedImage.width,\n                    height: transformedImage.height\n                }\n            },\n            exifTransform: loadedImage.exifTransform\n        };\n    }\n    loadImageFromObjectUrl(objectUrl) {\n        return new Promise(((resolve, reject) => {\n            const image = new Image();\n            image.onload = () => resolve(image);\n            image.onerror = reject;\n            image.src = objectUrl;\n        }));\n    }\n    getTransformedSize(originalSize, exifTransform, cropperSettings) {\n        const canvasRotation = cropperSettings.canvasRotation + exifTransform.rotate;\n        if (cropperSettings.containWithinAspectRatio) {\n            if (canvasRotation % 2) {\n                const minWidthToContain = originalSize.width * cropperSettings.aspectRatio;\n                const minHeightToContain = originalSize.height / cropperSettings.aspectRatio;\n                return {\n                    width: Math.max(originalSize.height, minWidthToContain),\n                    height: Math.max(originalSize.width, minHeightToContain)\n                };\n            }\n            else {\n                const minWidthToContain = originalSize.height * cropperSettings.aspectRatio;\n                const minHeightToContain = originalSize.width / cropperSettings.aspectRatio;\n                return {\n                    width: Math.max(originalSize.width, minWidthToContain),\n                    height: Math.max(originalSize.height, minHeightToContain)\n                };\n            }\n        }\n        if (canvasRotation % 2) {\n            return {\n                height: originalSize.width,\n                width: originalSize.height\n            };\n        }\n        return {\n            width: originalSize.width,\n            height: originalSize.height\n        };\n    }\n}\nLoadImageService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: LoadImageService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nLoadImageService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: LoadImageService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: LoadImageService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass ImageCropperComponent {\n    constructor(cropService, cropperPositionService, loadImageService, sanitizer, cd, zone, hammerLoader) {\n        this.cropService = cropService;\n        this.cropperPositionService = cropperPositionService;\n        this.loadImageService = loadImageService;\n        this.sanitizer = sanitizer;\n        this.cd = cd;\n        this.zone = zone;\n        this.hammerLoader = hammerLoader;\n        this.settings = new CropperSettings();\n        this.setImageMaxSizeRetries = 0;\n        this.resizedWhileHidden = false;\n        this.marginLeft = '0px';\n        this.maxSize = {\n            width: 0,\n            height: 0\n        };\n        this.moveTypes = MoveTypes;\n        this.imageVisible = false;\n        this.cropperFrameAriaLabel = this.settings.cropperFrameAriaLabel;\n        this.output = this.settings.output;\n        this.format = this.settings.format;\n        this.transform = {};\n        this.maintainAspectRatio = this.settings.maintainAspectRatio;\n        this.aspectRatio = this.settings.aspectRatio;\n        this.resetCropOnAspectRatioChange = this.settings.resetCropOnAspectRatioChange;\n        this.resizeToWidth = this.settings.resizeToWidth;\n        this.resizeToHeight = this.settings.resizeToHeight;\n        this.cropperMinWidth = this.settings.cropperMinWidth;\n        this.cropperMinHeight = this.settings.cropperMinHeight;\n        this.cropperMaxHeight = this.settings.cropperMaxHeight;\n        this.cropperMaxWidth = this.settings.cropperMaxWidth;\n        this.cropperStaticWidth = this.settings.cropperStaticWidth;\n        this.cropperStaticHeight = this.settings.cropperStaticHeight;\n        this.canvasRotation = this.settings.canvasRotation;\n        this.initialStepSize = this.settings.initialStepSize;\n        this.roundCropper = this.settings.roundCropper;\n        this.onlyScaleDown = this.settings.onlyScaleDown;\n        this.imageQuality = this.settings.imageQuality;\n        this.autoCrop = this.settings.autoCrop;\n        this.backgroundColor = this.settings.backgroundColor;\n        this.containWithinAspectRatio = this.settings.containWithinAspectRatio;\n        this.hideResizeSquares = this.settings.hideResizeSquares;\n        this.allowMoveImage = false;\n        this.cropper = {\n            x1: -100,\n            y1: -100,\n            x2: 10000,\n            y2: 10000\n        };\n        this.alignImage = this.settings.alignImage;\n        this.disabled = false;\n        this.hidden = false;\n        this.imageCropped = new EventEmitter();\n        this.startCropImage = new EventEmitter();\n        this.imageLoaded = new EventEmitter();\n        this.cropperReady = new EventEmitter();\n        this.loadImageFailed = new EventEmitter();\n        this.transformChange = new EventEmitter();\n        this.reset();\n    }\n    ngOnChanges(changes) {\n        this.onChangesUpdateSettings(changes);\n        this.onChangesInputImage(changes);\n        if (this.loadedImage?.original.image.complete && (changes['containWithinAspectRatio'] || changes['canvasRotation'])) {\n            this.loadImageService\n                .transformLoadedImage(this.loadedImage, this.settings)\n                .then((res) => this.setLoadedImage(res))\n                .catch((err) => this.loadImageError(err));\n        }\n        if (changes['cropper'] || changes['maintainAspectRatio'] || changes['aspectRatio']) {\n            this.setMaxSize();\n            this.setCropperScaledMinSize();\n            this.setCropperScaledMaxSize();\n            if (this.maintainAspectRatio &&\n                (this.resetCropOnAspectRatioChange || !this.aspectRatioIsCorrect()) &&\n                (changes['maintainAspectRatio'] || changes['aspectRatio'])) {\n                this.resetCropperPosition();\n            }\n            else if (changes['cropper']) {\n                this.checkCropperPosition(false);\n                this.doAutoCrop();\n            }\n        }\n        if (changes['transform']) {\n            this.transform = this.transform || {};\n            this.setCssTransform();\n            this.doAutoCrop();\n        }\n        if (changes['hidden'] && this.resizedWhileHidden && !this.hidden) {\n            setTimeout(() => {\n                this.onResize();\n                this.resizedWhileHidden = false;\n            });\n        }\n    }\n    onChangesUpdateSettings(changes) {\n        this.settings.setOptionsFromChanges(changes);\n        if (this.settings.cropperStaticHeight && this.settings.cropperStaticWidth) {\n            this.hideResizeSquares = true;\n            this.settings.setOptions({\n                hideResizeSquares: true,\n                cropperMinWidth: this.settings.cropperStaticWidth,\n                cropperMinHeight: this.settings.cropperStaticHeight,\n                cropperMaxHeight: this.settings.cropperStaticHeight,\n                cropperMaxWidth: this.settings.cropperStaticWidth,\n                maintainAspectRatio: false\n            });\n        }\n    }\n    onChangesInputImage(changes) {\n        if (changes['imageChangedEvent'] || changes['imageURL'] || changes['imageBase64'] || changes['imageFile']) {\n            this.reset();\n        }\n        if (changes['imageChangedEvent'] && this.isValidImageChangedEvent()) {\n            this.loadImageFile(this.imageChangedEvent.target.files[0]);\n        }\n        if (changes['imageURL'] && this.imageURL) {\n            this.loadImageFromURL(this.imageURL);\n        }\n        if (changes['imageBase64'] && this.imageBase64) {\n            this.loadBase64Image(this.imageBase64);\n        }\n        if (changes['imageFile'] && this.imageFile) {\n            this.loadImageFile(this.imageFile);\n        }\n    }\n    isValidImageChangedEvent() {\n        return this.imageChangedEvent?.target?.files?.length > 0;\n    }\n    setCssTransform() {\n        const translateUnit = this.transform?.translateUnit || '%';\n        this.safeTransformStyle = this.sanitizer.bypassSecurityTrustStyle(`translate(${this.transform.translateH || 0}${translateUnit}, ${this.transform.translateV || 0}${translateUnit})` +\n            ' scaleX(' + (this.transform.scale || 1) * (this.transform.flipH ? -1 : 1) + ')' +\n            ' scaleY(' + (this.transform.scale || 1) * (this.transform.flipV ? -1 : 1) + ')' +\n            ' rotate(' + (this.transform.rotate || 0) + 'deg)');\n    }\n    ngOnInit() {\n        this.settings.stepSize = this.initialStepSize;\n        this.activatePinchGesture();\n    }\n    reset() {\n        this.imageVisible = false;\n        this.loadedImage = undefined;\n        this.safeImgDataUrl = 'data:image/png;base64,iVBORw0KGg'\n            + 'oAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQYV2NgAAIAAAU'\n            + 'AAarVyFEAAAAASUVORK5CYII=';\n        this.moveStart = {\n            active: false,\n            type: null,\n            position: null,\n            x1: 0,\n            y1: 0,\n            x2: 0,\n            y2: 0,\n            clientX: 0,\n            clientY: 0\n        };\n        this.maxSize = {\n            width: 0,\n            height: 0\n        };\n        this.cropper.x1 = -100;\n        this.cropper.y1 = -100;\n        this.cropper.x2 = 10000;\n        this.cropper.y2 = 10000;\n    }\n    loadImageFile(file) {\n        this.loadImageService\n            .loadImageFile(file, this.settings)\n            .then((res) => this.setLoadedImage(res))\n            .catch((err) => this.loadImageError(err));\n    }\n    loadBase64Image(imageBase64) {\n        this.loadImageService\n            .loadBase64Image(imageBase64, this.settings)\n            .then((res) => this.setLoadedImage(res))\n            .catch((err) => this.loadImageError(err));\n    }\n    loadImageFromURL(url) {\n        this.loadImageService\n            .loadImageFromURL(url, this.settings)\n            .then((res) => this.setLoadedImage(res))\n            .catch((err) => this.loadImageError(err));\n    }\n    setLoadedImage(loadedImage) {\n        this.loadedImage = loadedImage;\n        this.safeImgDataUrl = this.sanitizer.bypassSecurityTrustResourceUrl(loadedImage.transformed.objectUrl);\n        this.cd.markForCheck();\n    }\n    loadImageError(error) {\n        console.error(error);\n        this.loadImageFailed.emit();\n    }\n    imageLoadedInView() {\n        if (this.loadedImage != null) {\n            this.imageLoaded.emit(this.loadedImage);\n            this.setImageMaxSizeRetries = 0;\n            setTimeout(() => this.checkImageMaxSizeRecursively());\n        }\n    }\n    checkImageMaxSizeRecursively() {\n        if (this.setImageMaxSizeRetries > 40) {\n            this.loadImageFailed.emit();\n        }\n        else if (this.sourceImageLoaded()) {\n            this.setMaxSize();\n            this.setCropperScaledMinSize();\n            this.setCropperScaledMaxSize();\n            this.resetCropperPosition();\n            this.cropperReady.emit({ ...this.maxSize });\n            this.cd.markForCheck();\n        }\n        else {\n            this.setImageMaxSizeRetries++;\n            setTimeout(() => this.checkImageMaxSizeRecursively(), 50);\n        }\n    }\n    sourceImageLoaded() {\n        return this.sourceImage?.nativeElement?.offsetWidth > 0;\n    }\n    onResize() {\n        if (!this.loadedImage) {\n            return;\n        }\n        if (this.hidden) {\n            this.resizedWhileHidden = true;\n        }\n        else {\n            const oldMaxSize = { ...this.maxSize };\n            this.setMaxSize();\n            this.resizeCropperPosition(oldMaxSize);\n            this.setCropperScaledMinSize();\n            this.setCropperScaledMaxSize();\n        }\n    }\n    async activatePinchGesture() {\n        // Loads HammerJS via angular APIs if configured\n        await this.hammerLoader?.();\n        const Hammer = window?.['Hammer'] || null;\n        if (Hammer) {\n            const hammer = new Hammer(this.wrapper.nativeElement);\n            hammer.get('pinch').set({ enable: true });\n            hammer.on('pinchmove', this.onPinch.bind(this));\n            hammer.on('pinchend', this.pinchStop.bind(this));\n            hammer.on('pinchstart', this.startPinch.bind(this));\n        }\n        else if (isDevMode()) {\n            console.warn('[NgxImageCropper] Could not find HammerJS - Pinch Gesture won\\'t work');\n        }\n    }\n    resizeCropperPosition(oldMaxSize) {\n        if (oldMaxSize.width !== this.maxSize.width || oldMaxSize.height !== this.maxSize.height) {\n            this.cropper.x1 = this.cropper.x1 * this.maxSize.width / oldMaxSize.width;\n            this.cropper.x2 = this.cropper.x2 * this.maxSize.width / oldMaxSize.width;\n            this.cropper.y1 = this.cropper.y1 * this.maxSize.height / oldMaxSize.height;\n            this.cropper.y2 = this.cropper.y2 * this.maxSize.height / oldMaxSize.height;\n        }\n    }\n    resetCropperPosition() {\n        this.cropperPositionService.resetCropperPosition(this.sourceImage, this.cropper, this.settings, this.maxSize);\n        this.doAutoCrop();\n        this.imageVisible = true;\n    }\n    keyboardAccess(event) {\n        this.changeKeyboardStepSize(event);\n        this.keyboardMoveCropper(event);\n    }\n    changeKeyboardStepSize(event) {\n        const key = +event.key;\n        if (key >= 1 && key <= 9) {\n            this.settings.stepSize = key;\n        }\n    }\n    keyboardMoveCropper(event) {\n        const keyboardWhiteList = ['ArrowUp', 'ArrowDown', 'ArrowRight', 'ArrowLeft'];\n        if (!(keyboardWhiteList.includes(event.key))) {\n            return;\n        }\n        const moveType = event.shiftKey ? MoveTypes.Resize : MoveTypes.Move;\n        const position = event.altKey ? getInvertedPositionForKey(event.key) : getPositionForKey(event.key);\n        const moveEvent = getEventForKey(event.key, this.settings.stepSize);\n        event.preventDefault();\n        event.stopPropagation();\n        this.startMove({ clientX: 0, clientY: 0 }, moveType, position);\n        this.handleMouseMove(moveEvent);\n        this.handleMouseUp();\n    }\n    startMove(event, moveType, position = null) {\n        if (this.disabled\n            || this.moveStart?.active && this.moveStart?.type === MoveTypes.Pinch\n            || moveType === MoveTypes.Drag && !this.allowMoveImage) {\n            return;\n        }\n        if (event.preventDefault) {\n            event.preventDefault();\n        }\n        this.moveStart = {\n            active: true,\n            type: moveType,\n            position,\n            transform: { ...this.transform },\n            clientX: this.cropperPositionService.getClientX(event),\n            clientY: this.cropperPositionService.getClientY(event),\n            ...this.cropper\n        };\n        this.initMouseMove();\n    }\n    initMouseMove() {\n        merge(fromEvent(document, 'mousemove'), fromEvent(document, 'touchmove')).pipe(takeUntil(merge(fromEvent(document, 'mouseup'), fromEvent(document, 'touchend')).pipe(first())))\n            .subscribe({\n            next: (event) => this.zone.run(() => {\n                this.handleMouseMove(event);\n                this.cd.markForCheck();\n            }),\n            complete: () => this.zone.run(() => {\n                this.handleMouseUp();\n                this.cd.markForCheck();\n            })\n        });\n    }\n    startPinch(event) {\n        if (!this.safeImgDataUrl) {\n            return;\n        }\n        if (event.preventDefault) {\n            event.preventDefault();\n        }\n        this.moveStart = {\n            active: true,\n            type: MoveTypes.Pinch,\n            position: 'center',\n            clientX: this.cropper.x1 + (this.cropper.x2 - this.cropper.x1) / 2,\n            clientY: this.cropper.y1 + (this.cropper.y2 - this.cropper.y1) / 2,\n            ...this.cropper\n        };\n    }\n    handleMouseMove(event) {\n        if (this.moveStart.active) {\n            if (event.stopPropagation) {\n                event.stopPropagation();\n            }\n            if (event.preventDefault) {\n                event.preventDefault();\n            }\n            if (this.moveStart.type === MoveTypes.Move) {\n                this.cropperPositionService.move(event, this.moveStart, this.cropper);\n                this.checkCropperPosition(true);\n            }\n            else if (this.moveStart.type === MoveTypes.Resize) {\n                if (!this.cropperStaticWidth && !this.cropperStaticHeight) {\n                    this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n                }\n                this.checkCropperPosition(false);\n            }\n            else if (this.moveStart.type === MoveTypes.Drag) {\n                const diffX = this.cropperPositionService.getClientX(event) - this.moveStart.clientX;\n                const diffY = this.cropperPositionService.getClientY(event) - this.moveStart.clientY;\n                this.transform = {\n                    ...this.transform,\n                    translateH: (this.moveStart.transform?.translateH || 0) + diffX,\n                    translateV: (this.moveStart.transform?.translateV || 0) + diffY\n                };\n                this.setCssTransform();\n            }\n        }\n    }\n    onPinch(event) {\n        if (this.moveStart.active) {\n            if (event.stopPropagation) {\n                event.stopPropagation();\n            }\n            if (event.preventDefault) {\n                event.preventDefault();\n            }\n            if (this.moveStart.type === MoveTypes.Pinch) {\n                this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n                this.checkCropperPosition(false);\n            }\n            this.cd.markForCheck();\n        }\n    }\n    setMaxSize() {\n        if (this.sourceImage) {\n            const sourceImageStyle = getComputedStyle(this.sourceImage.nativeElement);\n            this.maxSize.width = parseFloat(sourceImageStyle.width);\n            this.maxSize.height = parseFloat(sourceImageStyle.height);\n            this.marginLeft = this.sanitizer.bypassSecurityTrustStyle('calc(50% - ' + this.maxSize.width / 2 + 'px)');\n        }\n    }\n    setCropperScaledMinSize() {\n        if (this.loadedImage?.transformed?.image) {\n            this.setCropperScaledMinWidth();\n            this.setCropperScaledMinHeight();\n        }\n        else {\n            this.settings.cropperScaledMinWidth = 20;\n            this.settings.cropperScaledMinHeight = 20;\n        }\n    }\n    setCropperScaledMinWidth() {\n        this.settings.cropperScaledMinWidth = this.cropperMinWidth > 0\n            ? Math.max(20, this.cropperMinWidth / this.loadedImage.transformed.image.width * this.maxSize.width)\n            : 20;\n    }\n    setCropperScaledMinHeight() {\n        if (this.maintainAspectRatio) {\n            this.settings.cropperScaledMinHeight = Math.max(20, this.settings.cropperScaledMinWidth / this.aspectRatio);\n        }\n        else if (this.cropperMinHeight > 0) {\n            this.settings.cropperScaledMinHeight = Math.max(20, this.cropperMinHeight / this.loadedImage.transformed.image.height * this.maxSize.height);\n        }\n        else {\n            this.settings.cropperScaledMinHeight = 20;\n        }\n    }\n    setCropperScaledMaxSize() {\n        if (this.loadedImage?.transformed?.image) {\n            const ratio = this.loadedImage.transformed.size.width / this.maxSize.width;\n            this.settings.cropperScaledMaxWidth = this.cropperMaxWidth > 20 ? this.cropperMaxWidth / ratio : this.maxSize.width;\n            this.settings.cropperScaledMaxHeight = this.cropperMaxHeight > 20 ? this.cropperMaxHeight / ratio : this.maxSize.height;\n            if (this.maintainAspectRatio) {\n                if (this.settings.cropperScaledMaxWidth > this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n                    this.settings.cropperScaledMaxWidth = this.settings.cropperScaledMaxHeight * this.aspectRatio;\n                }\n                else if (this.settings.cropperScaledMaxWidth < this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n                    this.settings.cropperScaledMaxHeight = this.settings.cropperScaledMaxWidth / this.aspectRatio;\n                }\n            }\n        }\n        else {\n            this.settings.cropperScaledMaxWidth = this.maxSize.width;\n            this.settings.cropperScaledMaxHeight = this.maxSize.height;\n        }\n    }\n    checkCropperPosition(maintainSize = false) {\n        if (this.cropper.x1 < 0) {\n            this.cropper.x2 -= maintainSize ? this.cropper.x1 : 0;\n            this.cropper.x1 = 0;\n        }\n        if (this.cropper.y1 < 0) {\n            this.cropper.y2 -= maintainSize ? this.cropper.y1 : 0;\n            this.cropper.y1 = 0;\n        }\n        if (this.cropper.x2 > this.maxSize.width) {\n            this.cropper.x1 -= maintainSize ? (this.cropper.x2 - this.maxSize.width) : 0;\n            this.cropper.x2 = this.maxSize.width;\n        }\n        if (this.cropper.y2 > this.maxSize.height) {\n            this.cropper.y1 -= maintainSize ? (this.cropper.y2 - this.maxSize.height) : 0;\n            this.cropper.y2 = this.maxSize.height;\n        }\n    }\n    handleMouseUp() {\n        if (this.moveStart.active) {\n            this.moveStart.active = false;\n            if (this.moveStart?.type === MoveTypes.Drag) {\n                this.transformChange.emit(this.transform);\n            }\n            else {\n                this.doAutoCrop();\n            }\n        }\n    }\n    pinchStop() {\n        if (this.moveStart.active) {\n            this.moveStart.active = false;\n            this.doAutoCrop();\n        }\n    }\n    doAutoCrop() {\n        if (this.autoCrop) {\n            void this.crop();\n        }\n    }\n    crop(output = this.settings.output) {\n        if (this.loadedImage?.transformed?.image != null) {\n            this.startCropImage.emit();\n            if (output === 'blob') {\n                return this.cropToBlob();\n            }\n            else if (output === 'base64') {\n                return this.cropToBase64();\n            }\n        }\n        return null;\n    }\n    cropToBlob() {\n        return new Promise((resolve, reject) => this.zone.run(async () => {\n            const result = await this.cropService.crop(this.loadedImage, this.cropper, this.settings, 'blob', this.maxSize);\n            if (result) {\n                this.imageCropped.emit(result);\n                resolve(result);\n            }\n            else {\n                reject('Crop image failed');\n            }\n        }));\n    }\n    cropToBase64() {\n        const result = this.cropService.crop(this.loadedImage, this.cropper, this.settings, 'base64', this.maxSize);\n        if (result) {\n            this.imageCropped.emit(result);\n            return result;\n        }\n        return null;\n    }\n    aspectRatioIsCorrect() {\n        const currentCropAspectRatio = (this.cropper.x2 - this.cropper.x1) / (this.cropper.y2 - this.cropper.y1);\n        return currentCropAspectRatio === this.aspectRatio;\n    }\n}\nImageCropperComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: ImageCropperComponent, deps: [{ token: CropService }, { token: CropperPositionService }, { token: LoadImageService }, { token: i4.DomSanitizer }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nImageCropperComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.11\", type: ImageCropperComponent, selector: \"image-cropper\", inputs: { imageChangedEvent: \"imageChangedEvent\", imageURL: \"imageURL\", imageBase64: \"imageBase64\", imageFile: \"imageFile\", imageAltText: \"imageAltText\", cropperFrameAriaLabel: \"cropperFrameAriaLabel\", output: \"output\", format: \"format\", transform: \"transform\", maintainAspectRatio: \"maintainAspectRatio\", aspectRatio: \"aspectRatio\", resetCropOnAspectRatioChange: \"resetCropOnAspectRatioChange\", resizeToWidth: \"resizeToWidth\", resizeToHeight: \"resizeToHeight\", cropperMinWidth: \"cropperMinWidth\", cropperMinHeight: \"cropperMinHeight\", cropperMaxHeight: \"cropperMaxHeight\", cropperMaxWidth: \"cropperMaxWidth\", cropperStaticWidth: \"cropperStaticWidth\", cropperStaticHeight: \"cropperStaticHeight\", canvasRotation: \"canvasRotation\", initialStepSize: \"initialStepSize\", roundCropper: \"roundCropper\", onlyScaleDown: \"onlyScaleDown\", imageQuality: \"imageQuality\", autoCrop: \"autoCrop\", backgroundColor: \"backgroundColor\", containWithinAspectRatio: \"containWithinAspectRatio\", hideResizeSquares: \"hideResizeSquares\", allowMoveImage: \"allowMoveImage\", cropper: \"cropper\", alignImage: \"alignImage\", disabled: \"disabled\", hidden: \"hidden\" }, outputs: { imageCropped: \"imageCropped\", startCropImage: \"startCropImage\", imageLoaded: \"imageLoaded\", cropperReady: \"cropperReady\", loadImageFailed: \"loadImageFailed\", transformChange: \"transformChange\" }, host: { listeners: { \"window:resize\": \"onResize()\" }, properties: { \"style.text-align\": \"this.alignImage\", \"class.disabled\": \"this.disabled\", \"class.ngx-ix-hidden\": \"this.hidden\" } }, viewQueries: [{ propertyName: \"wrapper\", first: true, predicate: [\"wrapper\"], descendants: true, static: true }, { propertyName: \"sourceImage\", first: true, predicate: [\"sourceImage\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<div\\n  [style.background]=\\\"imageVisible && backgroundColor\\\"\\n  #wrapper\\n>\\n  <img\\n    #sourceImage\\n    class=\\\"ngx-ic-source-image\\\"\\n    role=\\\"presentation\\\"\\n    *ngIf=\\\"safeImgDataUrl\\\"\\n    [src]=\\\"safeImgDataUrl\\\"\\n    [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n    [style.transform]=\\\"safeTransformStyle\\\"\\n    [class.ngx-ic-draggable]=\\\"!disabled && allowMoveImage\\\"\\n    [attr.alt]=\\\"imageAltText\\\"\\n    (load)=\\\"imageLoadedInView()\\\"\\n    (mousedown)=\\\"startMove($event, moveTypes.Drag)\\\"\\n    (touchstart)=\\\"startMove($event, moveTypes.Drag)\\\"\\n    (error)=\\\"loadImageError($event)\\\"\\n  >\\n  <div\\n    class=\\\"ngx-ic-overlay\\\"\\n    [style.width.px]=\\\"maxSize.width\\\"\\n    [style.height.px]=\\\"maxSize.height\\\"\\n    [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n  ></div>\\n  <div class=\\\"ngx-ic-cropper\\\"\\n       *ngIf=\\\"imageVisible\\\"\\n       [class.ngx-ic-round]=\\\"roundCropper\\\"\\n       [attr.aria-label]=\\\"cropperFrameAriaLabel\\\"\\n       [style.top.px]=\\\"cropper.y1\\\"\\n       [style.left.px]=\\\"cropper.x1\\\"\\n       [style.width.px]=\\\"cropper.x2 - cropper.x1\\\"\\n       [style.height.px]=\\\"cropper.y2 - cropper.y1\\\"\\n       [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n       [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n       (keydown)=\\\"keyboardAccess($event)\\\"\\n       tabindex=\\\"0\\\"\\n  >\\n    <div\\n      (mousedown)=\\\"startMove($event, moveTypes.Move)\\\"\\n      (touchstart)=\\\"startMove($event, moveTypes.Move)\\\"\\n      class=\\\"ngx-ic-move\\\"\\n      role=\\\"presentation\\\">\\n    </div>\\n    <ng-container *ngIf=\\\"!hideResizeSquares\\\">\\n            <span class=\\\"ngx-ic-resize ngx-ic-topleft\\\"\\n                  role=\\\"presentation\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-top\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-topright\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-right\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-bottomright\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-bottom\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-bottomleft\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-left\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-top\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'top')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'top')\\\">\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-right\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'right')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'right')\\\">\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-bottom\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\">\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-left\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'left')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'left')\\\">\\n            </span>\\n    </ng-container>\\n  </div>\\n</div>\\n\", styles: [\":host{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}:host>div{width:100%;position:relative}:host>div img.ngx-ic-source-image{max-width:100%;max-height:100%;transform-origin:center}:host>div img.ngx-ic-source-image.ngx-ic-draggable{user-drag:none;-webkit-user-drag:none;user-select:none;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;cursor:grab}:host .ngx-ic-overlay{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}:host .ngx-ic-cropper{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){:host .ngx-ic-cropper{outline-width:100vh}}:host .ngx-ic-cropper:after{position:absolute;content:\\\"\\\";inset:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}:host .ngx-ic-cropper .ngx-ic-move{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}:host .ngx-ic-cropper:focus .ngx-ic-move{border-color:#1e90ff;border-width:2px}:host .ngx-ic-cropper .ngx-ic-resize{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize .ngx-ic-square{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topleft{top:-12px;left:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-top{top:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topright{top:-12px;right:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-right{top:calc(50% - 12px);right:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomright{bottom:-12px;right:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottom{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomleft{bottom:-12px;left:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-left{top:calc(50% - 12px);left:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar{position:absolute;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-top{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-right{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-bottom{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-left{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper.ngx-ic-round{outline-color:transparent}:host .ngx-ic-cropper.ngx-ic-round:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){:host .ngx-ic-cropper.ngx-ic-round:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}:host .ngx-ic-cropper.ngx-ic-round .ngx-ic-move{border-radius:100%}:host.disabled .ngx-ic-cropper .ngx-ic-resize,:host.disabled .ngx-ic-cropper .ngx-ic-resize-bar,:host.disabled .ngx-ic-cropper .ngx-ic-move{display:none}:host.ngx-ix-hidden{display:none}\\n\"], directives: [{ type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: ImageCropperComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'image-cropper', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div\\n  [style.background]=\\\"imageVisible && backgroundColor\\\"\\n  #wrapper\\n>\\n  <img\\n    #sourceImage\\n    class=\\\"ngx-ic-source-image\\\"\\n    role=\\\"presentation\\\"\\n    *ngIf=\\\"safeImgDataUrl\\\"\\n    [src]=\\\"safeImgDataUrl\\\"\\n    [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n    [style.transform]=\\\"safeTransformStyle\\\"\\n    [class.ngx-ic-draggable]=\\\"!disabled && allowMoveImage\\\"\\n    [attr.alt]=\\\"imageAltText\\\"\\n    (load)=\\\"imageLoadedInView()\\\"\\n    (mousedown)=\\\"startMove($event, moveTypes.Drag)\\\"\\n    (touchstart)=\\\"startMove($event, moveTypes.Drag)\\\"\\n    (error)=\\\"loadImageError($event)\\\"\\n  >\\n  <div\\n    class=\\\"ngx-ic-overlay\\\"\\n    [style.width.px]=\\\"maxSize.width\\\"\\n    [style.height.px]=\\\"maxSize.height\\\"\\n    [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n  ></div>\\n  <div class=\\\"ngx-ic-cropper\\\"\\n       *ngIf=\\\"imageVisible\\\"\\n       [class.ngx-ic-round]=\\\"roundCropper\\\"\\n       [attr.aria-label]=\\\"cropperFrameAriaLabel\\\"\\n       [style.top.px]=\\\"cropper.y1\\\"\\n       [style.left.px]=\\\"cropper.x1\\\"\\n       [style.width.px]=\\\"cropper.x2 - cropper.x1\\\"\\n       [style.height.px]=\\\"cropper.y2 - cropper.y1\\\"\\n       [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n       [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n       (keydown)=\\\"keyboardAccess($event)\\\"\\n       tabindex=\\\"0\\\"\\n  >\\n    <div\\n      (mousedown)=\\\"startMove($event, moveTypes.Move)\\\"\\n      (touchstart)=\\\"startMove($event, moveTypes.Move)\\\"\\n      class=\\\"ngx-ic-move\\\"\\n      role=\\\"presentation\\\">\\n    </div>\\n    <ng-container *ngIf=\\\"!hideResizeSquares\\\">\\n            <span class=\\\"ngx-ic-resize ngx-ic-topleft\\\"\\n                  role=\\\"presentation\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-top\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-topright\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-right\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-bottomright\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-bottom\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-bottomleft\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize ngx-ic-left\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-top\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'top')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'top')\\\">\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-right\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'right')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'right')\\\">\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-bottom\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\">\\n            </span>\\n      <span class=\\\"ngx-ic-resize-bar ngx-ic-left\\\"\\n            role=\\\"presentation\\\"\\n            (mousedown)=\\\"startMove($event, moveTypes.Resize, 'left')\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Resize, 'left')\\\">\\n            </span>\\n    </ng-container>\\n  </div>\\n</div>\\n\", styles: [\":host{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}:host>div{width:100%;position:relative}:host>div img.ngx-ic-source-image{max-width:100%;max-height:100%;transform-origin:center}:host>div img.ngx-ic-source-image.ngx-ic-draggable{user-drag:none;-webkit-user-drag:none;user-select:none;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;cursor:grab}:host .ngx-ic-overlay{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}:host .ngx-ic-cropper{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){:host .ngx-ic-cropper{outline-width:100vh}}:host .ngx-ic-cropper:after{position:absolute;content:\\\"\\\";inset:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}:host .ngx-ic-cropper .ngx-ic-move{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}:host .ngx-ic-cropper:focus .ngx-ic-move{border-color:#1e90ff;border-width:2px}:host .ngx-ic-cropper .ngx-ic-resize{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize .ngx-ic-square{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topleft{top:-12px;left:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-top{top:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topright{top:-12px;right:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-right{top:calc(50% - 12px);right:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomright{bottom:-12px;right:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottom{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomleft{bottom:-12px;left:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-left{top:calc(50% - 12px);left:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar{position:absolute;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-top{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-right{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-bottom{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-left{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper.ngx-ic-round{outline-color:transparent}:host .ngx-ic-cropper.ngx-ic-round:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){:host .ngx-ic-cropper.ngx-ic-round:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}:host .ngx-ic-cropper.ngx-ic-round .ngx-ic-move{border-radius:100%}:host.disabled .ngx-ic-cropper .ngx-ic-resize,:host.disabled .ngx-ic-cropper .ngx-ic-resize-bar,:host.disabled .ngx-ic-cropper .ngx-ic-move{display:none}:host.ngx-ix-hidden{display:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: CropService }, { type: CropperPositionService }, { type: LoadImageService }, { type: i4.DomSanitizer }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }]; }, propDecorators: { wrapper: [{\n                type: ViewChild,\n                args: ['wrapper', { static: true }]\n            }], sourceImage: [{\n                type: ViewChild,\n                args: ['sourceImage', { static: false }]\n            }], imageChangedEvent: [{\n                type: Input\n            }], imageURL: [{\n                type: Input\n            }], imageBase64: [{\n                type: Input\n            }], imageFile: [{\n                type: Input\n            }], imageAltText: [{\n                type: Input\n            }], cropperFrameAriaLabel: [{\n                type: Input\n            }], output: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], transform: [{\n                type: Input\n            }], maintainAspectRatio: [{\n                type: Input\n            }], aspectRatio: [{\n                type: Input\n            }], resetCropOnAspectRatioChange: [{\n                type: Input\n            }], resizeToWidth: [{\n                type: Input\n            }], resizeToHeight: [{\n                type: Input\n            }], cropperMinWidth: [{\n                type: Input\n            }], cropperMinHeight: [{\n                type: Input\n            }], cropperMaxHeight: [{\n                type: Input\n            }], cropperMaxWidth: [{\n                type: Input\n            }], cropperStaticWidth: [{\n                type: Input\n            }], cropperStaticHeight: [{\n                type: Input\n            }], canvasRotation: [{\n                type: Input\n            }], initialStepSize: [{\n                type: Input\n            }], roundCropper: [{\n                type: Input\n            }], onlyScaleDown: [{\n                type: Input\n            }], imageQuality: [{\n                type: Input\n            }], autoCrop: [{\n                type: Input\n            }], backgroundColor: [{\n                type: Input\n            }], containWithinAspectRatio: [{\n                type: Input\n            }], hideResizeSquares: [{\n                type: Input\n            }], allowMoveImage: [{\n                type: Input\n            }], cropper: [{\n                type: Input\n            }], alignImage: [{\n                type: HostBinding,\n                args: ['style.text-align']\n            }, {\n                type: Input\n            }], disabled: [{\n                type: HostBinding,\n                args: ['class.disabled']\n            }, {\n                type: Input\n            }], hidden: [{\n                type: HostBinding,\n                args: ['class.ngx-ix-hidden']\n            }, {\n                type: Input\n            }], imageCropped: [{\n                type: Output\n            }], startCropImage: [{\n                type: Output\n            }], imageLoaded: [{\n                type: Output\n            }], cropperReady: [{\n                type: Output\n            }], loadImageFailed: [{\n                type: Output\n            }], transformChange: [{\n                type: Output\n            }], onResize: [{\n                type: HostListener,\n                args: ['window:resize']\n            }] } });\n\nclass ImageCropperModule {\n}\nImageCropperModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: ImageCropperModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nImageCropperModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: ImageCropperModule, declarations: [ImageCropperComponent], imports: [CommonModule], exports: [ImageCropperComponent] });\nImageCropperModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: ImageCropperModule, imports: [[\n            CommonModule\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: ImageCropperModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule\n                    ],\n                    declarations: [\n                        ImageCropperComponent,\n                    ],\n                    exports: [\n                        ImageCropperComponent\n                    ]\n                }]\n        }] });\n\nfunction base64ToFile(base64Image) {\n    const split = base64Image.split(',');\n    const type = split[0].replace('data:', '').replace(';base64', '');\n    const byteString = atob(split[1]);\n    const ab = new ArrayBuffer(byteString.length);\n    const ia = new Uint8Array(ab);\n    for (let i = 0; i < byteString.length; i += 1) {\n        ia[i] = byteString.charCodeAt(i);\n    }\n    return new Blob([ab], { type });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CropService, CropperSettings, ImageCropperComponent, ImageCropperModule, LoadImageService, base64ToFile, resizeCanvas };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACxL,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,EAAEC,KAAK,QAAQ,gBAAgB;AACjD,SAASC,KAAK,EAAEC,SAAS,QAAQ,MAAM;AACvC,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA+TgD3B,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,eAm+Bq5E,CAAC;IAn+Bx5E7B,EAAE,CAAA8B,UAAA,kBAAAC,yDAAA;MAAF/B,EAAE,CAAAgC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+BquEF,MAAA,CAAAG,iBAAA,CAAkB,CAAC;IAAA,CAAC,CAAC,uBAAAC,8DAAAC,MAAA;MAn+B5vEtC,EAAE,CAAAgC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B8wEF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAC,IAAgC,CAAC;IAAA,CAAC,CAAC,wBAAAC,+DAAAJ,MAAA;MAn+BnzEtC,EAAE,CAAAgC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+Bs0EF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAC,IAAgC,CAAC;IAAA,CAAC,CAAC,mBAAAE,0DAAAL,MAAA;MAn+B32EtC,EAAE,CAAAgC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+By3EF,MAAA,CAAAW,cAAA,CAAAN,MAAqB,CAAC;IAAA,CAAC,CAAC;IAn+Bn5EtC,EAAE,CAAA6C,YAAA,CAm+Bq5E,CAAC;EAAA;EAAA,IAAApB,EAAA;IAAA,MAAAQ,MAAA,GAn+Bx5EjC,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAA8C,WAAA,eAAAb,MAAA,CAAAc,YAAA,uBAm+BwkE,CAAC,cAAAd,MAAA,CAAAe,kBAA6C,CAAC;IAn+BznEhD,EAAE,CAAAiD,WAAA,sBAAAhB,MAAA,CAAAiB,QAAA,IAAAjB,MAAA,CAAAkB,cAm+BorE,CAAC;IAn+BvrEnD,EAAE,CAAAoD,UAAA,QAAAnB,MAAA,CAAAoB,cAAA,EAAFrD,EAAE,CAAAsD,aAm+BwgE,CAAC;IAn+B3gEtD,EAAE,CAAAuD,WAAA,QAAAtB,MAAA,CAAAuB,YAAA;EAAA;AAAA;AAAA,SAAAC,oDAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiC,GAAA,GAAF1D,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA2D,uBAAA,EAm+Bw4G,CAAC;IAn+B34G3D,EAAE,CAAA6B,cAAA,aAm+BipH,CAAC;IAn+BppH7B,EAAE,CAAA8B,UAAA,uBAAA8B,8EAAAtB,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B8gHF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,SAAS,CAAC;IAAA,CAAC,CAAC,wBAAAC,+EAAAxB,MAAA;MAn+BhkHtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+BimHF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,SAAS,CAAC;IAAA,CAAC,CAAC;IAn+BnpH7D,EAAE,CAAA+D,SAAA,cAm+BwsH,CAAC;IAn+B3sH/D,EAAE,CAAA6C,YAAA,CAm+B6tH,CAAC;IAn+BhuH7C,EAAE,CAAA6B,cAAA,cAm+B8wH,CAAC;IAn+BjxH7B,EAAE,CAAA+D,SAAA,cAm+Bq0H,CAAC;IAn+Bx0H/D,EAAE,CAAA6C,YAAA,CAm+B01H,CAAC;IAn+B71H7C,EAAE,CAAA6B,cAAA,cAm+B8kI,CAAC;IAn+BjlI7B,EAAE,CAAA8B,UAAA,uBAAAkC,8EAAA1B,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B+8HF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,UAAU,CAAC;IAAA,CAAC,CAAC,wBAAAI,+EAAA3B,MAAA;MAn+BlgItC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B6hIF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,UAAU,CAAC;IAAA,CAAC,CAAC;IAn+BhlI7D,EAAE,CAAA+D,SAAA,cAm+BqoI,CAAC;IAn+BxoI/D,EAAE,CAAA6C,YAAA,CAm+B0pI,CAAC;IAn+B7pI7C,EAAE,CAAA6B,cAAA,cAm+B6sI,CAAC;IAn+BhtI7B,EAAE,CAAA+D,SAAA,cAm+BowI,CAAC;IAn+BvwI/D,EAAE,CAAA6C,YAAA,CAm+ByxI,CAAC;IAn+B5xI7C,EAAE,CAAA6B,cAAA,cAm+BshJ,CAAC;IAn+BzhJ7B,EAAE,CAAA8B,UAAA,uBAAAoC,8EAAA5B,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+Bi5IF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,aAAa,CAAC;IAAA,CAAC,CAAC,wBAAAM,+EAAA7B,MAAA;MAn+Bv8ItC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+Bk+IF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,aAAa,CAAC;IAAA,CAAC,CAAC;IAn+BxhJ7D,EAAE,CAAA+D,SAAA,eAm+B6kJ,CAAC;IAn+BhlJ/D,EAAE,CAAA6C,YAAA,CAm+BkmJ,CAAC;IAn+BrmJ7C,EAAE,CAAA6B,cAAA,eAm+BspJ,CAAC;IAn+BzpJ7B,EAAE,CAAA+D,SAAA,eAm+B6sJ,CAAC;IAn+BhtJ/D,EAAE,CAAA6C,YAAA,CAm+BkuJ,CAAC;IAn+BruJ7C,EAAE,CAAA6B,cAAA,eAm+B49J,CAAC;IAn+B/9J7B,EAAE,CAAA8B,UAAA,uBAAAsC,+EAAA9B,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+By1JF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,YAAY,CAAC;IAAA,CAAC,CAAC,wBAAAQ,gFAAA/B,MAAA;MAn+B94JtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+By6JF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,YAAY,CAAC;IAAA,CAAC,CAAC;IAn+B99J7D,EAAE,CAAA+D,SAAA,eAm+BmhK,CAAC;IAn+BthK/D,EAAE,CAAA6C,YAAA,CAm+BwiK,CAAC;IAn+B3iK7C,EAAE,CAAA6B,cAAA,eAm+B0lK,CAAC;IAn+B7lK7B,EAAE,CAAA+D,SAAA,eAm+BipK,CAAC;IAn+BppK/D,EAAE,CAAA6C,YAAA,CAm+BsqK,CAAC;IAn+BzqK7C,EAAE,CAAA6B,cAAA,eAm+B+4K,CAAC;IAn+Bl5K7B,EAAE,CAAA8B,UAAA,uBAAAwC,+EAAAhC,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B0xKF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,KAAK,CAAC;IAAA,CAAC,CAAC,wBAAAU,gFAAAjC,MAAA;MAn+Bx0KtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+Bm2KF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,KAAK,CAAC;IAAA,CAAC,CAAC;IAn+Bj5K7D,EAAE,CAAA6C,YAAA,CAm+Bo6K,CAAC;IAn+Bv6K7C,EAAE,CAAA6B,cAAA,eAm+BmpL,CAAC;IAn+BtpL7B,EAAE,CAAA8B,UAAA,uBAAA0C,+EAAAlC,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B0hLF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,OAAO,CAAC;IAAA,CAAC,CAAC,wBAAAY,gFAAAnC,MAAA;MAn+B1kLtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+BqmLF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,OAAO,CAAC;IAAA,CAAC,CAAC;IAn+BrpL7D,EAAE,CAAA6C,YAAA,CAm+BwqL,CAAC;IAn+B3qL7C,EAAE,CAAA6B,cAAA,eAm+B05L,CAAC;IAn+B75L7B,EAAE,CAAA8B,UAAA,uBAAA4C,+EAAApC,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B+xLF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,QAAQ,CAAC;IAAA,CAAC,CAAC,wBAAAc,gFAAArC,MAAA;MAn+Bh1LtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B22LF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,QAAQ,CAAC;IAAA,CAAC,CAAC;IAn+B55L7D,EAAE,CAAA6C,YAAA,CAm+B+6L,CAAC;IAn+Bl7L7C,EAAE,CAAA6B,cAAA,eAm+B2pM,CAAC;IAn+B9pM7B,EAAE,CAAA8B,UAAA,uBAAA8C,+EAAAtC,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+BoiMF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,MAAM,CAAC;IAAA,CAAC,CAAC,wBAAAgB,gFAAAvC,MAAA;MAn+BnlMtC,EAAE,CAAAgC,aAAA,CAAA0B,GAAA;MAAA,MAAAzB,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B8mMF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAAqB,MAAA,EAAoC,MAAM,CAAC;IAAA,CAAC,CAAC;IAn+B7pM7D,EAAE,CAAA6C,YAAA,CAm+BgrM,CAAC;IAn+BnrM7C,EAAE,CAAA8E,qBAAA;EAAA;AAAA;AAAA,SAAAC,qCAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuD,GAAA,GAAFhF,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,YAm+BmpG,CAAC;IAn+BtpG7B,EAAE,CAAA8B,UAAA,qBAAAmD,4DAAA3C,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAAgD,GAAA;MAAA,MAAA/C,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+BgmGF,MAAA,CAAAiD,cAAA,CAAA5C,MAAqB,CAAC;IAAA,CAAC,CAAC;IAn+B1nGtC,EAAE,CAAA6B,cAAA,YAm+B20G,CAAC;IAn+B90G7B,EAAE,CAAA8B,UAAA,uBAAAqD,8DAAA7C,MAAA;MAAFtC,EAAE,CAAAgC,aAAA,CAAAgD,GAAA;MAAA,MAAA/C,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+BorGF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAA4C,IAAgC,CAAC;IAAA,CAAC,CAAC,wBAAAC,+DAAA/C,MAAA;MAn+BztGtC,EAAE,CAAAgC,aAAA,CAAAgD,GAAA;MAAA,MAAA/C,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAm+B8uGF,MAAA,CAAAM,SAAA,CAAAD,MAAA,EAAAL,MAAA,CAAAO,SAAA,CAAA4C,IAAgC,CAAC;IAAA,CAAC,CAAC;IAn+BnxGpF,EAAE,CAAA6C,YAAA,CAm+Bu1G,CAAC;IAn+B11G7C,EAAE,CAAAsF,UAAA,IAAA7B,mDAAA,0BAm+Bw4G,CAAC;IAn+B34GzD,EAAE,CAAA6C,YAAA,CAm+B+sM,CAAC;EAAA;EAAA,IAAApB,EAAA;IAAA,MAAAQ,MAAA,GAn+BltMjC,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAA8C,WAAA,QAAAb,MAAA,CAAAsD,OAAA,CAAAC,EAAA,MAm+ByyF,CAAC,SAAAvD,MAAA,CAAAsD,OAAA,CAAAE,EAAA,MAAsC,CAAC,UAAAxD,MAAA,CAAAsD,OAAA,CAAAG,EAAA,GAAAzD,MAAA,CAAAsD,OAAA,CAAAE,EAAA,MAAoD,CAAC,WAAAxD,MAAA,CAAAsD,OAAA,CAAAI,EAAA,GAAA1D,MAAA,CAAAsD,OAAA,CAAAC,EAAA,MAAqD,CAAC,gBAAAvD,MAAA,CAAA2D,UAAA,gBAAA3D,MAAA,CAAA4D,UAAA,OAA2E,CAAC,eAAA5D,MAAA,CAAAc,YAAA,uBAAkE,CAAC;IAn+B7kG/C,EAAE,CAAAiD,WAAA,iBAAAhB,MAAA,CAAA6D,YAm+B+sF,CAAC;IAn+BltF9F,EAAE,CAAAuD,WAAA,eAAAtB,MAAA,CAAA8D,qBAAA;IAAF/F,EAAE,CAAAgG,SAAA,EAm+Bq4G,CAAC;IAn+Bx4GhG,EAAE,CAAAoD,UAAA,UAAAnB,MAAA,CAAAgE,iBAm+Bq4G,CAAC;EAAA;AAAA;AAhyCv+G,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAG,MAAM;IACpB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACrB,YAAY,GAAG,KAAK;IACzB,IAAI,CAACsB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACvB,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACL,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACG,qBAAqB,GAAG,YAAY;IACzC;IACA,IAAI,CAAC0B,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACV,eAAe;EACxC;EACAW,UAAUA,CAACC,OAAO,EAAE;IAChBC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CACfG,MAAM,CAAEC,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBC,OAAO,CAAED,CAAC,IAAK,IAAI,CAACA,CAAC,CAAC,GAAGJ,OAAO,CAACI,CAAC,CAAC,CAAC;IACzC,IAAI,CAACE,eAAe,CAAC,CAAC;EAC1B;EACAC,qBAAqBA,CAACC,OAAO,EAAE;IAC3BP,MAAM,CAACC,IAAI,CAACM,OAAO,CAAC,CACfL,MAAM,CAAEC,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBC,OAAO,CAAED,CAAC,IAAK,IAAI,CAACA,CAAC,CAAC,GAAGI,OAAO,CAACJ,CAAC,CAAC,CAACK,YAAY,CAAC;IACtD,IAAI,CAACH,eAAe,CAAC,CAAC;EAC1B;EACAA,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC/B,mBAAmB,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE;MAC/C,MAAM,IAAIiC,KAAK,CAAC,gEAAgE,CAAC;IACrF;EACJ;AACJ;AAEA,IAAIC,SAAS,gBACZ,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;EAC1BA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;EAC1BA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC9BA,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO;EAAC,OAJtBA,SAAS;AAKpB,CAAC,CAAEA,SAAS,IAAiB,CAAC,CAAE,CANnB;AAQb,SAASC,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,QAAQA,GAAG;IACP,KAAK,SAAS;MACV,OAAO,KAAK;IAChB,KAAK,YAAY;MACb,OAAO,OAAO;IAClB,KAAK,WAAW;MACZ,OAAO,QAAQ;IACnB,KAAK,WAAW;IAChB;MACI,OAAO,MAAM;EACrB;AACJ;AACA,SAASC,yBAAyBA,CAACD,GAAG,EAAE;EACpC,QAAQA,GAAG;IACP,KAAK,SAAS;MACV,OAAO,QAAQ;IACnB,KAAK,YAAY;MACb,OAAO,MAAM;IACjB,KAAK,WAAW;MACZ,OAAO,KAAK;IAChB,KAAK,WAAW;IAChB;MACI,OAAO,OAAO;EACtB;AACJ;AACA,SAASE,cAAcA,CAACF,GAAG,EAAEf,QAAQ,EAAE;EACnC,QAAQe,GAAG;IACP,KAAK,SAAS;MACV,OAAO;QAAEG,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAEnB,QAAQ,GAAG,CAAC;MAAE,CAAC;IACjD,KAAK,YAAY;MACb,OAAO;QAAEkB,OAAO,EAAElB,QAAQ;QAAEmB,OAAO,EAAE;MAAE,CAAC;IAC5C,KAAK,WAAW;MACZ,OAAO;QAAED,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAEnB;MAAS,CAAC;IAC5C,KAAK,WAAW;IAChB;MACI,OAAO;QAAEkB,OAAO,EAAElB,QAAQ,GAAG,CAAC,CAAC;QAAEmB,OAAO,EAAE;MAAE,CAAC;EACrD;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;EACzC,MAAMC,YAAY,GAAGH,MAAM,CAACC,KAAK;EACjC,MAAMG,aAAa,GAAGJ,MAAM,CAACE,MAAM;EACnCD,KAAK,GAAGI,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC;EACzBC,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;EAC3B,MAAMK,OAAO,GAAGJ,YAAY,GAAGF,KAAK;EACpC,MAAMO,OAAO,GAAGJ,aAAa,GAAGF,MAAM;EACtC,MAAMO,YAAY,GAAGJ,IAAI,CAACK,IAAI,CAACH,OAAO,GAAG,CAAC,CAAC;EAC3C,MAAMI,YAAY,GAAGN,IAAI,CAACK,IAAI,CAACF,OAAO,GAAG,CAAC,CAAC;EAC3C,MAAMhI,GAAG,GAAGwH,MAAM,CAACY,UAAU,CAAC,IAAI,CAAC;EACnC,IAAIpI,GAAG,EAAE;IACL,MAAMqI,GAAG,GAAGrI,GAAG,CAACsI,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEX,YAAY,EAAEC,aAAa,CAAC;IAC/D,MAAMW,IAAI,GAAGvI,GAAG,CAACwI,eAAe,CAACf,KAAK,EAAEC,MAAM,CAAC;IAC/C,MAAMe,IAAI,GAAGJ,GAAG,CAACI,IAAI;IACrB,MAAMC,KAAK,GAAGH,IAAI,CAACE,IAAI;IACvB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,MAAM,EAAEiB,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,KAAK,EAAEmB,CAAC,EAAE,EAAE;QAC5B,MAAM5E,EAAE,GAAG,CAAC4E,CAAC,GAAGD,CAAC,GAAGlB,KAAK,IAAI,CAAC;QAC9B,MAAMoB,QAAQ,GAAGF,CAAC,GAAGX,OAAO;QAC5B,IAAIc,MAAM,GAAG,CAAC;QACd,IAAIC,OAAO,GAAG,CAAC;QACf,IAAIC,aAAa,GAAG,CAAC;QACrB,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,IAAI,GAAG,CAAC;QACZ,MAAMC,QAAQ,GAAGxB,IAAI,CAACyB,KAAK,CAACV,CAAC,GAAGb,OAAO,CAAC;QACxC,MAAMwB,QAAQ,GAAG1B,IAAI,CAACyB,KAAK,CAACX,CAAC,GAAGX,OAAO,CAAC;QACxC,IAAIwB,OAAO,GAAG3B,IAAI,CAACK,IAAI,CAAC,CAACU,CAAC,GAAG,CAAC,IAAIb,OAAO,CAAC;QAC1C,IAAI0B,OAAO,GAAG5B,IAAI,CAACK,IAAI,CAAC,CAACS,CAAC,GAAG,CAAC,IAAIX,OAAO,CAAC;QAC1CwB,OAAO,GAAG3B,IAAI,CAAC6B,GAAG,CAACF,OAAO,EAAE7B,YAAY,CAAC;QACzC8B,OAAO,GAAG5B,IAAI,CAAC6B,GAAG,CAACD,OAAO,EAAE7B,aAAa,CAAC;QAC1C,KAAK,IAAI+B,EAAE,GAAGJ,QAAQ,EAAEI,EAAE,GAAGF,OAAO,EAAEE,EAAE,EAAE,EAAE;UACxC,MAAMC,EAAE,GAAG/B,IAAI,CAACgC,GAAG,CAAChB,QAAQ,GAAGc,EAAE,CAAC,GAAGxB,YAAY;UACjD,MAAM2B,QAAQ,GAAGlB,CAAC,GAAGb,OAAO;UAC5B,MAAMgC,EAAE,GAAGH,EAAE,GAAGA,EAAE,CAAC,CAAC;UACpB,KAAK,IAAII,EAAE,GAAGX,QAAQ,EAAEW,EAAE,GAAGR,OAAO,EAAEQ,EAAE,EAAE,EAAE;YACxC,MAAMC,EAAE,GAAGpC,IAAI,CAACgC,GAAG,CAACC,QAAQ,GAAGE,EAAE,CAAC,GAAG/B,YAAY;YACjD,MAAMiC,CAAC,GAAGrC,IAAI,CAACsC,IAAI,CAACJ,EAAE,GAAGE,EAAE,GAAGA,EAAE,CAAC;YACjC,IAAIC,CAAC,IAAI,CAAC,EAAE;cACR;cACA;YACJ;YACA;YACApB,MAAM,GAAG,CAAC,GAAGoB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;YACtC,MAAME,KAAK,GAAG,CAAC,IAAIJ,EAAE,GAAGL,EAAE,GAAGhC,YAAY,CAAC;YAC1C;YACAyB,IAAI,IAAIN,MAAM,GAAGL,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC;YAChCpB,aAAa,IAAIF,MAAM;YACvB;YACA,IAAIL,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,EACrBtB,MAAM,GAAGA,MAAM,GAAGL,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG;YAC3CnB,IAAI,IAAIH,MAAM,GAAGL,IAAI,CAAC2B,KAAK,CAAC;YAC5BlB,IAAI,IAAIJ,MAAM,GAAGL,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC;YAChCjB,IAAI,IAAIL,MAAM,GAAGL,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC;YAChCrB,OAAO,IAAID,MAAM;UACrB;QACJ;QACAJ,KAAK,CAAC1E,EAAE,CAAC,GAAGiF,IAAI,GAAGF,OAAO;QAC1BL,KAAK,CAAC1E,EAAE,GAAG,CAAC,CAAC,GAAGkF,IAAI,GAAGH,OAAO;QAC9BL,KAAK,CAAC1E,EAAE,GAAG,CAAC,CAAC,GAAGmF,IAAI,GAAGJ,OAAO;QAC9BL,KAAK,CAAC1E,EAAE,GAAG,CAAC,CAAC,GAAGoF,IAAI,GAAGJ,aAAa;MACxC;IACJ;IACAxB,MAAM,CAACC,KAAK,GAAGA,KAAK;IACpBD,MAAM,CAACE,MAAM,GAAGA,MAAM;IACtB;IACA1H,GAAG,CAACqK,YAAY,CAAC9B,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EAChC;AACJ;AAEA,SAAS+B,UAAUA,CAACC,OAAO,EAAEC,UAAU,EAAE;EACrC,OAAQD,OAAO,GAAG,GAAG,GAAIC,UAAU;AACvC;AAAC,IAEKC,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACdC,IAAIA,CAACC,WAAW,EAAE9G,OAAO,EAAE+G,QAAQ,EAAEjG,MAAM,EAAEkG,OAAO,EAAE;MAClD,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACJ,WAAW,EAAE9G,OAAO,EAAE+G,QAAQ,EAAEC,OAAO,CAAC;MACpF,MAAMpD,KAAK,GAAGqD,aAAa,CAAC9G,EAAE,GAAG8G,aAAa,CAAC/G,EAAE;MACjD,MAAM2D,MAAM,GAAGoD,aAAa,CAAC7G,EAAE,GAAG6G,aAAa,CAAChH,EAAE;MAClD,MAAMkH,UAAU,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACnDF,UAAU,CAACvD,KAAK,GAAGA,KAAK;MACxBuD,UAAU,CAACtD,MAAM,GAAGA,MAAM;MAC1B,MAAM1H,GAAG,GAAGgL,UAAU,CAAC5C,UAAU,CAAC,IAAI,CAAC;MACvC,IAAI,CAACpI,GAAG,EAAE;QACN,OAAO,IAAI;MACf;MACA,IAAI4K,QAAQ,CAAC/E,eAAe,IAAI,IAAI,EAAE;QAClC7F,GAAG,CAACmL,SAAS,GAAGP,QAAQ,CAAC/E,eAAe;QACxC7F,GAAG,CAACoL,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE3D,KAAK,EAAEC,MAAM,CAAC;MACrC;MACA,MAAM2D,MAAM,GAAG,CAACT,QAAQ,CAAC/F,SAAS,CAACyG,KAAK,IAAI,CAAC,KAAKV,QAAQ,CAAC/F,SAAS,CAAC0G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,MAAMC,MAAM,GAAG,CAACZ,QAAQ,CAAC/F,SAAS,CAACyG,KAAK,IAAI,CAAC,KAAKV,QAAQ,CAAC/F,SAAS,CAAC4G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,MAAM;QAAEC,UAAU;QAAEC;MAAW,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAACjB,WAAW,EAAEC,QAAQ,EAAEC,OAAO,CAAC;MAC1F,MAAMgB,gBAAgB,GAAGlB,WAAW,CAACmB,WAAW;MAChD9L,GAAG,CAAC+L,YAAY,CAACV,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEG,MAAM,EAAEK,gBAAgB,CAACG,IAAI,CAACvE,KAAK,GAAG,CAAC,GAAGiE,UAAU,EAAEG,gBAAgB,CAACG,IAAI,CAACtE,MAAM,GAAG,CAAC,GAAGiE,UAAU,CAAC;MACnI3L,GAAG,CAACiM,SAAS,CAAC,CAACnB,aAAa,CAAC/G,EAAE,GAAGsH,MAAM,EAAE,CAACP,aAAa,CAAChH,EAAE,GAAG0H,MAAM,CAAC;MACrExL,GAAG,CAACkM,MAAM,CAAC,CAACtB,QAAQ,CAAC/F,SAAS,CAACqH,MAAM,IAAI,CAAC,IAAIrE,IAAI,CAACsE,EAAE,GAAG,GAAG,CAAC;MAC5DnM,GAAG,CAACoM,SAAS,CAACP,gBAAgB,CAACQ,KAAK,EAAE,CAACR,gBAAgB,CAACG,IAAI,CAACvE,KAAK,GAAG,CAAC,EAAE,CAACoE,gBAAgB,CAACG,IAAI,CAACtE,MAAM,GAAG,CAAC,CAAC;MAC1G,MAAM4E,MAAM,GAAG;QACX7E,KAAK;QAAEC,MAAM;QACboD,aAAa;QACbyB,eAAe,EAAE;UAAE,GAAG1I;QAAQ;MAClC,CAAC;MACD,IAAI+G,QAAQ,CAAC9E,wBAAwB,EAAE;QACnCwG,MAAM,CAACE,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAAC9B,WAAW,EAAE9G,OAAO,EAAE+G,QAAQ,EAAEC,OAAO,CAAC;MACrG;MACA,MAAM6B,WAAW,GAAG,IAAI,CAACC,cAAc,CAAClF,KAAK,EAAEC,MAAM,EAAEkD,QAAQ,CAAC;MAChE,IAAI8B,WAAW,KAAK,CAAC,EAAE;QACnBJ,MAAM,CAAC7E,KAAK,GAAGI,IAAI,CAACC,KAAK,CAACL,KAAK,GAAGiF,WAAW,CAAC;QAC9CJ,MAAM,CAAC5E,MAAM,GAAGkD,QAAQ,CAAChG,mBAAmB,GACtCiD,IAAI,CAACC,KAAK,CAACwE,MAAM,CAAC7E,KAAK,GAAGmD,QAAQ,CAAC9F,WAAW,CAAC,GAC/C+C,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAGgF,WAAW,CAAC;QACtCnF,YAAY,CAACyD,UAAU,EAAEsB,MAAM,CAAC7E,KAAK,EAAE6E,MAAM,CAAC5E,MAAM,CAAC;MACzD;MACA,IAAI/C,MAAM,KAAK,MAAM,EAAE;QACnB,OAAO,IAAI,CAACiI,UAAU,CAACN,MAAM,EAAEtB,UAAU,EAAEJ,QAAQ,CAAC;MACxD,CAAC,MACI;QACD0B,MAAM,CAACO,MAAM,GAAG7B,UAAU,CAAC8B,SAAS,CAAC,QAAQ,GAAGlC,QAAQ,CAAClG,MAAM,EAAE,IAAI,CAACqI,UAAU,CAACnC,QAAQ,CAAC,CAAC;QAC3F,OAAO0B,MAAM;MACjB;IACJ;IACMM,UAAUA,CAACjI,MAAM,EAAEqG,UAAU,EAAEJ,QAAQ,EAAE;MAAA,IAAAoC,KAAA;MAAA,OAAAC,iBAAA;QAC3CtI,MAAM,CAACuI,IAAI,SAAS,IAAIC,OAAO,CAACC,OAAO,IAAIpC,UAAU,CAACqC,MAAM,CAACD,OAAO,EAAE,QAAQ,GAAGxC,QAAQ,CAAClG,MAAM,EAAEsI,KAAI,CAACD,UAAU,CAACnC,QAAQ,CAAC,CAAC,CAAC;QAC7H,IAAIjG,MAAM,CAACuI,IAAI,EAAE;UACbvI,MAAM,CAAC2I,SAAS,GAAGC,GAAG,CAACC,eAAe,CAAC7I,MAAM,CAACuI,IAAI,CAAC;QACvD;QACA,OAAOvI,MAAM;MAAC;IAClB;IACAiH,kBAAkBA,CAACjB,WAAW,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MAC/C,IAAID,QAAQ,CAAC/F,SAAS,CAAC4I,aAAa,KAAK,IAAI,EAAE;QAC3C,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAChD,WAAW,EAAEE,OAAO,CAAC;QACjD,OAAO;UACHa,UAAU,EAAE,CAACd,QAAQ,CAAC/F,SAAS,CAAC6G,UAAU,IAAI,CAAC,IAAIgC,KAAK;UACxD/B,UAAU,EAAE,CAACf,QAAQ,CAAC/F,SAAS,CAAC8G,UAAU,IAAI,CAAC,IAAI+B;QACvD,CAAC;MACL,CAAC,MACI;QACD,OAAO;UACHhC,UAAU,EAAEd,QAAQ,CAAC/F,SAAS,CAAC6G,UAAU,GAAGpB,UAAU,CAACM,QAAQ,CAAC/F,SAAS,CAAC6G,UAAU,EAAEf,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACvE,KAAK,CAAC,GAAG,CAAC;UAC7HkE,UAAU,EAAEf,QAAQ,CAAC/F,SAAS,CAAC8G,UAAU,GAAGrB,UAAU,CAACM,QAAQ,CAAC/F,SAAS,CAAC8G,UAAU,EAAEhB,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACtE,MAAM,CAAC,GAAG;QACjI,CAAC;MACL;IACJ;IACAiG,QAAQA,CAAChD,WAAW,EAAEE,OAAO,EAAE;MAC3B,OAAOF,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACvE,KAAK,GAAGoD,OAAO,CAACpD,KAAK;IAC7D;IACAsD,gBAAgBA,CAACJ,WAAW,EAAE9G,OAAO,EAAE+G,QAAQ,EAAEC,OAAO,EAAE;MACtD,MAAM6C,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAChD,WAAW,EAAEE,OAAO,CAAC;MACjD,MAAM+C,GAAG,GAAG;QACR7J,EAAE,EAAE8D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACE,EAAE,GAAG2J,KAAK,CAAC;QAClC5J,EAAE,EAAE+D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACC,EAAE,GAAG4J,KAAK,CAAC;QAClC1J,EAAE,EAAE6D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACG,EAAE,GAAG0J,KAAK,CAAC;QAClCzJ,EAAE,EAAE4D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACI,EAAE,GAAGyJ,KAAK;MACrC,CAAC;MACD,IAAI,CAAC9C,QAAQ,CAAC9E,wBAAwB,EAAE;QACpC8H,GAAG,CAAC7J,EAAE,GAAG8D,IAAI,CAACgG,GAAG,CAACD,GAAG,CAAC7J,EAAE,EAAE,CAAC,CAAC;QAC5B6J,GAAG,CAAC9J,EAAE,GAAG+D,IAAI,CAACgG,GAAG,CAACD,GAAG,CAAC9J,EAAE,EAAE,CAAC,CAAC;QAC5B8J,GAAG,CAAC5J,EAAE,GAAG6D,IAAI,CAAC6B,GAAG,CAACkE,GAAG,CAAC5J,EAAE,EAAE2G,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACvE,KAAK,CAAC;QAC7DmG,GAAG,CAAC3J,EAAE,GAAG4D,IAAI,CAAC6B,GAAG,CAACkE,GAAG,CAAC3J,EAAE,EAAE0G,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACtE,MAAM,CAAC;MAClE;MACA,OAAOkG,GAAG;IACd;IACAnB,sBAAsBA,CAAC9B,WAAW,EAAE9G,OAAO,EAAE+G,QAAQ,EAAEC,OAAO,EAAE;MAC5D,MAAMrF,cAAc,GAAGoF,QAAQ,CAACpF,cAAc,GAAGmF,WAAW,CAACmD,aAAa,CAAC5B,MAAM;MACjF,MAAMwB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAChD,WAAW,EAAEE,OAAO,CAAC;MACjD,IAAIkD,OAAO;MACX,IAAIC,OAAO;MACX,IAAIxI,cAAc,GAAG,CAAC,EAAE;QACpBuI,OAAO,GAAG,CAACpD,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACvE,KAAK,GAAGkD,WAAW,CAACsD,QAAQ,CAACjC,IAAI,CAACtE,MAAM,IAAI,CAAC;QACrFsG,OAAO,GAAG,CAACrD,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACtE,MAAM,GAAGiD,WAAW,CAACsD,QAAQ,CAACjC,IAAI,CAACvE,KAAK,IAAI,CAAC;MACzF,CAAC,MACI;QACDsG,OAAO,GAAG,CAACpD,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACvE,KAAK,GAAGkD,WAAW,CAACsD,QAAQ,CAACjC,IAAI,CAACvE,KAAK,IAAI,CAAC;QACpFuG,OAAO,GAAG,CAACrD,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACtE,MAAM,GAAGiD,WAAW,CAACsD,QAAQ,CAACjC,IAAI,CAACtE,MAAM,IAAI,CAAC;MAC1F;MACA,MAAMkG,GAAG,GAAG;QACR7J,EAAE,EAAE8D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACE,EAAE,GAAG2J,KAAK,CAAC,GAAGK,OAAO;QAC5CjK,EAAE,EAAE+D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACC,EAAE,GAAG4J,KAAK,CAAC,GAAGM,OAAO;QAC5ChK,EAAE,EAAE6D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACG,EAAE,GAAG0J,KAAK,CAAC,GAAGK,OAAO;QAC5C9J,EAAE,EAAE4D,IAAI,CAACC,KAAK,CAACjE,OAAO,CAACI,EAAE,GAAGyJ,KAAK,CAAC,GAAGM;MACzC,CAAC;MACD,IAAI,CAACpD,QAAQ,CAAC9E,wBAAwB,EAAE;QACpC8H,GAAG,CAAC7J,EAAE,GAAG8D,IAAI,CAACgG,GAAG,CAACD,GAAG,CAAC7J,EAAE,EAAE,CAAC,CAAC;QAC5B6J,GAAG,CAAC9J,EAAE,GAAG+D,IAAI,CAACgG,GAAG,CAACD,GAAG,CAAC9J,EAAE,EAAE,CAAC,CAAC;QAC5B8J,GAAG,CAAC5J,EAAE,GAAG6D,IAAI,CAAC6B,GAAG,CAACkE,GAAG,CAAC5J,EAAE,EAAE2G,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACvE,KAAK,CAAC;QAC7DmG,GAAG,CAAC3J,EAAE,GAAG4D,IAAI,CAAC6B,GAAG,CAACkE,GAAG,CAAC3J,EAAE,EAAE0G,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACtE,MAAM,CAAC;MAClE;MACA,OAAOkG,GAAG;IACd;IACAjB,cAAcA,CAAClF,KAAK,EAAEC,MAAM,EAAEkD,QAAQ,EAAE;MACpC,MAAMsD,UAAU,GAAGtD,QAAQ,CAAC5F,aAAa,GAAGyC,KAAK;MACjD,MAAM0G,WAAW,GAAGvD,QAAQ,CAAC3F,cAAc,GAAGyC,MAAM;MACpD,MAAM0G,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC;MAC1B,IAAIzD,QAAQ,CAAC5F,aAAa,GAAG,CAAC,EAAE;QAC5BoJ,MAAM,CAACE,IAAI,CAACJ,UAAU,CAAC;MAC3B;MACA,IAAItD,QAAQ,CAAC3F,cAAc,GAAG,CAAC,EAAE;QAC7BmJ,MAAM,CAACE,IAAI,CAACH,WAAW,CAAC;MAC5B;MACA,MAAM7B,MAAM,GAAG8B,MAAM,CAACG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG1G,IAAI,CAAC6B,GAAG,CAAC,GAAG0E,MAAM,CAAC;MAC5D,IAAI9B,MAAM,GAAG,CAAC,IAAI,CAAC1B,QAAQ,CAAClF,aAAa,EAAE;QACvC,OAAO4G,MAAM;MACjB;MACA,OAAOzE,IAAI,CAAC6B,GAAG,CAAC4C,MAAM,EAAE,CAAC,CAAC;IAC9B;IACAS,UAAUA,CAACnC,QAAQ,EAAE;MACjB,OAAO/C,IAAI,CAAC6B,GAAG,CAAC,CAAC,EAAE7B,IAAI,CAACgG,GAAG,CAAC,CAAC,EAAEjD,QAAQ,CAACjF,YAAY,GAAG,GAAG,CAAC,CAAC;IAChE;EACJ;EACA8E,WAAW,CAAC+D,IAAI,YAAAC,oBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAyFjE,WAAW;EAAA,CAAoD;EACxKA,WAAW,CAACkE,KAAK,kBAD8ErQ,EAAE,CAAAsQ,kBAAA;IAAAC,KAAA,EACYpE,WAAW;IAAAqE,OAAA,EAAXrE,WAAW,CAAA+D,IAAA;IAAAO,UAAA,EAAc;EAAM,EAAG;EAAC,OAzI1ItE,WAAW;AAAA;AA0IjB;EAAA,QAAAuE,SAAA,oBAAAA,SAAA;AAAA;AAGc,IAERC,sBAAsB;EAA5B,MAAMA,sBAAsB,CAAC;IACzBC,oBAAoBA,CAACC,WAAW,EAAE5C,eAAe,EAAE3B,QAAQ,EAAEC,OAAO,EAAE;MAClE,IAAI,CAACsE,WAAW,EAAEC,aAAa,EAAE;QAC7B;MACJ;MACA,IAAIxE,QAAQ,CAACrF,mBAAmB,IAAIqF,QAAQ,CAACtF,kBAAkB,EAAE;QAC7DiH,eAAe,CAACxI,EAAE,GAAG,CAAC;QACtBwI,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK,GAAGmD,QAAQ,CAACtF,kBAAkB,GAC5DsF,QAAQ,CAACtF,kBAAkB,GAAGuF,OAAO,CAACpD,KAAK;QAC/C8E,eAAe,CAACzI,EAAE,GAAG,CAAC;QACtByI,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAM,GAAGkD,QAAQ,CAACrF,mBAAmB,GAC9DqF,QAAQ,CAACrF,mBAAmB,GAAGsF,OAAO,CAACnD,MAAM;MACrD,CAAC,MACI;QACD,MAAM2H,YAAY,GAAGxH,IAAI,CAAC6B,GAAG,CAACkB,QAAQ,CAAC3E,qBAAqB,EAAE4E,OAAO,CAACpD,KAAK,CAAC;QAC5E,MAAM6H,aAAa,GAAGzH,IAAI,CAAC6B,GAAG,CAACkB,QAAQ,CAAC1E,sBAAsB,EAAE2E,OAAO,CAACnD,MAAM,CAAC;QAC/E,IAAI,CAACkD,QAAQ,CAAChG,mBAAmB,EAAE;UAC/B2H,eAAe,CAACxI,EAAE,GAAG,CAAC;UACtBwI,eAAe,CAACvI,EAAE,GAAGqL,YAAY;UACjC9C,eAAe,CAACzI,EAAE,GAAG,CAAC;UACtByI,eAAe,CAACtI,EAAE,GAAGqL,aAAa;QACtC,CAAC,MACI,IAAIzE,OAAO,CAACpD,KAAK,GAAGmD,QAAQ,CAAC9F,WAAW,GAAG+F,OAAO,CAACnD,MAAM,EAAE;UAC5D6E,eAAe,CAACxI,EAAE,GAAG,CAAC;UACtBwI,eAAe,CAACvI,EAAE,GAAGqL,YAAY;UACjC,MAAME,4BAA4B,GAAGF,YAAY,GAAGzE,QAAQ,CAAC9F,WAAW;UACxEyH,eAAe,CAACzI,EAAE,GAAG,CAAC+G,OAAO,CAACnD,MAAM,GAAG6H,4BAA4B,IAAI,CAAC;UACxEhD,eAAe,CAACtI,EAAE,GAAGsI,eAAe,CAACzI,EAAE,GAAGyL,4BAA4B;QAC1E,CAAC,MACI;UACDhD,eAAe,CAACzI,EAAE,GAAG,CAAC;UACtByI,eAAe,CAACtI,EAAE,GAAGqL,aAAa;UAClC,MAAME,2BAA2B,GAAGF,aAAa,GAAG1E,QAAQ,CAAC9F,WAAW;UACxEyH,eAAe,CAACxI,EAAE,GAAG,CAAC8G,OAAO,CAACpD,KAAK,GAAG+H,2BAA2B,IAAI,CAAC;UACtEjD,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,GAAGyL,2BAA2B;QACzE;MACJ;IACJ;IACAC,IAAIA,CAACC,KAAK,EAAEC,SAAS,EAAEpD,eAAe,EAAE;MACpC,MAAMqD,KAAK,GAAG,IAAI,CAACC,UAAU,CAACH,KAAK,CAAC,GAAGC,SAAS,CAACtI,OAAO;MACxD,MAAMyI,KAAK,GAAG,IAAI,CAACC,UAAU,CAACL,KAAK,CAAC,GAAGC,SAAS,CAACrI,OAAO;MACxDiF,eAAe,CAACxI,EAAE,GAAG4L,SAAS,CAAC5L,EAAE,GAAG6L,KAAK;MACzCrD,eAAe,CAACzI,EAAE,GAAG6L,SAAS,CAAC7L,EAAE,GAAGgM,KAAK;MACzCvD,eAAe,CAACvI,EAAE,GAAG2L,SAAS,CAAC3L,EAAE,GAAG4L,KAAK;MACzCrD,eAAe,CAACtI,EAAE,GAAG0L,SAAS,CAAC1L,EAAE,GAAG6L,KAAK;IAC7C;IACAE,MAAMA,CAACN,KAAK,EAAEC,SAAS,EAAEpD,eAAe,EAAE1B,OAAO,EAAED,QAAQ,EAAE;MACzD,MAAMqF,KAAK,GAAG,IAAI,CAACJ,UAAU,CAACH,KAAK,CAAC,GAAGC,SAAS,CAACtI,OAAO;MACxD,MAAM6I,KAAK,GAAG,IAAI,CAACH,UAAU,CAACL,KAAK,CAAC,GAAGC,SAAS,CAACrI,OAAO;MACxD,QAAQqI,SAAS,CAACQ,QAAQ;QACtB,KAAK,MAAM;UACP5D,eAAe,CAACxI,EAAE,GAAG8D,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAAC8B,SAAS,CAAC5L,EAAE,GAAGkM,KAAK,EAAE1D,eAAe,CAACvI,EAAE,GAAG4G,QAAQ,CAAC3E,qBAAqB,CAAC,EAAEsG,eAAe,CAACvI,EAAE,GAAG4G,QAAQ,CAAC7E,qBAAqB,CAAC;UACvK;QACJ,KAAK,SAAS;UACVwG,eAAe,CAACxI,EAAE,GAAG8D,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAAC8B,SAAS,CAAC5L,EAAE,GAAGkM,KAAK,EAAE1D,eAAe,CAACvI,EAAE,GAAG4G,QAAQ,CAAC3E,qBAAqB,CAAC,EAAEsG,eAAe,CAACvI,EAAE,GAAG4G,QAAQ,CAAC7E,qBAAqB,CAAC;UACvKwG,eAAe,CAACzI,EAAE,GAAG+D,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAAC8B,SAAS,CAAC7L,EAAE,GAAGoM,KAAK,EAAE3D,eAAe,CAACtI,EAAE,GAAG2G,QAAQ,CAAC1E,sBAAsB,CAAC,EAAEqG,eAAe,CAACtI,EAAE,GAAG2G,QAAQ,CAAC5E,sBAAsB,CAAC;UACzK;QACJ,KAAK,KAAK;UACNuG,eAAe,CAACzI,EAAE,GAAG+D,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAAC8B,SAAS,CAAC7L,EAAE,GAAGoM,KAAK,EAAE3D,eAAe,CAACtI,EAAE,GAAG2G,QAAQ,CAAC1E,sBAAsB,CAAC,EAAEqG,eAAe,CAACtI,EAAE,GAAG2G,QAAQ,CAAC5E,sBAAsB,CAAC;UACzK;QACJ,KAAK,UAAU;UACXuG,eAAe,CAACvI,EAAE,GAAG6D,IAAI,CAACgG,GAAG,CAAChG,IAAI,CAAC6B,GAAG,CAACiG,SAAS,CAAC3L,EAAE,GAAGiM,KAAK,EAAE1D,eAAe,CAACxI,EAAE,GAAG6G,QAAQ,CAAC3E,qBAAqB,CAAC,EAAEsG,eAAe,CAACxI,EAAE,GAAG6G,QAAQ,CAAC7E,qBAAqB,CAAC;UACvKwG,eAAe,CAACzI,EAAE,GAAG+D,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAAC8B,SAAS,CAAC7L,EAAE,GAAGoM,KAAK,EAAE3D,eAAe,CAACtI,EAAE,GAAG2G,QAAQ,CAAC1E,sBAAsB,CAAC,EAAEqG,eAAe,CAACtI,EAAE,GAAG2G,QAAQ,CAAC5E,sBAAsB,CAAC;UACzK;QACJ,KAAK,OAAO;UACRuG,eAAe,CAACvI,EAAE,GAAG6D,IAAI,CAACgG,GAAG,CAAChG,IAAI,CAAC6B,GAAG,CAACiG,SAAS,CAAC3L,EAAE,GAAGiM,KAAK,EAAE1D,eAAe,CAACxI,EAAE,GAAG6G,QAAQ,CAAC3E,qBAAqB,CAAC,EAAEsG,eAAe,CAACxI,EAAE,GAAG6G,QAAQ,CAAC7E,qBAAqB,CAAC;UACvK;QACJ,KAAK,aAAa;UACdwG,eAAe,CAACvI,EAAE,GAAG6D,IAAI,CAACgG,GAAG,CAAChG,IAAI,CAAC6B,GAAG,CAACiG,SAAS,CAAC3L,EAAE,GAAGiM,KAAK,EAAE1D,eAAe,CAACxI,EAAE,GAAG6G,QAAQ,CAAC3E,qBAAqB,CAAC,EAAEsG,eAAe,CAACxI,EAAE,GAAG6G,QAAQ,CAAC7E,qBAAqB,CAAC;UACvKwG,eAAe,CAACtI,EAAE,GAAG4D,IAAI,CAACgG,GAAG,CAAChG,IAAI,CAAC6B,GAAG,CAACiG,SAAS,CAAC1L,EAAE,GAAGiM,KAAK,EAAE3D,eAAe,CAACzI,EAAE,GAAG8G,QAAQ,CAAC1E,sBAAsB,CAAC,EAAEqG,eAAe,CAACzI,EAAE,GAAG8G,QAAQ,CAAC5E,sBAAsB,CAAC;UACzK;QACJ,KAAK,QAAQ;UACTuG,eAAe,CAACtI,EAAE,GAAG4D,IAAI,CAACgG,GAAG,CAAChG,IAAI,CAAC6B,GAAG,CAACiG,SAAS,CAAC1L,EAAE,GAAGiM,KAAK,EAAE3D,eAAe,CAACzI,EAAE,GAAG8G,QAAQ,CAAC1E,sBAAsB,CAAC,EAAEqG,eAAe,CAACzI,EAAE,GAAG8G,QAAQ,CAAC5E,sBAAsB,CAAC;UACzK;QACJ,KAAK,YAAY;UACbuG,eAAe,CAACxI,EAAE,GAAG8D,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAAC8B,SAAS,CAAC5L,EAAE,GAAGkM,KAAK,EAAE1D,eAAe,CAACvI,EAAE,GAAG4G,QAAQ,CAAC3E,qBAAqB,CAAC,EAAEsG,eAAe,CAACvI,EAAE,GAAG4G,QAAQ,CAAC7E,qBAAqB,CAAC;UACvKwG,eAAe,CAACtI,EAAE,GAAG4D,IAAI,CAACgG,GAAG,CAAChG,IAAI,CAAC6B,GAAG,CAACiG,SAAS,CAAC1L,EAAE,GAAGiM,KAAK,EAAE3D,eAAe,CAACzI,EAAE,GAAG8G,QAAQ,CAAC1E,sBAAsB,CAAC,EAAEqG,eAAe,CAACzI,EAAE,GAAG8G,QAAQ,CAAC5E,sBAAsB,CAAC;UACzK;QACJ,KAAK,QAAQ;UACT,MAAMsF,KAAK,GAAGoE,KAAK,CAACpE,KAAK;UACzB,MAAM8E,QAAQ,GAAGvI,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAACjD,QAAQ,CAAC7E,qBAAqB,EAAG8B,IAAI,CAACgC,GAAG,CAAC8F,SAAS,CAAC3L,EAAE,GAAG2L,SAAS,CAAC5L,EAAE,CAAC,GAAIuH,KAAK,CAAC,EAAEV,QAAQ,CAAC3E,qBAAqB,CAAC;UACpJ,MAAMoK,SAAS,GAAGxI,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAACgG,GAAG,CAACjD,QAAQ,CAAC5E,sBAAsB,EAAG6B,IAAI,CAACgC,GAAG,CAAC8F,SAAS,CAAC1L,EAAE,GAAG0L,SAAS,CAAC7L,EAAE,CAAC,GAAIwH,KAAK,CAAC,EAAEV,QAAQ,CAAC1E,sBAAsB,CAAC;UACvJqG,eAAe,CAACxI,EAAE,GAAG4L,SAAS,CAACtI,OAAO,GAAG+I,QAAQ,GAAG,CAAC;UACrD7D,eAAe,CAACvI,EAAE,GAAG2L,SAAS,CAACtI,OAAO,GAAG+I,QAAQ,GAAG,CAAC;UACrD7D,eAAe,CAACzI,EAAE,GAAG6L,SAAS,CAACrI,OAAO,GAAG+I,SAAS,GAAG,CAAC;UACtD9D,eAAe,CAACtI,EAAE,GAAG0L,SAAS,CAACrI,OAAO,GAAG+I,SAAS,GAAG,CAAC;UACtD,IAAI9D,eAAe,CAACxI,EAAE,GAAG,CAAC,EAAE;YACxBwI,eAAe,CAACvI,EAAE,IAAIuI,eAAe,CAACxI,EAAE;YACxCwI,eAAe,CAACxI,EAAE,GAAG,CAAC;UAC1B,CAAC,MACI,IAAIwI,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK,EAAE;YACzC8E,eAAe,CAACxI,EAAE,IAAKwI,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAM;YAC1D8E,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK;UACtC;UACA,IAAI8E,eAAe,CAACzI,EAAE,GAAG,CAAC,EAAE;YACxByI,eAAe,CAACtI,EAAE,IAAIsI,eAAe,CAACzI,EAAE;YACxCyI,eAAe,CAACzI,EAAE,GAAG,CAAC;UAC1B,CAAC,MACI,IAAIyI,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAM,EAAE;YAC1C6E,eAAe,CAACzI,EAAE,IAAKyI,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAO;YAC3D6E,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAM;UACvC;UACA;MACR;MACA,IAAIkD,QAAQ,CAAChG,mBAAmB,EAAE;QAC9B,IAAI,CAAC0L,gBAAgB,CAACX,SAAS,CAACQ,QAAQ,EAAE5D,eAAe,EAAE1B,OAAO,EAAED,QAAQ,CAAC;MACjF;IACJ;IACA0F,gBAAgBA,CAACH,QAAQ,EAAE5D,eAAe,EAAE1B,OAAO,EAAED,QAAQ,EAAE;MAC3D,IAAI2F,SAAS,GAAG,CAAC;MACjB,IAAIC,SAAS,GAAG,CAAC;MACjB,QAAQL,QAAQ;QACZ,KAAK,KAAK;UACN5D,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,GAAG,CAACwI,eAAe,CAACtI,EAAE,GAAGsI,eAAe,CAACzI,EAAE,IAAI8G,QAAQ,CAAC9F,WAAW;UAC1GyL,SAAS,GAAG1I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK,EAAE,CAAC,CAAC;UAC3D+I,SAAS,GAAG3I,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGtB,eAAe,CAACzI,EAAE,EAAE,CAAC,CAAC;UAC/C,IAAIyM,SAAS,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,EAAE;YAChCjE,eAAe,CAACvI,EAAE,IAAKwM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAIC,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS;YACrHhE,eAAe,CAACzI,EAAE,IAAK0M,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAGC,SAAS,GAAGD,SAAS,GAAG3F,QAAQ,CAAC9F,WAAW;UACvH;UACA;QACJ,KAAK,QAAQ;UACTyH,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,GAAG,CAACwI,eAAe,CAACtI,EAAE,GAAGsI,eAAe,CAACzI,EAAE,IAAI8G,QAAQ,CAAC9F,WAAW;UAC1GyL,SAAS,GAAG1I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK,EAAE,CAAC,CAAC;UAC3D+I,SAAS,GAAG3I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAM,EAAE,CAAC,CAAC;UAC5D,IAAI6I,SAAS,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,EAAE;YAChCjE,eAAe,CAACvI,EAAE,IAAKwM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAIC,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS;YACrHhE,eAAe,CAACtI,EAAE,IAAKuM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAGC,SAAS,GAAID,SAAS,GAAG3F,QAAQ,CAAC9F,WAAY;UACzH;UACA;QACJ,KAAK,SAAS;UACVyH,eAAe,CAACzI,EAAE,GAAGyI,eAAe,CAACtI,EAAE,GAAG,CAACsI,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,IAAI6G,QAAQ,CAAC9F,WAAW;UAC1GyL,SAAS,GAAG1I,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGtB,eAAe,CAACxI,EAAE,EAAE,CAAC,CAAC;UAC/CyM,SAAS,GAAG3I,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGtB,eAAe,CAACzI,EAAE,EAAE,CAAC,CAAC;UAC/C,IAAIyM,SAAS,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,EAAE;YAChCjE,eAAe,CAACxI,EAAE,IAAKyM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAIC,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS;YACrHhE,eAAe,CAACzI,EAAE,IAAK0M,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAGC,SAAS,GAAGD,SAAS,GAAG3F,QAAQ,CAAC9F,WAAW;UACvH;UACA;QACJ,KAAK,UAAU;UACXyH,eAAe,CAACzI,EAAE,GAAGyI,eAAe,CAACtI,EAAE,GAAG,CAACsI,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,IAAI6G,QAAQ,CAAC9F,WAAW;UAC1GyL,SAAS,GAAG1I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK,EAAE,CAAC,CAAC;UAC3D+I,SAAS,GAAG3I,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGtB,eAAe,CAACzI,EAAE,EAAE,CAAC,CAAC;UAC/C,IAAIyM,SAAS,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,EAAE;YAChCjE,eAAe,CAACvI,EAAE,IAAKwM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAIC,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS;YACrHhE,eAAe,CAACzI,EAAE,IAAK0M,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAGC,SAAS,GAAGD,SAAS,GAAG3F,QAAQ,CAAC9F,WAAW;UACvH;UACA;QACJ,KAAK,OAAO;QACZ,KAAK,aAAa;UACdyH,eAAe,CAACtI,EAAE,GAAGsI,eAAe,CAACzI,EAAE,GAAG,CAACyI,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,IAAI6G,QAAQ,CAAC9F,WAAW;UAC1GyL,SAAS,GAAG1I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK,EAAE,CAAC,CAAC;UAC3D+I,SAAS,GAAG3I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAM,EAAE,CAAC,CAAC;UAC5D,IAAI6I,SAAS,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,EAAE;YAChCjE,eAAe,CAACvI,EAAE,IAAKwM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAIC,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS;YACrHhE,eAAe,CAACtI,EAAE,IAAKuM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAGC,SAAS,GAAGD,SAAS,GAAG3F,QAAQ,CAAC9F,WAAW;UACvH;UACA;QACJ,KAAK,MAAM;QACX,KAAK,YAAY;UACbyH,eAAe,CAACtI,EAAE,GAAGsI,eAAe,CAACzI,EAAE,GAAG,CAACyI,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,IAAI6G,QAAQ,CAAC9F,WAAW;UAC1GyL,SAAS,GAAG1I,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGtB,eAAe,CAACxI,EAAE,EAAE,CAAC,CAAC;UAC/CyM,SAAS,GAAG3I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAM,EAAE,CAAC,CAAC;UAC5D,IAAI6I,SAAS,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,EAAE;YAChCjE,eAAe,CAACxI,EAAE,IAAKyM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAIC,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS;YACrHhE,eAAe,CAACtI,EAAE,IAAKuM,SAAS,GAAG5F,QAAQ,CAAC9F,WAAW,GAAIyL,SAAS,GAAGC,SAAS,GAAGD,SAAS,GAAG3F,QAAQ,CAAC9F,WAAW;UACvH;UACA;QACJ,KAAK,QAAQ;UACTyH,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,GAAG,CAACwI,eAAe,CAACtI,EAAE,GAAGsI,eAAe,CAACzI,EAAE,IAAI8G,QAAQ,CAAC9F,WAAW;UAC1GyH,eAAe,CAACtI,EAAE,GAAGsI,eAAe,CAACzI,EAAE,GAAG,CAACyI,eAAe,CAACvI,EAAE,GAAGuI,eAAe,CAACxI,EAAE,IAAI6G,QAAQ,CAAC9F,WAAW;UAC1G,MAAM2L,UAAU,GAAG5I,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGtB,eAAe,CAACxI,EAAE,EAAE,CAAC,CAAC;UACtD,MAAM2M,UAAU,GAAG7I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACvI,EAAE,GAAG6G,OAAO,CAACpD,KAAK,EAAE,CAAC,CAAC;UAClE,MAAMkJ,UAAU,GAAG9I,IAAI,CAACgG,GAAG,CAACtB,eAAe,CAACtI,EAAE,GAAG4G,OAAO,CAACnD,MAAM,EAAE,CAAC,CAAC;UACnE,MAAMkJ,UAAU,GAAG/I,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGtB,eAAe,CAACzI,EAAE,EAAE,CAAC,CAAC;UACtD,IAAI2M,UAAU,GAAG,CAAC,IAAIC,UAAU,GAAG,CAAC,IAAIC,UAAU,GAAG,CAAC,IAAIC,UAAU,GAAG,CAAC,EAAE;YACtErE,eAAe,CAACxI,EAAE,IAAK4M,UAAU,GAAG/F,QAAQ,CAAC9F,WAAW,GAAI2L,UAAU,GAAIE,UAAU,GAAG/F,QAAQ,CAAC9F,WAAW,GAAI2L,UAAU;YACzHlE,eAAe,CAACvI,EAAE,IAAK4M,UAAU,GAAGhG,QAAQ,CAAC9F,WAAW,GAAI4L,UAAU,GAAIE,UAAU,GAAGhG,QAAQ,CAAC9F,WAAW,GAAI4L,UAAU;YACzHnE,eAAe,CAACzI,EAAE,IAAK8M,UAAU,GAAGhG,QAAQ,CAAC9F,WAAW,GAAI4L,UAAU,GAAGE,UAAU,GAAGF,UAAU,GAAG9F,QAAQ,CAAC9F,WAAW;YACvHyH,eAAe,CAACtI,EAAE,IAAK0M,UAAU,GAAG/F,QAAQ,CAAC9F,WAAW,GAAI2L,UAAU,GAAGE,UAAU,GAAGF,UAAU,GAAG7F,QAAQ,CAAC9F,WAAW;UAC3H;UACA;MACR;IACJ;IACA+K,UAAUA,CAACH,KAAK,EAAE;MACd,OAAOA,KAAK,CAACmB,OAAO,GAAG,CAAC,CAAC,CAACxJ,OAAO,IAAIqI,KAAK,CAACrI,OAAO,IAAI,CAAC;IAC3D;IACA0I,UAAUA,CAACL,KAAK,EAAE;MACd,OAAOA,KAAK,CAACmB,OAAO,GAAG,CAAC,CAAC,CAACvJ,OAAO,IAAIoI,KAAK,CAACpI,OAAO,IAAI,CAAC;IAC3D;EACJ;EACA2H,sBAAsB,CAACT,IAAI,YAAAsC,+BAAApC,CAAA;IAAA,YAAAA,CAAA,IAAyFO,sBAAsB;EAAA,CAAoD;EAC9LA,sBAAsB,CAACN,KAAK,kBAvMmErQ,EAAE,CAAAsQ,kBAAA;IAAAC,KAAA,EAuMuBI,sBAAsB;IAAAH,OAAA,EAAtBG,sBAAsB,CAAAT,IAAA;IAAAO,UAAA,EAAc;EAAM,EAAG;EAAC,OAhMhKE,sBAAsB;AAAA;AAiM5B;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;;AAKA;AACA;AACA;AACA,MAAM+B,2BAA2B,GAAG,wEAAwE,GACxG,wEAAwE,GACxE,wEAAwE,GACxE,wEAAwE,GACxE,wEAAwE,GACxE,2DAA2D;AAC/D,SAASC,yBAAyBA,CAAA,EAAG;EACjC,OAAO,IAAI7D,OAAO,CAAEC,OAAO,IAAK;IAC5B,MAAM/E,GAAG,GAAG,IAAI4I,KAAK,CAAC,CAAC;IACvB5I,GAAG,CAAC6I,MAAM,GAAG,MAAM;MACf;MACA,MAAMC,SAAS,GAAG9I,GAAG,CAACZ,KAAK,KAAK,CAAC,IAAIY,GAAG,CAACX,MAAM,KAAK,CAAC;MACrD0F,OAAO,CAAC+D,SAAS,CAAC;IACtB,CAAC;IACD9I,GAAG,CAAC+I,GAAG,GAAGL,2BAA2B;EACzC,CAAC,CAAC;AACN;AACA,SAASM,8BAA8BA,CAACC,yBAAyB,EAAE;EAC/D,IAAI,OAAOA,yBAAyB,KAAK,QAAQ,EAAE;IAC/CA,yBAAyB,GAAGC,eAAe,CAACD,yBAAyB,CAAC;EAC1E;EACA,QAAQA,yBAAyB;IAC7B,KAAK,CAAC;MACF,OAAO;QAAEpF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAK,CAAC;IACpC,KAAK,CAAC;MACF,OAAO;QAAEtF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAM,CAAC;IACrC,KAAK,CAAC;MACF,OAAO;QAAEtF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAK,CAAC;IACpC,KAAK,CAAC;MACF,OAAO;QAAEtF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAK,CAAC;IACpC,KAAK,CAAC;MACF,OAAO;QAAEtF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAM,CAAC;IACrC,KAAK,CAAC;MACF,OAAO;QAAEtF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAK,CAAC;IACpC,KAAK,CAAC;MACF,OAAO;QAAEtF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAM,CAAC;IACrC;MACI,OAAO;QAAEtF,MAAM,EAAE,CAAC;QAAEsF,IAAI,EAAE;MAAM,CAAC;EACzC;AACJ;AACA,SAASD,eAAeA,CAACE,WAAW,EAAE;EAClC,MAAMC,IAAI,GAAG,IAAIC,QAAQ,CAACF,WAAW,CAAC;EACtC,IAAIC,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,MAAM,EAAE;IACrC,OAAO,CAAC,CAAC;EACb;EACA,MAAMrD,MAAM,GAAGmD,IAAI,CAACG,UAAU;EAC9B,IAAIC,MAAM,GAAG,CAAC;EACd,OAAOA,MAAM,GAAGvD,MAAM,EAAE;IACpB,IAAImD,IAAI,CAACE,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,EACtC,OAAO,CAAC,CAAC;IACb,MAAMC,MAAM,GAAGL,IAAI,CAACE,SAAS,CAACE,MAAM,EAAE,KAAK,CAAC;IAC5CA,MAAM,IAAI,CAAC;IACX,IAAIC,MAAM,IAAI,MAAM,EAAE;MAClB,IAAIL,IAAI,CAACM,SAAS,CAACF,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,UAAU,EAAE;QACnD,OAAO,CAAC,CAAC;MACb;MACA,MAAMG,MAAM,GAAGP,IAAI,CAACE,SAAS,CAACE,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,MAAM;MAC3DA,MAAM,IAAIJ,IAAI,CAACM,SAAS,CAACF,MAAM,GAAG,CAAC,EAAEG,MAAM,CAAC;MAC5C,MAAMC,IAAI,GAAGR,IAAI,CAACE,SAAS,CAACE,MAAM,EAAEG,MAAM,CAAC;MAC3CH,MAAM,IAAI,CAAC;MACX,KAAK,IAAIlJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsJ,IAAI,EAAEtJ,CAAC,EAAE,EAAE;QAC3B,IAAI8I,IAAI,CAACE,SAAS,CAACE,MAAM,GAAIlJ,CAAC,GAAG,EAAG,EAAEqJ,MAAM,CAAC,IAAI,MAAM,EAAE;UACrD,OAAOP,IAAI,CAACE,SAAS,CAACE,MAAM,GAAIlJ,CAAC,GAAG,EAAG,GAAG,CAAC,EAAEqJ,MAAM,CAAC;QACxD;MACJ;IACJ,CAAC,MACI,IAAI,CAACF,MAAM,GAAG,MAAM,MAAM,MAAM,EAAE;MACnC;IACJ,CAAC,MACI;MACDD,MAAM,IAAIJ,IAAI,CAACE,SAAS,CAACE,MAAM,EAAE,KAAK,CAAC;IAC3C;EACJ;EACA,OAAO,CAAC,CAAC;AACb;AAAC,IAEKK,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB1N,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC2N,mBAAmB,GAAGpB,yBAAyB,CAAC,CAAC;IAC1D;IACMqB,aAAaA,CAACC,IAAI,EAAEC,eAAe,EAAE;MAAA,IAAAC,MAAA;MAAA,OAAAvF,iBAAA;QACvC,MAAMwE,WAAW,SAASa,IAAI,CAACb,WAAW,CAAC,CAAC;QAC5C,aAAae,MAAI,CAACC,yCAAyC,CAAChB,WAAW,EAAEa,IAAI,CAACI,IAAI,EAAEH,eAAe,CAAC;MAAC;IACzG;IACAE,yCAAyCA,CAAChB,WAAW,EAAEkB,SAAS,EAAEJ,eAAe,EAAE;MAC/E,IAAI,CAAC,IAAI,CAACK,gBAAgB,CAACD,SAAS,CAAC,EAAE;QACnC,OAAOxF,OAAO,CAAC0F,MAAM,CAAC,IAAI9L,KAAK,CAAC,oBAAoB,CAAC,CAAC;MAC1D;MACA,OAAO,IAAI,CAAC+L,wBAAwB,CAACrB,WAAW,EAAEc,eAAe,EAAEI,SAAS,CAAC;IACjF;IACAC,gBAAgBA,CAACF,IAAI,EAAE;MACnB,OAAO,uEAAuE,CAACK,IAAI,CAACL,IAAI,CAAC;IAC7F;IACMM,gBAAgBA,CAACC,GAAG,EAAEV,eAAe,EAAE;MAAA,IAAAW,MAAA;MAAA,OAAAjG,iBAAA;QACzC,MAAMkG,GAAG,SAASC,KAAK,CAACH,GAAG,CAAC;QAC5B,MAAM/F,IAAI,SAASiG,GAAG,CAACjG,IAAI,CAAC,CAAC;QAC7B,MAAMmG,MAAM,SAASnG,IAAI,CAACuE,WAAW,CAAC,CAAC;QACvC,aAAayB,MAAI,CAACJ,wBAAwB,CAACO,MAAM,EAAEd,eAAe,EAAErF,IAAI,CAACwF,IAAI,CAAC;MAAC;IACnF;IACAY,eAAeA,CAACC,WAAW,EAAEhB,eAAe,EAAE;MAC1C,MAAMd,WAAW,GAAG,IAAI,CAAC+B,mBAAmB,CAACD,WAAW,CAAC;MACzD,OAAO,IAAI,CAACT,wBAAwB,CAACrB,WAAW,EAAEc,eAAe,CAAC;IACtE;IACAiB,mBAAmBA,CAACD,WAAW,EAAE;MAC7BA,WAAW,GAAGA,WAAW,CAACE,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC;MACjE,MAAMC,YAAY,GAAGC,IAAI,CAACJ,WAAW,CAAC;MACtC,MAAMK,GAAG,GAAGF,YAAY,CAACnF,MAAM;MAC/B,MAAMsF,KAAK,GAAG,IAAIC,UAAU,CAACF,GAAG,CAAC;MACjC,KAAK,IAAIhL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgL,GAAG,EAAEhL,CAAC,EAAE,EAAE;QAC1BiL,KAAK,CAACjL,CAAC,CAAC,GAAG8K,YAAY,CAACK,UAAU,CAACnL,CAAC,CAAC;MACzC;MACA,OAAOiL,KAAK,CAACR,MAAM;IACvB;IACMP,wBAAwBA,CAACrB,WAAW,EAAEc,eAAe,EAAEI,SAAS,EAAE;MAAA,IAAAqB,MAAA;MAAA,OAAA/G,iBAAA;QACpE,MAAMkG,GAAG,SAAS,IAAIhG,OAAO;UAAA,IAAA8G,IAAA,GAAAhH,iBAAA,CAAC,WAAOG,OAAO,EAAEyF,MAAM,EAAK;YACrD,IAAI;cACA,MAAM3F,IAAI,GAAG,IAAIgH,IAAI,CAAC,CAACzC,WAAW,CAAC,EAAEkB,SAAS,GAAG;gBAAED,IAAI,EAAEC;cAAU,CAAC,GAAGwB,SAAS,CAAC;cACjF,MAAM7G,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;cAC3C,MAAMkH,aAAa,GAAG,IAAInD,KAAK,CAAC,CAAC;cACjC,MAAMoD,KAAK,GAAG1B,SAAS,KAAK,eAAe;cAC3C,MAAM2B,iBAAiB,GAAGD,KAAK,SAASL,MAAI,CAACO,eAAe,CAACrH,IAAI,CAAC,GAAGiH,SAAS;cAC9EC,aAAa,CAAClD,MAAM,GAAG,MAAM9D,OAAO,CAAC;gBACjCgH,aAAa;gBACbE,iBAAiB;gBACjBE,iBAAiB,EAAElH,SAAS;gBAC5BmH,mBAAmB,EAAEhD;cACzB,CAAC,CAAC;cACF2C,aAAa,CAACM,OAAO,GAAG7B,MAAM;cAC9BuB,aAAa,CAAChD,GAAG,GAAG9D,SAAS;YACjC,CAAC,CACD,OAAOqH,CAAC,EAAE;cACN9B,MAAM,CAAC8B,CAAC,CAAC;YACb;UACJ,CAAC;UAAA,iBAAAC,EAAA,EAAAC,GAAA;YAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;QACF,aAAaf,MAAI,CAACgB,6BAA6B,CAAC7B,GAAG,EAAEZ,eAAe,EAAEY,GAAG,CAACmB,iBAAiB,IAAI,IAAI,CAAC;MAAC;IACzG;IACMC,eAAeA,CAACrH,IAAI,EAAE;MAAA,OAAAD,iBAAA;QACxB,MAAMgI,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;QAC9B,MAAMC,GAAG,GAAGF,MAAM,CAACG,eAAe,OAAOlI,IAAI,CAACmI,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC;QACtE,MAAMC,UAAU,GAAGH,GAAG,CAACI,aAAa,CAAC,KAAK,CAAC;QAC3C,IAAI,CAACD,UAAU,EAAE;UACb,MAAMvO,KAAK,CAAC,2BAA2B,CAAC;QAC5C;QACA,MAAMyO,SAAS,GAAGF,UAAU,CAACG,YAAY,CAAC,OAAO,CAAC;QAClD,MAAMC,UAAU,GAAGJ,UAAU,CAACG,YAAY,CAAC,QAAQ,CAAC;QACpD,IAAID,SAAS,IAAIE,UAAU,EAAE;UACzB,OAAO,IAAI;QACf;QACA,MAAMC,WAAW,GAAGL,UAAU,CAACG,YAAY,CAAC,SAAS,CAAC,IAC/CH,UAAU,CAACG,YAAY,CAAC,SAAS,CAAC;QACzC,IAAIE,WAAW,EAAE;UACb,MAAMC,OAAO,GAAGD,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC;UACtC,OAAO;YACHpO,KAAK,EAAE,CAACmO,OAAO,CAAC,CAAC,CAAC;YAClBlO,MAAM,EAAE,CAACkO,OAAO,CAAC,CAAC;UACtB,CAAC;QACL;QACA,MAAM7O,KAAK,CAAC,+EAA+E,CAAC;MAAC;IACjG;IACMiO,6BAA6BA,CAAC7B,GAAG,EAAEZ,eAAe,EAAEuD,cAAc,GAAG,KAAK,EAAE;MAAA,IAAAC,MAAA;MAAA,OAAA9I,iBAAA;QAC9E,MAAM+I,UAAU,SAASD,MAAI,CAAC3D,mBAAmB;QACjD,MAAMtE,aAAa,GAAGuD,8BAA8B,CAAC2E,UAAU,GAAG,CAAC,CAAC,GAAG7C,GAAG,CAACsB,mBAAmB,CAAC;QAC/F,IAAI,CAACtB,GAAG,CAACiB,aAAa,IAAI,CAACjB,GAAG,CAACiB,aAAa,CAAC6B,QAAQ,EAAE;UACnD,OAAO9I,OAAO,CAAC0F,MAAM,CAAC,IAAI9L,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACvD;QACA,MAAM4D,WAAW,GAAG;UAChBsD,QAAQ,EAAE;YACNX,SAAS,EAAE6F,GAAG,CAACqB,iBAAiB;YAChCnI,KAAK,EAAE8G,GAAG,CAACiB,aAAa;YACxBpI,IAAI,EAAEmH,GAAG,CAACmB,iBAAiB,IAAI;cAC3B7M,KAAK,EAAE0L,GAAG,CAACiB,aAAa,CAAC8B,YAAY;cACrCxO,MAAM,EAAEyL,GAAG,CAACiB,aAAa,CAAC+B;YAC9B;UACJ,CAAC;UACDrI;QACJ,CAAC;QACD,OAAOiI,MAAI,CAACK,oBAAoB,CAACzL,WAAW,EAAE4H,eAAe,EAAEuD,cAAc,CAAC;MAAC;IACnF;IACMM,oBAAoBA,CAACzL,WAAW,EAAE4H,eAAe,EAAEuD,cAAc,GAAG,KAAK,EAAE;MAAA,IAAAO,MAAA;MAAA,OAAApJ,iBAAA;QAC7E,MAAMzH,cAAc,GAAG+M,eAAe,CAAC/M,cAAc,GAAGmF,WAAW,CAACmD,aAAa,CAAC5B,MAAM;QACxF,MAAMoK,YAAY,GAAG3L,WAAW,CAACsD,QAAQ,CAACjC,IAAI;QAC9C,IAAI,CAAC8J,cAAc,IAAItQ,cAAc,KAAK,CAAC,IAAI,CAACmF,WAAW,CAACmD,aAAa,CAAC0D,IAAI,IAAI,CAACe,eAAe,CAACzM,wBAAwB,EAAE;UACzH,OAAO;YACHmI,QAAQ,EAAE;cACNX,SAAS,EAAE3C,WAAW,CAACsD,QAAQ,CAACX,SAAS;cACzCjB,KAAK,EAAE1B,WAAW,CAACsD,QAAQ,CAAC5B,KAAK;cACjCL,IAAI,EAAE;gBAAE,GAAGsK;cAAa;YAC5B,CAAC;YACDxK,WAAW,EAAE;cACTwB,SAAS,EAAE3C,WAAW,CAACsD,QAAQ,CAACX,SAAS;cACzCjB,KAAK,EAAE1B,WAAW,CAACsD,QAAQ,CAAC5B,KAAK;cACjCL,IAAI,EAAE;gBAAE,GAAGsK;cAAa;YAC5B,CAAC;YACDxI,aAAa,EAAEnD,WAAW,CAACmD;UAC/B,CAAC;QACL;QACA,MAAMyI,eAAe,GAAGF,MAAI,CAACG,kBAAkB,CAACF,YAAY,EAAE3L,WAAW,CAACmD,aAAa,EAAEyE,eAAe,CAAC;QACzG,MAAM/K,MAAM,GAAGyD,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C1D,MAAM,CAACC,KAAK,GAAG8O,eAAe,CAAC9O,KAAK;QACpCD,MAAM,CAACE,MAAM,GAAG6O,eAAe,CAAC7O,MAAM;QACtC,MAAM1H,GAAG,GAAGwH,MAAM,CAACY,UAAU,CAAC,IAAI,CAAC;QACnCpI,GAAG,EAAE+L,YAAY,CAACpB,WAAW,CAACmD,aAAa,CAAC0D,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEhK,MAAM,CAACC,KAAK,GAAG,CAAC,EAAED,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;QACxG1H,GAAG,EAAEkM,MAAM,CAACrE,IAAI,CAACsE,EAAE,IAAI3G,cAAc,GAAG,CAAC,CAAC,CAAC;QAC3CxF,GAAG,EAAEoM,SAAS,CAACzB,WAAW,CAACsD,QAAQ,CAAC5B,KAAK,EAAE,CAACiK,YAAY,CAAC7O,KAAK,GAAG,CAAC,EAAE,CAAC6O,YAAY,CAAC5O,MAAM,GAAG,CAAC,CAAC;QAC7F,MAAMwF,IAAI,SAAS,IAAIC,OAAO,CAACC,OAAO,IAAI5F,MAAM,CAAC6F,MAAM,CAACD,OAAO,EAAEmF,eAAe,CAAC7N,MAAM,CAAC,CAAC;QACzF,IAAI,CAACwI,IAAI,EAAE;UACP,MAAM,IAAInG,KAAK,CAAC,2CAA2C,CAAC;QAChE;QACA,MAAMuG,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QAC3C,MAAMrB,gBAAgB,SAASwK,MAAI,CAACI,sBAAsB,CAACnJ,SAAS,CAAC;QACrE,OAAO;UACHW,QAAQ,EAAE;YACNX,SAAS,EAAE3C,WAAW,CAACsD,QAAQ,CAACX,SAAS;YACzCjB,KAAK,EAAE1B,WAAW,CAACsD,QAAQ,CAAC5B,KAAK;YACjCL,IAAI,EAAE;cAAE,GAAGsK;YAAa;UAC5B,CAAC;UACDxK,WAAW,EAAE;YACTwB,SAAS,EAAEA,SAAS;YACpBjB,KAAK,EAAER,gBAAgB;YACvBG,IAAI,EAAE;cACFvE,KAAK,EAAEoE,gBAAgB,CAACpE,KAAK;cAC7BC,MAAM,EAAEmE,gBAAgB,CAACnE;YAC7B;UACJ,CAAC;UACDoG,aAAa,EAAEnD,WAAW,CAACmD;QAC/B,CAAC;MAAC;IACN;IACA2I,sBAAsBA,CAACnJ,SAAS,EAAE;MAC9B,OAAO,IAAIH,OAAO,CAAE,CAACC,OAAO,EAAEyF,MAAM,KAAK;QACrC,MAAMxG,KAAK,GAAG,IAAI4E,KAAK,CAAC,CAAC;QACzB5E,KAAK,CAAC6E,MAAM,GAAG,MAAM9D,OAAO,CAACf,KAAK,CAAC;QACnCA,KAAK,CAACqI,OAAO,GAAG7B,MAAM;QACtBxG,KAAK,CAAC+E,GAAG,GAAG9D,SAAS;MACzB,CAAE,CAAC;IACP;IACAkJ,kBAAkBA,CAACF,YAAY,EAAExI,aAAa,EAAEyE,eAAe,EAAE;MAC7D,MAAM/M,cAAc,GAAG+M,eAAe,CAAC/M,cAAc,GAAGsI,aAAa,CAAC5B,MAAM;MAC5E,IAAIqG,eAAe,CAACzM,wBAAwB,EAAE;QAC1C,IAAIN,cAAc,GAAG,CAAC,EAAE;UACpB,MAAMkR,iBAAiB,GAAGJ,YAAY,CAAC7O,KAAK,GAAG8K,eAAe,CAACzN,WAAW;UAC1E,MAAM6R,kBAAkB,GAAGL,YAAY,CAAC5O,MAAM,GAAG6K,eAAe,CAACzN,WAAW;UAC5E,OAAO;YACH2C,KAAK,EAAEI,IAAI,CAACgG,GAAG,CAACyI,YAAY,CAAC5O,MAAM,EAAEgP,iBAAiB,CAAC;YACvDhP,MAAM,EAAEG,IAAI,CAACgG,GAAG,CAACyI,YAAY,CAAC7O,KAAK,EAAEkP,kBAAkB;UAC3D,CAAC;QACL,CAAC,MACI;UACD,MAAMD,iBAAiB,GAAGJ,YAAY,CAAC5O,MAAM,GAAG6K,eAAe,CAACzN,WAAW;UAC3E,MAAM6R,kBAAkB,GAAGL,YAAY,CAAC7O,KAAK,GAAG8K,eAAe,CAACzN,WAAW;UAC3E,OAAO;YACH2C,KAAK,EAAEI,IAAI,CAACgG,GAAG,CAACyI,YAAY,CAAC7O,KAAK,EAAEiP,iBAAiB,CAAC;YACtDhP,MAAM,EAAEG,IAAI,CAACgG,GAAG,CAACyI,YAAY,CAAC5O,MAAM,EAAEiP,kBAAkB;UAC5D,CAAC;QACL;MACJ;MACA,IAAInR,cAAc,GAAG,CAAC,EAAE;QACpB,OAAO;UACHkC,MAAM,EAAE4O,YAAY,CAAC7O,KAAK;UAC1BA,KAAK,EAAE6O,YAAY,CAAC5O;QACxB,CAAC;MACL;MACA,OAAO;QACHD,KAAK,EAAE6O,YAAY,CAAC7O,KAAK;QACzBC,MAAM,EAAE4O,YAAY,CAAC5O;MACzB,CAAC;IACL;EACJ;EACAyK,gBAAgB,CAAC3D,IAAI,YAAAoI,yBAAAlI,CAAA;IAAA,YAAAA,CAAA,IAAyFyD,gBAAgB;EAAA,CAAoD;EAClLA,gBAAgB,CAACxD,KAAK,kBA5dyErQ,EAAE,CAAAsQ,kBAAA;IAAAC,KAAA,EA4diBsD,gBAAgB;IAAArD,OAAA,EAAhBqD,gBAAgB,CAAA3D,IAAA;IAAAO,UAAA,EAAc;EAAM,EAAG;EAAC,OAhMpJoD,gBAAgB;AAAA;AAiMtB;EAAA,QAAAnD,SAAA,oBAAAA,SAAA;AAAA;AAGc,IAER6H,qBAAqB;EAA3B,MAAMA,qBAAqB,CAAC;IACxBpS,WAAWA,CAACqS,WAAW,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,EAAE,EAAEC,IAAI,EAAEC,YAAY,EAAE;MAClG,IAAI,CAACN,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACC,sBAAsB,GAAGA,sBAAsB;MACpD,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACxM,QAAQ,GAAG,IAAIpG,eAAe,CAAC,CAAC;MACrC,IAAI,CAAC6S,sBAAsB,GAAG,CAAC;MAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACnT,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC0G,OAAO,GAAG;QACXpD,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE;MACZ,CAAC;MACD,IAAI,CAAC5G,SAAS,GAAGkG,SAAS;MAC1B,IAAI,CAAC3F,YAAY,GAAG,KAAK;MACzB,IAAI,CAACgD,qBAAqB,GAAG,IAAI,CAACuG,QAAQ,CAACvG,qBAAqB;MAChE,IAAI,CAACM,MAAM,GAAG,IAAI,CAACiG,QAAQ,CAACjG,MAAM;MAClC,IAAI,CAACD,MAAM,GAAG,IAAI,CAACkG,QAAQ,CAAClG,MAAM;MAClC,IAAI,CAACG,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACD,mBAAmB,GAAG,IAAI,CAACgG,QAAQ,CAAChG,mBAAmB;MAC5D,IAAI,CAACE,WAAW,GAAG,IAAI,CAAC8F,QAAQ,CAAC9F,WAAW;MAC5C,IAAI,CAACC,4BAA4B,GAAG,IAAI,CAAC6F,QAAQ,CAAC7F,4BAA4B;MAC9E,IAAI,CAACC,aAAa,GAAG,IAAI,CAAC4F,QAAQ,CAAC5F,aAAa;MAChD,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC2F,QAAQ,CAAC3F,cAAc;MAClD,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC0F,QAAQ,CAAC1F,eAAe;MACpD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACyF,QAAQ,CAACzF,gBAAgB;MACtD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACwF,QAAQ,CAACxF,gBAAgB;MACtD,IAAI,CAACC,eAAe,GAAG,IAAI,CAACuF,QAAQ,CAACvF,eAAe;MACpD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACsF,QAAQ,CAACtF,kBAAkB;MAC1D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACqF,QAAQ,CAACrF,mBAAmB;MAC5D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACoF,QAAQ,CAACpF,cAAc;MAClD,IAAI,CAACC,eAAe,GAAG,IAAI,CAACmF,QAAQ,CAACnF,eAAe;MACpD,IAAI,CAACrB,YAAY,GAAG,IAAI,CAACwG,QAAQ,CAACxG,YAAY;MAC9C,IAAI,CAACsB,aAAa,GAAG,IAAI,CAACkF,QAAQ,CAAClF,aAAa;MAChD,IAAI,CAACC,YAAY,GAAG,IAAI,CAACiF,QAAQ,CAACjF,YAAY;MAC9C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACgF,QAAQ,CAAChF,QAAQ;MACtC,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC+E,QAAQ,CAAC/E,eAAe;MACpD,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAAC8E,QAAQ,CAAC9E,wBAAwB;MACtE,IAAI,CAACvB,iBAAiB,GAAG,IAAI,CAACqG,QAAQ,CAACrG,iBAAiB;MACxD,IAAI,CAAC9C,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACoC,OAAO,GAAG;QACXE,EAAE,EAAE,CAAC,GAAG;QACRD,EAAE,EAAE,CAAC,GAAG;QACRE,EAAE,EAAE,KAAK;QACTC,EAAE,EAAE;MACR,CAAC;MACD,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC0G,QAAQ,CAAC1G,UAAU;MAC1C,IAAI,CAAC1C,QAAQ,GAAG,KAAK;MACrB,IAAI,CAAC+V,MAAM,GAAG,KAAK;MACnB,IAAI,CAACC,YAAY,GAAG,IAAIhZ,YAAY,CAAC,CAAC;MACtC,IAAI,CAACiZ,cAAc,GAAG,IAAIjZ,YAAY,CAAC,CAAC;MACxC,IAAI,CAACkZ,WAAW,GAAG,IAAIlZ,YAAY,CAAC,CAAC;MACrC,IAAI,CAACmZ,YAAY,GAAG,IAAInZ,YAAY,CAAC,CAAC;MACtC,IAAI,CAACoZ,eAAe,GAAG,IAAIpZ,YAAY,CAAC,CAAC;MACzC,IAAI,CAACqZ,eAAe,GAAG,IAAIrZ,YAAY,CAAC,CAAC;MACzC,IAAI,CAACsZ,KAAK,CAAC,CAAC;IAChB;IACAC,WAAWA,CAAClR,OAAO,EAAE;MACjB,IAAI,CAACmR,uBAAuB,CAACnR,OAAO,CAAC;MACrC,IAAI,CAACoR,mBAAmB,CAACpR,OAAO,CAAC;MACjC,IAAI,IAAI,CAAC8D,WAAW,EAAEsD,QAAQ,CAAC5B,KAAK,CAAC4J,QAAQ,KAAKpP,OAAO,CAAC,0BAA0B,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC,EAAE;QACjH,IAAI,CAACmQ,gBAAgB,CAChBZ,oBAAoB,CAAC,IAAI,CAACzL,WAAW,EAAE,IAAI,CAACC,QAAQ,CAAC,CACrDsN,IAAI,CAAE/E,GAAG,IAAK,IAAI,CAACgF,cAAc,CAAChF,GAAG,CAAC,CAAC,CACvCiF,KAAK,CAAEC,GAAG,IAAK,IAAI,CAACnX,cAAc,CAACmX,GAAG,CAAC,CAAC;MACjD;MACA,IAAIxR,OAAO,CAAC,SAAS,CAAC,IAAIA,OAAO,CAAC,qBAAqB,CAAC,IAAIA,OAAO,CAAC,aAAa,CAAC,EAAE;QAChF,IAAI,CAACyR,UAAU,CAAC,CAAC;QACjB,IAAI,CAACC,uBAAuB,CAAC,CAAC;QAC9B,IAAI,CAACC,uBAAuB,CAAC,CAAC;QAC9B,IAAI,IAAI,CAAC5T,mBAAmB,KACvB,IAAI,CAACG,4BAA4B,IAAI,CAAC,IAAI,CAAC0T,oBAAoB,CAAC,CAAC,CAAC,KAClE5R,OAAO,CAAC,qBAAqB,CAAC,IAAIA,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE;UAC5D,IAAI,CAACqI,oBAAoB,CAAC,CAAC;QAC/B,CAAC,MACI,IAAIrI,OAAO,CAAC,SAAS,CAAC,EAAE;UACzB,IAAI,CAAC6R,oBAAoB,CAAC,KAAK,CAAC;UAChC,IAAI,CAACC,UAAU,CAAC,CAAC;QACrB;MACJ;MACA,IAAI9R,OAAO,CAAC,WAAW,CAAC,EAAE;QACtB,IAAI,CAAChC,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC+T,eAAe,CAAC,CAAC;QACtB,IAAI,CAACD,UAAU,CAAC,CAAC;MACrB;MACA,IAAI9R,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACyQ,kBAAkB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;QAC9DsB,UAAU,CAAC,MAAM;UACb,IAAI,CAACC,QAAQ,CAAC,CAAC;UACf,IAAI,CAACxB,kBAAkB,GAAG,KAAK;QACnC,CAAC,CAAC;MACN;IACJ;IACAU,uBAAuBA,CAACnR,OAAO,EAAE;MAC7B,IAAI,CAAC+D,QAAQ,CAAChE,qBAAqB,CAACC,OAAO,CAAC;MAC5C,IAAI,IAAI,CAAC+D,QAAQ,CAACrF,mBAAmB,IAAI,IAAI,CAACqF,QAAQ,CAACtF,kBAAkB,EAAE;QACvE,IAAI,CAACf,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACqG,QAAQ,CAACxE,UAAU,CAAC;UACrB7B,iBAAiB,EAAE,IAAI;UACvBW,eAAe,EAAE,IAAI,CAAC0F,QAAQ,CAACtF,kBAAkB;UACjDH,gBAAgB,EAAE,IAAI,CAACyF,QAAQ,CAACrF,mBAAmB;UACnDH,gBAAgB,EAAE,IAAI,CAACwF,QAAQ,CAACrF,mBAAmB;UACnDF,eAAe,EAAE,IAAI,CAACuF,QAAQ,CAACtF,kBAAkB;UACjDV,mBAAmB,EAAE;QACzB,CAAC,CAAC;MACN;IACJ;IACAqT,mBAAmBA,CAACpR,OAAO,EAAE;MACzB,IAAIA,OAAO,CAAC,mBAAmB,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAIA,OAAO,CAAC,WAAW,CAAC,EAAE;QACvG,IAAI,CAACiR,KAAK,CAAC,CAAC;MAChB;MACA,IAAIjR,OAAO,CAAC,mBAAmB,CAAC,IAAI,IAAI,CAACkS,wBAAwB,CAAC,CAAC,EAAE;QACjE,IAAI,CAAC1G,aAAa,CAAC,IAAI,CAAC2G,iBAAiB,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9D;MACA,IAAIrS,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAACsS,QAAQ,EAAE;QACtC,IAAI,CAACnG,gBAAgB,CAAC,IAAI,CAACmG,QAAQ,CAAC;MACxC;MACA,IAAItS,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC0M,WAAW,EAAE;QAC5C,IAAI,CAACD,eAAe,CAAC,IAAI,CAACC,WAAW,CAAC;MAC1C;MACA,IAAI1M,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAACuS,SAAS,EAAE;QACxC,IAAI,CAAC/G,aAAa,CAAC,IAAI,CAAC+G,SAAS,CAAC;MACtC;IACJ;IACAL,wBAAwBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAE3K,MAAM,GAAG,CAAC;IAC5D;IACAqK,eAAeA,CAAA,EAAG;MACd,MAAMnL,aAAa,GAAG,IAAI,CAAC5I,SAAS,EAAE4I,aAAa,IAAI,GAAG;MAC1D,IAAI,CAACnM,kBAAkB,GAAG,IAAI,CAAC2V,SAAS,CAACoC,wBAAwB,CAAE,aAAY,IAAI,CAACxU,SAAS,CAAC6G,UAAU,IAAI,CAAE,GAAE+B,aAAc,KAAI,IAAI,CAAC5I,SAAS,CAAC8G,UAAU,IAAI,CAAE,GAAE8B,aAAc,GAAE,GAC/K,UAAU,GAAG,CAAC,IAAI,CAAC5I,SAAS,CAACyG,KAAK,IAAI,CAAC,KAAK,IAAI,CAACzG,SAAS,CAAC0G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAChF,UAAU,GAAG,CAAC,IAAI,CAAC1G,SAAS,CAACyG,KAAK,IAAI,CAAC,KAAK,IAAI,CAACzG,SAAS,CAAC4G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAChF,UAAU,IAAI,IAAI,CAAC5G,SAAS,CAACqH,MAAM,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;IAC3D;IACAoN,QAAQA,CAAA,EAAG;MACP,IAAI,CAAC1O,QAAQ,CAACzE,QAAQ,GAAG,IAAI,CAACV,eAAe;MAC7C,IAAI,CAAC8T,oBAAoB,CAAC,CAAC;IAC/B;IACAzB,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACzW,YAAY,GAAG,KAAK;MACzB,IAAI,CAACsJ,WAAW,GAAGwJ,SAAS;MAC5B,IAAI,CAACxS,cAAc,GAAG,kCAAkC,GAClD,2DAA2D,GAC3D,2BAA2B;MACjC,IAAI,CAACgO,SAAS,GAAG;QACb6J,MAAM,EAAE,KAAK;QACb9G,IAAI,EAAE,IAAI;QACVvC,QAAQ,EAAE,IAAI;QACdpM,EAAE,EAAE,CAAC;QACLD,EAAE,EAAE,CAAC;QACLE,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLoD,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACb,CAAC;MACD,IAAI,CAACuD,OAAO,GAAG;QACXpD,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE;MACZ,CAAC;MACD,IAAI,CAAC7D,OAAO,CAACE,EAAE,GAAG,CAAC,GAAG;MACtB,IAAI,CAACF,OAAO,CAACC,EAAE,GAAG,CAAC,GAAG;MACtB,IAAI,CAACD,OAAO,CAACG,EAAE,GAAG,KAAK;MACvB,IAAI,CAACH,OAAO,CAACI,EAAE,GAAG,KAAK;IAC3B;IACAoO,aAAaA,CAACC,IAAI,EAAE;MAChB,IAAI,CAAC0E,gBAAgB,CAChB3E,aAAa,CAACC,IAAI,EAAE,IAAI,CAAC1H,QAAQ,CAAC,CAClCsN,IAAI,CAAE/E,GAAG,IAAK,IAAI,CAACgF,cAAc,CAAChF,GAAG,CAAC,CAAC,CACvCiF,KAAK,CAAEC,GAAG,IAAK,IAAI,CAACnX,cAAc,CAACmX,GAAG,CAAC,CAAC;IACjD;IACA/E,eAAeA,CAACC,WAAW,EAAE;MACzB,IAAI,CAACyD,gBAAgB,CAChB1D,eAAe,CAACC,WAAW,EAAE,IAAI,CAAC3I,QAAQ,CAAC,CAC3CsN,IAAI,CAAE/E,GAAG,IAAK,IAAI,CAACgF,cAAc,CAAChF,GAAG,CAAC,CAAC,CACvCiF,KAAK,CAAEC,GAAG,IAAK,IAAI,CAACnX,cAAc,CAACmX,GAAG,CAAC,CAAC;IACjD;IACArF,gBAAgBA,CAACC,GAAG,EAAE;MAClB,IAAI,CAAC+D,gBAAgB,CAChBhE,gBAAgB,CAACC,GAAG,EAAE,IAAI,CAACrI,QAAQ,CAAC,CACpCsN,IAAI,CAAE/E,GAAG,IAAK,IAAI,CAACgF,cAAc,CAAChF,GAAG,CAAC,CAAC,CACvCiF,KAAK,CAAEC,GAAG,IAAK,IAAI,CAACnX,cAAc,CAACmX,GAAG,CAAC,CAAC;IACjD;IACAF,cAAcA,CAACxN,WAAW,EAAE;MACxB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAAChJ,cAAc,GAAG,IAAI,CAACsV,SAAS,CAACwC,8BAA8B,CAAC9O,WAAW,CAACmB,WAAW,CAACwB,SAAS,CAAC;MACtG,IAAI,CAAC4J,EAAE,CAACwC,YAAY,CAAC,CAAC;IAC1B;IACAxY,cAAcA,CAACyY,KAAK,EAAE;MAClBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;MACpB,IAAI,CAAC/B,eAAe,CAACiC,IAAI,CAAC,CAAC;IAC/B;IACAnZ,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACiK,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAI,CAAC+M,WAAW,CAACmC,IAAI,CAAC,IAAI,CAAClP,WAAW,CAAC;QACvC,IAAI,CAAC0M,sBAAsB,GAAG,CAAC;QAC/BwB,UAAU,CAAC,MAAM,IAAI,CAACiB,4BAA4B,CAAC,CAAC,CAAC;MACzD;IACJ;IACAA,4BAA4BA,CAAA,EAAG;MAC3B,IAAI,IAAI,CAACzC,sBAAsB,GAAG,EAAE,EAAE;QAClC,IAAI,CAACO,eAAe,CAACiC,IAAI,CAAC,CAAC;MAC/B,CAAC,MACI,IAAI,IAAI,CAACE,iBAAiB,CAAC,CAAC,EAAE;QAC/B,IAAI,CAACzB,UAAU,CAAC,CAAC;QACjB,IAAI,CAACC,uBAAuB,CAAC,CAAC;QAC9B,IAAI,CAACC,uBAAuB,CAAC,CAAC;QAC9B,IAAI,CAACtJ,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACyI,YAAY,CAACkC,IAAI,CAAC;UAAE,GAAG,IAAI,CAAChP;QAAQ,CAAC,CAAC;QAC3C,IAAI,CAACqM,EAAE,CAACwC,YAAY,CAAC,CAAC;MAC1B,CAAC,MACI;QACD,IAAI,CAACrC,sBAAsB,EAAE;QAC7BwB,UAAU,CAAC,MAAM,IAAI,CAACiB,4BAA4B,CAAC,CAAC,EAAE,EAAE,CAAC;MAC7D;IACJ;IACAC,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAI,CAAC5K,WAAW,EAAEC,aAAa,EAAE4K,WAAW,GAAG,CAAC;IAC3D;IACAlB,QAAQA,CAAA,EAAG;MACP,IAAI,CAAC,IAAI,CAACnO,WAAW,EAAE;QACnB;MACJ;MACA,IAAI,IAAI,CAAC4M,MAAM,EAAE;QACb,IAAI,CAACD,kBAAkB,GAAG,IAAI;MAClC,CAAC,MACI;QACD,MAAM2C,UAAU,GAAG;UAAE,GAAG,IAAI,CAACpP;QAAQ,CAAC;QACtC,IAAI,CAACyN,UAAU,CAAC,CAAC;QACjB,IAAI,CAAC4B,qBAAqB,CAACD,UAAU,CAAC;QACtC,IAAI,CAAC1B,uBAAuB,CAAC,CAAC;QAC9B,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAClC;IACJ;IACMe,oBAAoBA,CAAA,EAAG;MAAA,IAAAY,MAAA;MAAA,OAAAlN,iBAAA;QACzB;QACA,MAAMkN,MAAI,CAAC/C,YAAY,GAAG,CAAC;QAC3B,MAAMgD,MAAM,GAAGC,MAAM,GAAG,QAAQ,CAAC,IAAI,IAAI;QACzC,IAAID,MAAM,EAAE;UACR,MAAME,MAAM,GAAG,IAAIF,MAAM,CAACD,MAAI,CAACI,OAAO,CAACnL,aAAa,CAAC;UACrDkL,MAAM,CAACE,GAAG,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC,CAAC;UACzCJ,MAAM,CAACK,EAAE,CAAC,WAAW,EAAER,MAAI,CAACS,OAAO,CAACC,IAAI,CAACV,MAAI,CAAC,CAAC;UAC/CG,MAAM,CAACK,EAAE,CAAC,UAAU,EAAER,MAAI,CAACW,SAAS,CAACD,IAAI,CAACV,MAAI,CAAC,CAAC;UAChDG,MAAM,CAACK,EAAE,CAAC,YAAY,EAAER,MAAI,CAACY,UAAU,CAACF,IAAI,CAACV,MAAI,CAAC,CAAC;QACvD,CAAC,MACI,IAAI1b,SAAS,CAAC,CAAC,EAAE;UAClBmb,OAAO,CAACoB,IAAI,CAAC,uEAAuE,CAAC;QACzF;MAAC;IACL;IACAd,qBAAqBA,CAACD,UAAU,EAAE;MAC9B,IAAIA,UAAU,CAACxS,KAAK,KAAK,IAAI,CAACoD,OAAO,CAACpD,KAAK,IAAIwS,UAAU,CAACvS,MAAM,KAAK,IAAI,CAACmD,OAAO,CAACnD,MAAM,EAAE;QACtF,IAAI,CAAC7D,OAAO,CAACE,EAAE,GAAG,IAAI,CAACF,OAAO,CAACE,EAAE,GAAG,IAAI,CAAC8G,OAAO,CAACpD,KAAK,GAAGwS,UAAU,CAACxS,KAAK;QACzE,IAAI,CAAC5D,OAAO,CAACG,EAAE,GAAG,IAAI,CAACH,OAAO,CAACG,EAAE,GAAG,IAAI,CAAC6G,OAAO,CAACpD,KAAK,GAAGwS,UAAU,CAACxS,KAAK;QACzE,IAAI,CAAC5D,OAAO,CAACC,EAAE,GAAG,IAAI,CAACD,OAAO,CAACC,EAAE,GAAG,IAAI,CAAC+G,OAAO,CAACnD,MAAM,GAAGuS,UAAU,CAACvS,MAAM;QAC3E,IAAI,CAAC7D,OAAO,CAACI,EAAE,GAAG,IAAI,CAACJ,OAAO,CAACI,EAAE,GAAG,IAAI,CAAC4G,OAAO,CAACnD,MAAM,GAAGuS,UAAU,CAACvS,MAAM;MAC/E;IACJ;IACAwH,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAAC6H,sBAAsB,CAAC7H,oBAAoB,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACtL,OAAO,EAAE,IAAI,CAAC+G,QAAQ,EAAE,IAAI,CAACC,OAAO,CAAC;MAC7G,IAAI,CAAC8N,UAAU,CAAC,CAAC;MACjB,IAAI,CAACtX,YAAY,GAAG,IAAI;IAC5B;IACAmC,cAAcA,CAACkM,KAAK,EAAE;MAClB,IAAI,CAACuL,sBAAsB,CAACvL,KAAK,CAAC;MAClC,IAAI,CAACwL,mBAAmB,CAACxL,KAAK,CAAC;IACnC;IACAuL,sBAAsBA,CAACvL,KAAK,EAAE;MAC1B,MAAMxI,GAAG,GAAG,CAACwI,KAAK,CAACxI,GAAG;MACtB,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC0D,QAAQ,CAACzE,QAAQ,GAAGe,GAAG;MAChC;IACJ;IACAgU,mBAAmBA,CAACxL,KAAK,EAAE;MACvB,MAAMyL,iBAAiB,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;MAC7E,IAAI,CAAEA,iBAAiB,CAACC,QAAQ,CAAC1L,KAAK,CAACxI,GAAG,CAAE,EAAE;QAC1C;MACJ;MACA,MAAMmU,QAAQ,GAAG3L,KAAK,CAAC4L,QAAQ,GAAGtU,SAAS,CAAC7E,MAAM,GAAG6E,SAAS,CAACtD,IAAI;MACnE,MAAMyM,QAAQ,GAAGT,KAAK,CAAC6L,MAAM,GAAGpU,yBAAyB,CAACuI,KAAK,CAACxI,GAAG,CAAC,GAAGD,iBAAiB,CAACyI,KAAK,CAACxI,GAAG,CAAC;MACnG,MAAMsU,SAAS,GAAGpU,cAAc,CAACsI,KAAK,CAACxI,GAAG,EAAE,IAAI,CAAC0D,QAAQ,CAACzE,QAAQ,CAAC;MACnEuJ,KAAK,CAAC+L,cAAc,CAAC,CAAC;MACtB/L,KAAK,CAACgM,eAAe,CAAC,CAAC;MACvB,IAAI,CAAC7a,SAAS,CAAC;QAAEwG,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAC,EAAE+T,QAAQ,EAAElL,QAAQ,CAAC;MAC9D,IAAI,CAACwL,eAAe,CAACH,SAAS,CAAC;MAC/B,IAAI,CAACI,aAAa,CAAC,CAAC;IACxB;IACA/a,SAASA,CAAC6O,KAAK,EAAE2L,QAAQ,EAAElL,QAAQ,GAAG,IAAI,EAAE;MACxC,IAAI,IAAI,CAAC3O,QAAQ,IACV,IAAI,CAACmO,SAAS,EAAE6J,MAAM,IAAI,IAAI,CAAC7J,SAAS,EAAE+C,IAAI,KAAK1L,SAAS,CAAC6U,KAAK,IAClER,QAAQ,KAAKrU,SAAS,CAACjG,IAAI,IAAI,CAAC,IAAI,CAACU,cAAc,EAAE;QACxD;MACJ;MACA,IAAIiO,KAAK,CAAC+L,cAAc,EAAE;QACtB/L,KAAK,CAAC+L,cAAc,CAAC,CAAC;MAC1B;MACA,IAAI,CAAC9L,SAAS,GAAG;QACb6J,MAAM,EAAE,IAAI;QACZ9G,IAAI,EAAE2I,QAAQ;QACdlL,QAAQ;QACRtL,SAAS,EAAE;UAAE,GAAG,IAAI,CAACA;QAAU,CAAC;QAChCwC,OAAO,EAAE,IAAI,CAAC0P,sBAAsB,CAAClH,UAAU,CAACH,KAAK,CAAC;QACtDpI,OAAO,EAAE,IAAI,CAACyP,sBAAsB,CAAChH,UAAU,CAACL,KAAK,CAAC;QACtD,GAAG,IAAI,CAAC7L;MACZ,CAAC;MACD,IAAI,CAACiY,aAAa,CAAC,CAAC;IACxB;IACAA,aAAaA,CAAA,EAAG;MACZtc,KAAK,CAACC,SAAS,CAACwL,QAAQ,EAAE,WAAW,CAAC,EAAExL,SAAS,CAACwL,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC8Q,IAAI,CAACzc,SAAS,CAACE,KAAK,CAACC,SAAS,CAACwL,QAAQ,EAAE,SAAS,CAAC,EAAExL,SAAS,CAACwL,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC8Q,IAAI,CAACxc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1Kyc,SAAS,CAAC;QACXC,IAAI,EAAGvM,KAAK,IAAK,IAAI,CAACyH,IAAI,CAAC+E,GAAG,CAAC,MAAM;UACjC,IAAI,CAACP,eAAe,CAACjM,KAAK,CAAC;UAC3B,IAAI,CAACwH,EAAE,CAACwC,YAAY,CAAC,CAAC;QAC1B,CAAC,CAAC;QACFzD,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACkB,IAAI,CAAC+E,GAAG,CAAC,MAAM;UAChC,IAAI,CAACN,aAAa,CAAC,CAAC;UACpB,IAAI,CAAC1E,EAAE,CAACwC,YAAY,CAAC,CAAC;QAC1B,CAAC;MACL,CAAC,CAAC;IACN;IACAqB,UAAUA,CAACrL,KAAK,EAAE;MACd,IAAI,CAAC,IAAI,CAAC/N,cAAc,EAAE;QACtB;MACJ;MACA,IAAI+N,KAAK,CAAC+L,cAAc,EAAE;QACtB/L,KAAK,CAAC+L,cAAc,CAAC,CAAC;MAC1B;MACA,IAAI,CAAC9L,SAAS,GAAG;QACb6J,MAAM,EAAE,IAAI;QACZ9G,IAAI,EAAE1L,SAAS,CAAC6U,KAAK;QACrB1L,QAAQ,EAAE,QAAQ;QAClB9I,OAAO,EAAE,IAAI,CAACxD,OAAO,CAACE,EAAE,GAAG,CAAC,IAAI,CAACF,OAAO,CAACG,EAAE,GAAG,IAAI,CAACH,OAAO,CAACE,EAAE,IAAI,CAAC;QAClEuD,OAAO,EAAE,IAAI,CAACzD,OAAO,CAACC,EAAE,GAAG,CAAC,IAAI,CAACD,OAAO,CAACI,EAAE,GAAG,IAAI,CAACJ,OAAO,CAACC,EAAE,IAAI,CAAC;QAClE,GAAG,IAAI,CAACD;MACZ,CAAC;IACL;IACA8X,eAAeA,CAACjM,KAAK,EAAE;MACnB,IAAI,IAAI,CAACC,SAAS,CAAC6J,MAAM,EAAE;QACvB,IAAI9J,KAAK,CAACgM,eAAe,EAAE;UACvBhM,KAAK,CAACgM,eAAe,CAAC,CAAC;QAC3B;QACA,IAAIhM,KAAK,CAAC+L,cAAc,EAAE;UACtB/L,KAAK,CAAC+L,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,IAAI,CAAC9L,SAAS,CAAC+C,IAAI,KAAK1L,SAAS,CAACtD,IAAI,EAAE;UACxC,IAAI,CAACqT,sBAAsB,CAACtH,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC9L,OAAO,CAAC;UACrE,IAAI,CAAC6U,oBAAoB,CAAC,IAAI,CAAC;QACnC,CAAC,MACI,IAAI,IAAI,CAAC/I,SAAS,CAAC+C,IAAI,KAAK1L,SAAS,CAAC7E,MAAM,EAAE;UAC/C,IAAI,CAAC,IAAI,CAACmD,kBAAkB,IAAI,CAAC,IAAI,CAACC,mBAAmB,EAAE;YACvD,IAAI,CAACwR,sBAAsB,CAAC/G,MAAM,CAACN,KAAK,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC9L,OAAO,EAAE,IAAI,CAACgH,OAAO,EAAE,IAAI,CAACD,QAAQ,CAAC;UACxG;UACA,IAAI,CAAC8N,oBAAoB,CAAC,KAAK,CAAC;QACpC,CAAC,MACI,IAAI,IAAI,CAAC/I,SAAS,CAAC+C,IAAI,KAAK1L,SAAS,CAACjG,IAAI,EAAE;UAC7C,MAAM6O,KAAK,GAAG,IAAI,CAACmH,sBAAsB,CAAClH,UAAU,CAACH,KAAK,CAAC,GAAG,IAAI,CAACC,SAAS,CAACtI,OAAO;UACpF,MAAMyI,KAAK,GAAG,IAAI,CAACiH,sBAAsB,CAAChH,UAAU,CAACL,KAAK,CAAC,GAAG,IAAI,CAACC,SAAS,CAACrI,OAAO;UACpF,IAAI,CAACzC,SAAS,GAAG;YACb,GAAG,IAAI,CAACA,SAAS;YACjB6G,UAAU,EAAE,CAAC,IAAI,CAACiE,SAAS,CAAC9K,SAAS,EAAE6G,UAAU,IAAI,CAAC,IAAIkE,KAAK;YAC/DjE,UAAU,EAAE,CAAC,IAAI,CAACgE,SAAS,CAAC9K,SAAS,EAAE8G,UAAU,IAAI,CAAC,IAAImE;UAC9D,CAAC;UACD,IAAI,CAAC8I,eAAe,CAAC,CAAC;QAC1B;MACJ;IACJ;IACAgC,OAAOA,CAAClL,KAAK,EAAE;MACX,IAAI,IAAI,CAACC,SAAS,CAAC6J,MAAM,EAAE;QACvB,IAAI9J,KAAK,CAACgM,eAAe,EAAE;UACvBhM,KAAK,CAACgM,eAAe,CAAC,CAAC;QAC3B;QACA,IAAIhM,KAAK,CAAC+L,cAAc,EAAE;UACtB/L,KAAK,CAAC+L,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,IAAI,CAAC9L,SAAS,CAAC+C,IAAI,KAAK1L,SAAS,CAAC6U,KAAK,EAAE;UACzC,IAAI,CAAC9E,sBAAsB,CAAC/G,MAAM,CAACN,KAAK,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC9L,OAAO,EAAE,IAAI,CAACgH,OAAO,EAAE,IAAI,CAACD,QAAQ,CAAC;UACpG,IAAI,CAAC8N,oBAAoB,CAAC,KAAK,CAAC;QACpC;QACA,IAAI,CAACxB,EAAE,CAACwC,YAAY,CAAC,CAAC;MAC1B;IACJ;IACApB,UAAUA,CAAA,EAAG;MACT,IAAI,IAAI,CAACnJ,WAAW,EAAE;QAClB,MAAMgN,gBAAgB,GAAGC,gBAAgB,CAAC,IAAI,CAACjN,WAAW,CAACC,aAAa,CAAC;QACzE,IAAI,CAACvE,OAAO,CAACpD,KAAK,GAAG4U,UAAU,CAACF,gBAAgB,CAAC1U,KAAK,CAAC;QACvD,IAAI,CAACoD,OAAO,CAACnD,MAAM,GAAG2U,UAAU,CAACF,gBAAgB,CAACzU,MAAM,CAAC;QACzD,IAAI,CAACvD,UAAU,GAAG,IAAI,CAAC8S,SAAS,CAACoC,wBAAwB,CAAC,aAAa,GAAG,IAAI,CAACxO,OAAO,CAACpD,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;MAC7G;IACJ;IACA8Q,uBAAuBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAAC5N,WAAW,EAAEmB,WAAW,EAAEO,KAAK,EAAE;QACtC,IAAI,CAACiQ,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACC,yBAAyB,CAAC,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAAC3R,QAAQ,CAAC7E,qBAAqB,GAAG,EAAE;QACxC,IAAI,CAAC6E,QAAQ,CAAC5E,sBAAsB,GAAG,EAAE;MAC7C;IACJ;IACAsW,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC1R,QAAQ,CAAC7E,qBAAqB,GAAG,IAAI,CAACb,eAAe,GAAG,CAAC,GACxD2C,IAAI,CAACgG,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC3I,eAAe,GAAG,IAAI,CAACyF,WAAW,CAACmB,WAAW,CAACO,KAAK,CAAC5E,KAAK,GAAG,IAAI,CAACoD,OAAO,CAACpD,KAAK,CAAC,GAClG,EAAE;IACZ;IACA8U,yBAAyBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAAC3X,mBAAmB,EAAE;QAC1B,IAAI,CAACgG,QAAQ,CAAC5E,sBAAsB,GAAG6B,IAAI,CAACgG,GAAG,CAAC,EAAE,EAAE,IAAI,CAACjD,QAAQ,CAAC7E,qBAAqB,GAAG,IAAI,CAACjB,WAAW,CAAC;MAC/G,CAAC,MACI,IAAI,IAAI,CAACK,gBAAgB,GAAG,CAAC,EAAE;QAChC,IAAI,CAACyF,QAAQ,CAAC5E,sBAAsB,GAAG6B,IAAI,CAACgG,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC1I,gBAAgB,GAAG,IAAI,CAACwF,WAAW,CAACmB,WAAW,CAACO,KAAK,CAAC3E,MAAM,GAAG,IAAI,CAACmD,OAAO,CAACnD,MAAM,CAAC;MAChJ,CAAC,MACI;QACD,IAAI,CAACkD,QAAQ,CAAC5E,sBAAsB,GAAG,EAAE;MAC7C;IACJ;IACAwS,uBAAuBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAAC7N,WAAW,EAAEmB,WAAW,EAAEO,KAAK,EAAE;QACtC,MAAMqB,KAAK,GAAG,IAAI,CAAC/C,WAAW,CAACmB,WAAW,CAACE,IAAI,CAACvE,KAAK,GAAG,IAAI,CAACoD,OAAO,CAACpD,KAAK;QAC1E,IAAI,CAACmD,QAAQ,CAAC3E,qBAAqB,GAAG,IAAI,CAACZ,eAAe,GAAG,EAAE,GAAG,IAAI,CAACA,eAAe,GAAGqI,KAAK,GAAG,IAAI,CAAC7C,OAAO,CAACpD,KAAK;QACnH,IAAI,CAACmD,QAAQ,CAAC1E,sBAAsB,GAAG,IAAI,CAACd,gBAAgB,GAAG,EAAE,GAAG,IAAI,CAACA,gBAAgB,GAAGsI,KAAK,GAAG,IAAI,CAAC7C,OAAO,CAACnD,MAAM;QACvH,IAAI,IAAI,CAAC9C,mBAAmB,EAAE;UAC1B,IAAI,IAAI,CAACgG,QAAQ,CAAC3E,qBAAqB,GAAG,IAAI,CAAC2E,QAAQ,CAAC1E,sBAAsB,GAAG,IAAI,CAACpB,WAAW,EAAE;YAC/F,IAAI,CAAC8F,QAAQ,CAAC3E,qBAAqB,GAAG,IAAI,CAAC2E,QAAQ,CAAC1E,sBAAsB,GAAG,IAAI,CAACpB,WAAW;UACjG,CAAC,MACI,IAAI,IAAI,CAAC8F,QAAQ,CAAC3E,qBAAqB,GAAG,IAAI,CAAC2E,QAAQ,CAAC1E,sBAAsB,GAAG,IAAI,CAACpB,WAAW,EAAE;YACpG,IAAI,CAAC8F,QAAQ,CAAC1E,sBAAsB,GAAG,IAAI,CAAC0E,QAAQ,CAAC3E,qBAAqB,GAAG,IAAI,CAACnB,WAAW;UACjG;QACJ;MACJ,CAAC,MACI;QACD,IAAI,CAAC8F,QAAQ,CAAC3E,qBAAqB,GAAG,IAAI,CAAC4E,OAAO,CAACpD,KAAK;QACxD,IAAI,CAACmD,QAAQ,CAAC1E,sBAAsB,GAAG,IAAI,CAAC2E,OAAO,CAACnD,MAAM;MAC9D;IACJ;IACAgR,oBAAoBA,CAAC8D,YAAY,GAAG,KAAK,EAAE;MACvC,IAAI,IAAI,CAAC3Y,OAAO,CAACE,EAAE,GAAG,CAAC,EAAE;QACrB,IAAI,CAACF,OAAO,CAACG,EAAE,IAAIwY,YAAY,GAAG,IAAI,CAAC3Y,OAAO,CAACE,EAAE,GAAG,CAAC;QACrD,IAAI,CAACF,OAAO,CAACE,EAAE,GAAG,CAAC;MACvB;MACA,IAAI,IAAI,CAACF,OAAO,CAACC,EAAE,GAAG,CAAC,EAAE;QACrB,IAAI,CAACD,OAAO,CAACI,EAAE,IAAIuY,YAAY,GAAG,IAAI,CAAC3Y,OAAO,CAACC,EAAE,GAAG,CAAC;QACrD,IAAI,CAACD,OAAO,CAACC,EAAE,GAAG,CAAC;MACvB;MACA,IAAI,IAAI,CAACD,OAAO,CAACG,EAAE,GAAG,IAAI,CAAC6G,OAAO,CAACpD,KAAK,EAAE;QACtC,IAAI,CAAC5D,OAAO,CAACE,EAAE,IAAIyY,YAAY,GAAI,IAAI,CAAC3Y,OAAO,CAACG,EAAE,GAAG,IAAI,CAAC6G,OAAO,CAACpD,KAAK,GAAI,CAAC;QAC5E,IAAI,CAAC5D,OAAO,CAACG,EAAE,GAAG,IAAI,CAAC6G,OAAO,CAACpD,KAAK;MACxC;MACA,IAAI,IAAI,CAAC5D,OAAO,CAACI,EAAE,GAAG,IAAI,CAAC4G,OAAO,CAACnD,MAAM,EAAE;QACvC,IAAI,CAAC7D,OAAO,CAACC,EAAE,IAAI0Y,YAAY,GAAI,IAAI,CAAC3Y,OAAO,CAACI,EAAE,GAAG,IAAI,CAAC4G,OAAO,CAACnD,MAAM,GAAI,CAAC;QAC7E,IAAI,CAAC7D,OAAO,CAACI,EAAE,GAAG,IAAI,CAAC4G,OAAO,CAACnD,MAAM;MACzC;IACJ;IACAkU,aAAaA,CAAA,EAAG;MACZ,IAAI,IAAI,CAACjM,SAAS,CAAC6J,MAAM,EAAE;QACvB,IAAI,CAAC7J,SAAS,CAAC6J,MAAM,GAAG,KAAK;QAC7B,IAAI,IAAI,CAAC7J,SAAS,EAAE+C,IAAI,KAAK1L,SAAS,CAACjG,IAAI,EAAE;UACzC,IAAI,CAAC8W,eAAe,CAACgC,IAAI,CAAC,IAAI,CAAChV,SAAS,CAAC;QAC7C,CAAC,MACI;UACD,IAAI,CAAC8T,UAAU,CAAC,CAAC;QACrB;MACJ;IACJ;IACAmC,SAASA,CAAA,EAAG;MACR,IAAI,IAAI,CAACnL,SAAS,CAAC6J,MAAM,EAAE;QACvB,IAAI,CAAC7J,SAAS,CAAC6J,MAAM,GAAG,KAAK;QAC7B,IAAI,CAACb,UAAU,CAAC,CAAC;MACrB;IACJ;IACAA,UAAUA,CAAA,EAAG;MACT,IAAI,IAAI,CAAC/S,QAAQ,EAAE;QACf,KAAK,IAAI,CAAC8E,IAAI,CAAC,CAAC;MACpB;IACJ;IACAA,IAAIA,CAAC/F,MAAM,GAAG,IAAI,CAACiG,QAAQ,CAACjG,MAAM,EAAE;MAChC,IAAI,IAAI,CAACgG,WAAW,EAAEmB,WAAW,EAAEO,KAAK,IAAI,IAAI,EAAE;QAC9C,IAAI,CAACoL,cAAc,CAACoC,IAAI,CAAC,CAAC;QAC1B,IAAIlV,MAAM,KAAK,MAAM,EAAE;UACnB,OAAO,IAAI,CAACiI,UAAU,CAAC,CAAC;QAC5B,CAAC,MACI,IAAIjI,MAAM,KAAK,QAAQ,EAAE;UAC1B,OAAO,IAAI,CAAC8X,YAAY,CAAC,CAAC;QAC9B;MACJ;MACA,OAAO,IAAI;IACf;IACA7P,UAAUA,CAAA,EAAG;MAAA,IAAA8P,MAAA;MACT,OAAO,IAAIvP,OAAO,CAAC,CAACC,OAAO,EAAEyF,MAAM,KAAK,IAAI,CAACsE,IAAI,CAAC+E,GAAG,eAAAjP,iBAAA,CAAC,aAAY;QAC9D,MAAMX,MAAM,SAASoQ,MAAI,CAAC5F,WAAW,CAACpM,IAAI,CAACgS,MAAI,CAAC/R,WAAW,EAAE+R,MAAI,CAAC7Y,OAAO,EAAE6Y,MAAI,CAAC9R,QAAQ,EAAE,MAAM,EAAE8R,MAAI,CAAC7R,OAAO,CAAC;QAC/G,IAAIyB,MAAM,EAAE;UACRoQ,MAAI,CAAClF,YAAY,CAACqC,IAAI,CAACvN,MAAM,CAAC;UAC9Bc,OAAO,CAACd,MAAM,CAAC;QACnB,CAAC,MACI;UACDuG,MAAM,CAAC,mBAAmB,CAAC;QAC/B;MACJ,CAAC,EAAC,CAAC;IACP;IACA4J,YAAYA,CAAA,EAAG;MACX,MAAMnQ,MAAM,GAAG,IAAI,CAACwK,WAAW,CAACpM,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAAC9G,OAAO,EAAE,IAAI,CAAC+G,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAACC,OAAO,CAAC;MAC3G,IAAIyB,MAAM,EAAE;QACR,IAAI,CAACkL,YAAY,CAACqC,IAAI,CAACvN,MAAM,CAAC;QAC9B,OAAOA,MAAM;MACjB;MACA,OAAO,IAAI;IACf;IACAmM,oBAAoBA,CAAA,EAAG;MACnB,MAAMkE,sBAAsB,GAAG,CAAC,IAAI,CAAC9Y,OAAO,CAACG,EAAE,GAAG,IAAI,CAACH,OAAO,CAACE,EAAE,KAAK,IAAI,CAACF,OAAO,CAACI,EAAE,GAAG,IAAI,CAACJ,OAAO,CAACC,EAAE,CAAC;MACxG,OAAO6Y,sBAAsB,KAAK,IAAI,CAAC7X,WAAW;IACtD;EACJ;EACA+R,qBAAqB,CAACrI,IAAI,YAAAoO,8BAAAlO,CAAA;IAAA,YAAAA,CAAA,IAAyFmI,qBAAqB,EAl+BzCvY,EAAE,CAAAue,iBAAA,CAk+ByDpS,WAAW,GAl+BtEnM,EAAE,CAAAue,iBAAA,CAk+BiF5N,sBAAsB,GAl+BzG3Q,EAAE,CAAAue,iBAAA,CAk+BoH1K,gBAAgB,GAl+BtI7T,EAAE,CAAAue,iBAAA,CAk+BiJzd,EAAE,CAAC0d,YAAY,GAl+BlKxe,EAAE,CAAAue,iBAAA,CAk+B6Kve,EAAE,CAACye,iBAAiB,GAl+BnMze,EAAE,CAAAue,iBAAA,CAk+B8Mve,EAAE,CAAC0e,MAAM,GAl+BzN1e,EAAE,CAAAue,iBAAA,CAk+BoOxd,aAAa;EAAA,CAA4D;EAC9YwX,qBAAqB,CAACoG,IAAI,kBAn+BqE3e,EAAE,CAAA4e,iBAAA;IAAAxK,IAAA,EAm+BMmE,qBAAqB;IAAAsG,SAAA;IAAAC,SAAA,WAAAC,4BAAAtd,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAn+B7BzB,EAAE,CAAAgf,WAAA,CAAA1d,GAAA;QAAFtB,EAAE,CAAAgf,WAAA,CAAAzd,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAAwd,EAAA;QAAFjf,EAAE,CAAAkf,cAAA,CAAAD,EAAA,GAAFjf,EAAE,CAAAmf,WAAA,QAAAzd,GAAA,CAAAua,OAAA,GAAAgD,EAAA,CAAAhe,KAAA;QAAFjB,EAAE,CAAAkf,cAAA,CAAAD,EAAA,GAAFjf,EAAE,CAAAmf,WAAA,QAAAzd,GAAA,CAAAmP,WAAA,GAAAoO,EAAA,CAAAhe,KAAA;MAAA;IAAA;IAAAme,QAAA;IAAAC,YAAA,WAAAC,mCAAA7d,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFzB,EAAE,CAAA8B,UAAA,oBAAAyd,gDAAA;UAAA,OAm+BM7d,GAAA,CAAA8Y,QAAA,CAAS,CAAC;QAAA,UAn+BlBxa,EAAE,CAAAwf,eAm+B0B,CAAC;MAAA;MAAA,IAAA/d,EAAA;QAn+B7BzB,EAAE,CAAA8C,WAAA,eAAApB,GAAA,CAAAkE,UAm+B0B,CAAC;QAn+B7B5F,EAAE,CAAAiD,WAAA,aAAAvB,GAAA,CAAAwB,QAm+B0B,CAAC,kBAAAxB,GAAA,CAAAuX,MAAD,CAAC;MAAA;IAAA;IAAAwG,MAAA;MAAA/E,iBAAA;MAAAG,QAAA;MAAA5F,WAAA;MAAA6F,SAAA;MAAAtX,YAAA;MAAAuC,qBAAA;MAAAM,MAAA;MAAAD,MAAA;MAAAG,SAAA;MAAAD,mBAAA;MAAAE,WAAA;MAAAC,4BAAA;MAAAC,aAAA;MAAAC,cAAA;MAAAC,eAAA;MAAAC,gBAAA;MAAAC,gBAAA;MAAAC,eAAA;MAAAC,kBAAA;MAAAC,mBAAA;MAAAC,cAAA;MAAAC,eAAA;MAAArB,YAAA;MAAAsB,aAAA;MAAAC,YAAA;MAAAC,QAAA;MAAAC,eAAA;MAAAC,wBAAA;MAAAvB,iBAAA;MAAA9C,cAAA;MAAAoC,OAAA;MAAAK,UAAA;MAAA1C,QAAA;MAAA+V,MAAA;IAAA;IAAAyG,OAAA;MAAAxG,YAAA;MAAAC,cAAA;MAAAC,WAAA;MAAAC,YAAA;MAAAC,eAAA;MAAAC,eAAA;IAAA;IAAAoG,QAAA,GAn+B7B3f,EAAE,CAAA4f,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAxe,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFzB,EAAE,CAAA6B,cAAA,kBAm+Bo3D,CAAC;QAn+Bv3D7B,EAAE,CAAAsF,UAAA,IAAA9D,oCAAA,gBAm+Bq5E,CAAC;QAn+Bx5ExB,EAAE,CAAA+D,SAAA,YAm+BimF,CAAC;QAn+BpmF/D,EAAE,CAAAsF,UAAA,IAAAP,oCAAA,iBAm+BmpG,CAAC;QAn+BtpG/E,EAAE,CAAA6C,YAAA,CAm+ButM,CAAC;MAAA;MAAA,IAAApB,EAAA;QAn+B1tMzB,EAAE,CAAA8C,WAAA,eAAApB,GAAA,CAAAqB,YAAA,IAAArB,GAAA,CAAA6F,eAm+Bq2D,CAAC;QAn+Bx2DvH,EAAE,CAAAgG,SAAA,EAm+Bw+D,CAAC;QAn+B3+DhG,EAAE,CAAAoD,UAAA,SAAA1B,GAAA,CAAA2B,cAm+Bw+D,CAAC;QAn+B3+DrD,EAAE,CAAAgG,SAAA,CAm+Bm+E,CAAC;QAn+Bt+EhG,EAAE,CAAA8C,WAAA,UAAApB,GAAA,CAAA6K,OAAA,CAAApD,KAAA,MAm+Bm+E,CAAC,WAAAzH,GAAA,CAAA6K,OAAA,CAAAnD,MAAA,MAAyC,CAAC,gBAAA1H,GAAA,CAAAkE,UAAA,gBAAAlE,GAAA,CAAAmE,UAAA,OAAwE,CAAC;QAn+BzlF7F,EAAE,CAAAgG,SAAA,CAm+B+pF,CAAC;QAn+BlqFhG,EAAE,CAAAoD,UAAA,SAAA1B,GAAA,CAAAqB,YAm+B+pF,CAAC;MAAA;IAAA;IAAAmd,YAAA,GAAyiO9e,EAAE,CAAC+e,IAAI;IAAAC,MAAA;IAAAC,eAAA;EAAA,EAAwH;EAAC,OAjgBp6T9H,qBAAqB;AAAA;AAkgB3B;EAAA,QAAA7H,SAAA,oBAAAA,SAAA;AAAA;AA0GoB,IAEd4P,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;EAEzBA,kBAAkB,CAACpQ,IAAI,YAAAqQ,2BAAAnQ,CAAA;IAAA,YAAAA,CAAA,IAAyFkQ,kBAAkB;EAAA,CAAkD;EACpLA,kBAAkB,CAACE,IAAI,kBAnlCwExgB,EAAE,CAAAygB,gBAAA;IAAArM,IAAA,EAmlCgBkM;EAAkB,EAAqG;EACxOA,kBAAkB,CAACI,IAAI,kBAplCwE1gB,EAAE,CAAA2gB,gBAAA;IAAAC,OAAA,GAolC8C,CACnIvf,YAAY,CACf;EAAA,EAAI;EAAC,OANRif,kBAAkB;AAAA;AAOxB;EAAA,QAAA5P,SAAA,oBAAAA,SAAA;AAAA;AAeA,SAASmQ,YAAYA,CAACC,WAAW,EAAE;EAC/B,MAAMvJ,KAAK,GAAGuJ,WAAW,CAACvJ,KAAK,CAAC,GAAG,CAAC;EACpC,MAAMnD,IAAI,GAAGmD,KAAK,CAAC,CAAC,CAAC,CAACpC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EACjE,MAAM4L,UAAU,GAAG1L,IAAI,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC;EACjC,MAAMyJ,EAAE,GAAG,IAAIC,WAAW,CAACF,UAAU,CAAC9Q,MAAM,CAAC;EAC7C,MAAMiR,EAAE,GAAG,IAAI1L,UAAU,CAACwL,EAAE,CAAC;EAC7B,KAAK,IAAI1W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyW,UAAU,CAAC9Q,MAAM,EAAE3F,CAAC,IAAI,CAAC,EAAE;IAC3C4W,EAAE,CAAC5W,CAAC,CAAC,GAAGyW,UAAU,CAACtL,UAAU,CAACnL,CAAC,CAAC;EACpC;EACA,OAAO,IAAIsL,IAAI,CAAC,CAACoL,EAAE,CAAC,EAAE;IAAE5M;EAAK,CAAC,CAAC;AACnC;;AAEA;AACA;AACA;;AAEA,SAASjI,WAAW,EAAEjG,eAAe,EAAEqS,qBAAqB,EAAE+H,kBAAkB,EAAEzM,gBAAgB,EAAEgN,YAAY,EAAE5X,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}