{"ast": null, "code": "import { __decorate } from \"tslib\";\n/* The `NotesListComponent` is a component in an Angular application that displays a list of notes and\nprovides functionality for filtering, sorting, and performing actions on the notes. */\nimport { Component, Input } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { tap } from 'rxjs/operators';\nlet NotesListComponent = class NotesListComponent {\n  constructor(generalService, noteService, toastService, classroomService, cd, authService, noteListenersService, noteActionsService, dialogService, confirmDialogService) {\n    this.generalService = generalService;\n    this.noteService = noteService;\n    this.toastService = toastService;\n    this.classroomService = classroomService;\n    this.cd = cd;\n    this.authService = authService;\n    this.noteListenersService = noteListenersService;\n    this.noteActionsService = noteActionsService;\n    this.dialogService = dialogService;\n    this.confirmDialogService = confirmDialogService;\n    this.notes = [];\n    this.viewTitle = 'My Notes';\n    this.withClassroom = false;\n    this.hasAdd = true;\n    this.classroom = {};\n    this.subs = new SubSink();\n    this.showFilters = false;\n    this.showGroupActions = true;\n    this.showAddNote = false;\n    this.tryToSave = false;\n    this.showTextError = false;\n    this.isSharedFilter = false;\n    this.isNotSharedFilter = false;\n    this.isPinnedFilter = false;\n    this.isFavoritedFilter = false;\n    this.fromFilterValue = {};\n    this.toFilterValue = {};\n    this.datePickerOpen = false;\n    this.filteredNotes = [];\n    this.classrooms = [];\n    this.showSend = false;\n    this.showSendAndShare = false;\n    this.usersToSend = [];\n    this.currentSelectedClassroomId = 0;\n    this.showingMine = true;\n    this.inClassroom = false;\n    this.isLoading = false;\n    this.textEditor = '';\n    this.checkedNotes = [];\n    this.classroomStudents = [];\n    this.user = {};\n    this.role = \"\";\n    this.maxSize = 6;\n    this.directionLinks = true;\n    this.autoHide = false;\n    this.to = {};\n    this.from = {};\n    this.form = new UntypedFormGroup({});\n    this.isInMine = false;\n    this.noteViewAccess = NoteViewAccess;\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.user.role;\n    this.initClassroomListener();\n    // this.notSharedFilter(true);\n    this.form = new UntypedFormGroup({\n      title: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      text: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      })\n    });\n  }\n  ngOnChanges() {\n    if (this.classroom) {\n      this.classroomStudents = this.classroom.classroomStudents;\n    }\n    this.filteredNotes = this.notes;\n  }\n  ngAfterViewInit() {\n    this.showGroupActions = false;\n    this.cd.detectChanges();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.noteListenersService.setGroupActions(false);\n    this.noteService.checkedNotes = [];\n  }\n  ngAfterContentInit() {}\n  get currentNotesView() {\n    return this.noteListenersService.getNotesViewAccess(this.hasAdd, this.withClassroom);\n  }\n  //listener to change ui in nite details\n  setGroupActions() {\n    this.showGroupActions = !this.showGroupActions;\n    if (!this.showGroupActions) {\n      this.noteService.checkedNotes = [];\n    }\n    setTimeout(() => {\n      this.noteListenersService.setGroupActions(this.showGroupActions);\n    }, 300);\n  }\n  /**\n   * Filters the notes based on whether they are shared with the given student.\n   *\n   * @param targetStudent - The student user to filter by.\n   * @returns An array of notes that are shared with the given student.\n   */\n  getFilteredNotes(targetStudent) {\n    if (this.hasAdd) {\n      return this.filteredNotes.filter(note => {\n        return targetStudent ? note.sharedWithUsers.some(user => user.aspUserId === targetStudent.aspUserId) : true;\n      });\n    } else {\n      return this.filteredNotes.filter(note => {\n        return targetStudent ? note.createdBy.aspUserId === targetStudent.aspUserId : true;\n      });\n    }\n  }\n  areNotFavorited() {\n    for (let note of this.noteService.checkedNotes) {\n      if (note.favouritedBy.filter(user => user.id == this.authService.getUserId()).length == 0) return true;\n    }\n    return false;\n  }\n  areNotPinned() {\n    for (let note of this.noteService.checkedNotes) {\n      if (!note.pinned) return true;\n    }\n    return false;\n  }\n  /**\n   * Filters the notes based on the selected filters and returns the filtered notes.\n   *\n   * @return {Note[]} An array of Note objects that pass the applied filters.\n   */\n  getNotesFiltered() {\n    let filteredNotes = [];\n    for (let note of this.notes) {\n      if (this.isPinnedFilter) {\n        if (!note.pinned) {\n          continue;\n        }\n      }\n      if (this.isFavoritedFilter) {\n        if (note.favouritedBy.filter(user => user.aspUserId == this.authService.getUserId()).length == 0) {\n          continue;\n        }\n      }\n      //get shared \n      if (this.from.value) {\n        if (!(note.created.getTime() > this.from.value.getTime())) {\n          continue;\n        }\n      }\n      if (this.to.value) {\n        if (!(note.created.getTime() < moment(this.to.value).add('1', 'd').toDate().getTime())) {\n          continue;\n        }\n      }\n      if (this.isSharedFilter) {\n        if (note.sharedWithUsers.length == 0) {\n          continue;\n        }\n      }\n      if (this.isNotSharedFilter) {\n        if (note.sharedWithUsers.length > 0) {\n          continue;\n        }\n      }\n      filteredNotes.push(note);\n    }\n    return filteredNotes;\n  }\n  ifFieldValid(field) {\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n  }\n  isTextValid() {\n    this.showTextError = !this.textEditor && this.textEditor === '' ? true : false;\n  }\n  close(event) {\n    this.showSend = false;\n    this.showSendAndShare = false;\n  }\n  onClosedNewNote() {\n    console.log('he');\n    this.showAddNote = false;\n  }\n  onNoteChecked(event) {\n    console.log(event);\n    if (event.checked) {\n      this.noteService.checkedNotes.push(event.note);\n    } else {\n      this.noteService.checkedNotes = this.noteService.checkedNotes.filter(el => el.id !== event.note.id);\n    }\n  }\n  onShareMultiple() {\n    const checkedNotes = this.noteService.checkedNotes;\n    if (checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    const isTeacher = this.authService.isTeacher;\n    if (isTeacher) {\n      this.handleTeacherShareMultiple(checkedNotes);\n    } else {\n      this.handleStudentShareMultiple(checkedNotes);\n    }\n  }\n  onUnShareMultiple() {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    this.noteService.checkedNotes.forEach(note => {\n      const unshareWith = note.sharedWithUsers.map(user => user.aspUserId);\n      const sendForUnshare = {\n        noteId: note.id,\n        classroomId: Number(this.classroom.id),\n        studentIds: this.getUserIdsForUnshare(unshareWith, note)\n      };\n      this.noteActionsService.unShareNote(sendForUnshare, 'Notes unshared successfully');\n    });\n    this.setGroupActions();\n  }\n  downloadMultiple() {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    for (let note of this.noteService.checkedNotes) {\n      this.noteActionsService.download(note);\n    }\n  }\n  onRemoveNoteMultiple() {\n    this.initConfirmDeleteNoteDialogListener();\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    this.confirmDialogService.setProperties({\n      confirmMessage: `\n      Are you sure you want to delete the selected notes?`,\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      action: 'delete-note',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      showHeader: true,\n      dialogType: 'custom',\n      headerClass: 'my-custom-header-class'\n    });\n    this.confirmDialogService.show();\n  }\n  getUserIdsForUnshare(unshareWith, note) {\n    if (this.authService.isTeacher) {\n      return unshareWith;\n    } else if (this.noteService.isMyNote(note)) {\n      return [this.classroom.teacher.aspUserId];\n    }\n    return [this.user.aspUserId];\n  }\n  handleTeacherShareMultiple(checkedNotes) {\n    if (![this.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, this.noteViewAccess.TEACHER_VIEW_NOTES].includes(this.currentNotesView)) {\n      if (this.classroom.classroomStudents.length === 1) {\n        this.shareNotesMultipleWithClassroom(checkedNotes);\n      } else {\n        this.openShareMultipleWithNoteDialog(checkedNotes);\n      }\n    } else {\n      this.openShareMultipleWithNoteDialog(checkedNotes);\n    }\n  }\n  handleStudentShareMultiple(checkedNotes) {\n    const unshareWith = [];\n    for (let note of checkedNotes) {\n      for (let user of note.sharedWithUsers) {\n        unshareWith.push(user.aspUserId);\n      }\n      const sendForShare = {\n        noteId: note.id,\n        classroomId: +this.classroom.id,\n        studentIds: [this.classroom.teacher.aspUserId]\n      };\n      this.noteActionsService.shareNote(sendForShare, 'Notes unshared successfully');\n    }\n    this.setGroupActions();\n  }\n  shareNotesMultipleWithClassroom(checkedNotes) {\n    for (let note of checkedNotes) {\n      const sendForShare = {\n        noteId: note.id,\n        classroomId: +this.classroom.id,\n        studentIds: [this.classroom.classroomStudents[0].aspUserId]\n      };\n      this.noteActionsService.shareNote(sendForShare, 'Notes shared successfully');\n    }\n  }\n  openShareMultipleWithNoteDialog(checkedNotes) {\n    this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440, {\n      action: 'shareWith',\n      dialogsubTitle: '',\n      dialogTitle: 'Share Note with...',\n      preselectedClassroom: this.classroom\n    }, null, result => {\n      console.log(result);\n      if (result && result.action === 'shareWith') {\n        if (!this.generalService.isNullishObject(result)) {\n          for (let note of checkedNotes) {\n            this.noteActionsService.sendOrShare(note, result, this.authService.isStudent);\n          }\n          this.setGroupActions();\n          console.log(result);\n        }\n      }\n    });\n  }\n  initConfirmDeleteNoteDialogListener() {\n    this.confirmDeleteNoteSubscription = this.confirmDialogService.acceptSelected$.pipe(tap(res => {\n      this.confirmDialogService.hide();\n      console.log(res);\n      switch (res.action) {\n        case 'delete-note':\n          console.log('deltenetote');\n          this.onRemoveNoteMultipleAccepted();\n      }\n    })).subscribe();\n    this.subs.add(this.confirmDeleteNoteSubscription);\n  }\n  onRemoveNoteMultipleAccepted() {\n    console.log(this.noteService.checkedNotes);\n    for (let note of this.noteService.checkedNotes) {\n      console.log(note);\n      this.noteActionsService.afterAcceptRemoveNote(note);\n    }\n    this.confirmDeleteNoteSubscription.unsubscribe();\n    this.setGroupActions();\n  }\n  validateCheckedNotes(msg) {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: msg\n      });\n      return;\n    }\n  }\n  initClassroomListener() {\n    this.subs.add(this.noteListenersService.currentSelectedClassroom.subscribe(res => {\n      if (this.generalService.isNullishObject(res)) {\n        return;\n      }\n      console.log(this.filteredNotes);\n      this.classroom = res;\n      this.classroomStudents = this.classroom.classroomStudents;\n    }));\n  }\n};\n__decorate([Input()], NotesListComponent.prototype, \"notes\", void 0);\n__decorate([Input()], NotesListComponent.prototype, \"viewTitle\", void 0);\n__decorate([Input()], NotesListComponent.prototype, \"withClassroom\", void 0);\n__decorate([Input()], NotesListComponent.prototype, \"hasAdd\", void 0);\n__decorate([Input()], NotesListComponent.prototype, \"classroom\", void 0);\nNotesListComponent = __decorate([Component({\n  selector: 'app-notes-list',\n  templateUrl: './notes-list.component.html',\n  styleUrls: ['./notes-list.component.scss'],\n  providers: [DialogService, NoteListenersService]\n})], NotesListComponent);\nexport { NotesListComponent };", "map": {"version": 3, "names": ["Component", "Input", "UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "moment", "NoteListenersService", "NoteViewAccess", "LibraryFileActionsDialogComponent", "DialogService", "tap", "NotesListComponent", "constructor", "generalService", "noteService", "toastService", "classroomService", "cd", "authService", "noteListenersService", "noteActionsService", "dialogService", "confirmDialogService", "notes", "viewTitle", "withClassroom", "hasAdd", "classroom", "subs", "showFilters", "showGroupActions", "showAddNote", "tryToSave", "showTextError", "isSharedFilter", "isNotSharedFilter", "isPinnedFilter", "isFavoritedFilter", "fromFilterValue", "toFilterValue", "datePickerOpen", "filteredNotes", "classrooms", "showSend", "showSendAndShare", "usersToSend", "currentSelectedClassroomId", "showingMine", "inClassroom", "isLoading", "textEditor", "checkedNotes", "classroomStudents", "user", "role", "maxSize", "directionLinks", "autoHide", "to", "from", "form", "isInMine", "noteViewAccess", "ngOnInit", "getLoggedInUser", "initClassroomListener", "title", "validators", "required", "text", "ngOnChanges", "ngAfterViewInit", "detectChanges", "ngOnDestroy", "unsubscribe", "setGroupActions", "ngAfterContentInit", "currentNotesView", "getNotesViewAccess", "setTimeout", "getFilteredNotes", "targetStudent", "filter", "note", "sharedWithUsers", "some", "aspUserId", "created<PERSON>y", "areNotFavorited", "favourited<PERSON>y", "id", "getUserId", "length", "areNotPinned", "pinned", "getNotesFiltered", "value", "created", "getTime", "add", "toDate", "push", "ifFieldValid", "field", "get", "invalid", "touched", "isTextValid", "close", "event", "onClosedNewNote", "console", "log", "onNoteChecked", "checked", "el", "onShareMultiple", "validateCheckedNotes", "<PERSON><PERSON><PERSON>er", "handleTeacherShareMultiple", "handleStudentShareMultiple", "onUnShareMultiple", "for<PERSON>ach", "unshareWith", "map", "sendForUnshare", "noteId", "classroomId", "Number", "studentIds", "getUserIdsForUnshare", "unShareNote", "downloadMultiple", "download", "onRemoveNoteMultiple", "initConfirmDeleteNoteDialogListener", "setProperties", "confirmMessage", "acceptBtnLabel", "rejectBtnLabel", "action", "confirmIcon", "showHeader", "dialogType", "headerClass", "show", "isMyNote", "teacher", "TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES", "TEACHER_VIEW_NOTES", "includes", "shareNotesMultipleWithClassroom", "openShareMultipleWithNoteDialog", "sendForShare", "shareNote", "openDialogWithComponent", "dialogsubTitle", "dialogTitle", "preselectedClassroom", "result", "isNullishObject", "sendOrShare", "isStudent", "confirmDeleteNoteSubscription", "acceptSelected$", "pipe", "res", "hide", "onRemoveNoteMultipleAccepted", "subscribe", "afterAcceptRemoveNote", "msg", "setShowToastmessage", "severity", "summary", "detail", "currentSelectedClassroom", "__decorate", "selector", "templateUrl", "styleUrls", "providers"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\notes-list\\notes-list.component.ts"], "sourcesContent": ["/* The `NotesListComponent` is a component in an Angular application that displays a list of notes and\r\nprovides functionality for filtering, sorting, and performing actions on the notes. */\r\nimport { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Note, SendShareNoteRequest } from 'src/app/core/models/note.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport * as moment from 'moment';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { NoteActionsService } from 'src/app/core/services/note-actions.service';\r\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\r\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { take, tap } from 'rxjs/operators';\r\nimport { Subscription } from 'rxjs';\r\n\r\ninterface SendShareEvent {\r\n  usersSelected: User[];\r\n  kind: string;\r\n  userClassroomMap: Map<User[], number>;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-notes-list',\r\n  templateUrl: './notes-list.component.html',\r\n  styleUrls: ['./notes-list.component.scss'],\r\n  providers: [DialogService, NoteListenersService]\r\n\r\n})\r\nexport class NotesListComponent implements OnInit {\r\n  @Input() notes: Note[] = [];\r\n  @Input() viewTitle = 'My Notes';\r\n  @Input() withClassroom = false;\r\n  @Input() hasAdd = true;\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  private subs = new SubSink();\r\n  showFilters: boolean = false;\r\n  showGroupActions: boolean = true;\r\n  term: any;\r\n  sendTerm: any;\r\n  showAddNote: boolean = false;\r\n  tryToSave: boolean = false;\r\n  showTextError: boolean = false;\r\n  isSharedFilter: boolean = false;\r\n  isNotSharedFilter: boolean = false;\r\n  isPinnedFilter: boolean = false;\r\n  isFavoritedFilter: boolean = false;\r\n  fromFilterValue: Date = {} as Date;\r\n  toFilterValue: Date = {} as Date;\r\n  datePickerOpen: boolean = false;\r\n  filteredNotes: Note[] = [];\r\n  classrooms: Classroom[] = [];\r\n  showSend: boolean = false;\r\n  showSendAndShare: boolean = false;\r\n  usersToSend: User[] = [];\r\n  currentSelectedClassroomId: number = 0;\r\n  showingMine: boolean = true;\r\n  inClassroom: boolean = false;\r\n  isLoading: boolean = false;\r\n  textEditor = '';\r\n  checkedNotes: Note[] = [];\r\n  classroomStudents: User[] = [];\r\n  public user: User = {} as User;\r\n  public role: string = \"\";\r\n  public maxSize: number = 6;\r\n  public directionLinks: boolean = true;\r\n  public autoHide: boolean = false;\r\n  public to: any = {} as any;\r\n  public from: any = {} as any;\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  isInMine: boolean = false;\r\n  public noteViewAccess = NoteViewAccess;\r\n  confirmDeleteNoteSubscription!: Subscription;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private noteService: NoteService,\r\n    private toastService: ToastService,\r\n    private classroomService: ClassroomService,\r\n    private cd: ChangeDetectorRef,\r\n    private authService: AuthService,\r\n    public noteListenersService: NoteListenersService,\r\n    public noteActionsService: NoteActionsService,\r\n    public dialogService: DialogService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.user.role!\r\n    this.initClassroomListener();\r\n    // this.notSharedFilter(true);\r\n    this.form = new UntypedFormGroup({\r\n      title: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      text: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n    });\r\n  }\r\n\r\n  ngOnChanges() {\r\n    if (this.classroom) {\r\n      this.classroomStudents = this.classroom.classroomStudents;\r\n    }\r\n    this.filteredNotes = this.notes\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.showGroupActions = false;\r\n    this.cd.detectChanges()\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.noteListenersService.setGroupActions(false);\r\n    this.noteService.checkedNotes = [];\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n  }\r\n\r\n  get currentNotesView() {\r\n    return this.noteListenersService.getNotesViewAccess(this.hasAdd, this.withClassroom);\r\n  }\r\n\r\n  //listener to change ui in nite details\r\n  setGroupActions() {\r\n    this.showGroupActions = !this.showGroupActions;\r\n    if (!this.showGroupActions) {\r\n      this.noteService.checkedNotes = [];\r\n    }\r\n    setTimeout(() => {\r\n      this.noteListenersService.setGroupActions(this.showGroupActions);\r\n    }, 300);\r\n  }\r\n\r\n  /**\r\n   * Filters the notes based on whether they are shared with the given student.\r\n   *\r\n   * @param targetStudent - The student user to filter by.\r\n   * @returns An array of notes that are shared with the given student.\r\n   */\r\n  getFilteredNotes(targetStudent: User): Note[] {\r\n    if (this.hasAdd) {\r\n    return this.filteredNotes.filter((note: Note) => {\r\n      return targetStudent\r\n        ? note.sharedWithUsers.some(\r\n          (user: User) => user.aspUserId === targetStudent.aspUserId\r\n        )\r\n        : true;\r\n    });\r\n    } else {\r\n      return this.filteredNotes.filter((note: Note) => {\r\n        return targetStudent\r\n          ? note.createdBy.aspUserId === targetStudent.aspUserId : true;\r\n      });\r\n    }\r\n\r\n  }\r\n\r\n  areNotFavorited() {\r\n    for (let note of this.noteService.checkedNotes) {\r\n      if (note.favouritedBy.filter((user: User) => user.id == this.authService.getUserId()).length == 0) return true;\r\n    }\r\n    return false\r\n  }\r\n\r\n  areNotPinned() {\r\n    for (let note of this.noteService.checkedNotes) {\r\n      if (!note.pinned) return true;\r\n    }\r\n    return false\r\n  }\r\n\r\n\r\n  /**\r\n   * Filters the notes based on the selected filters and returns the filtered notes.\r\n   *\r\n   * @return {Note[]} An array of Note objects that pass the applied filters.\r\n   */\r\n  getNotesFiltered() {\r\n    let filteredNotes: Note[] = [];\r\n    for (let note of this.notes) {\r\n      if (this.isPinnedFilter) {\r\n        if (!note.pinned) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.isFavoritedFilter) {\r\n        if (note.favouritedBy.filter((user: User) => user.aspUserId == this.authService.getUserId()).length == 0) {\r\n          continue;\r\n        }\r\n      }\r\n      //get shared \r\n      if (this.from.value) {\r\n        if (!(note.created.getTime() > this.from.value.getTime())) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.to.value) {\r\n        if (!(note.created.getTime() < moment(this.to.value).add('1', 'd').toDate().getTime())) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.isSharedFilter) {\r\n        if (note.sharedWithUsers.length == 0) {\r\n          continue\r\n        }\r\n      }\r\n      if (this.isNotSharedFilter) {\r\n        if (note.sharedWithUsers.length > 0) {\r\n          continue\r\n        }\r\n      }\r\n      filteredNotes.push(note)\r\n    }\r\n    return filteredNotes\r\n  }\r\n\r\n  ifFieldValid(field: string) {\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid))\r\n  }\r\n\r\n  isTextValid() {\r\n    this.showTextError = !this.textEditor && this.textEditor === '' ? true : false\r\n  }\r\n\r\n  close(event: any) {\r\n    this.showSend = false\r\n    this.showSendAndShare = false\r\n  }\r\n\r\n  onClosedNewNote() {\r\n    console.log('he');\r\n    this.showAddNote = false;\r\n  }\r\n\r\n  onNoteChecked(event: any) {\r\n    console.log(event);\r\n    if (event.checked) {\r\n      this.noteService.checkedNotes.push(event.note);\r\n    } else {\r\n      this.noteService.checkedNotes = this.noteService.checkedNotes.filter(el => el.id !== event.note.id);\r\n    }\r\n  }\r\n\r\n  onShareMultiple() {\r\n    const checkedNotes = this.noteService.checkedNotes;\r\n      \r\n    if (checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n      \r\n    const isTeacher = this.authService.isTeacher;\r\n    \r\n    if (isTeacher) {\r\n      this.handleTeacherShareMultiple(checkedNotes);\r\n    } else {\r\n      this.handleStudentShareMultiple(checkedNotes);\r\n    }\r\n  }\r\n\r\n  onUnShareMultiple() {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n        this.validateCheckedNotes('Please select at least one note.');\r\n        return;\r\n    }\r\n\r\n    this.noteService.checkedNotes.forEach(note => {\r\n        const unshareWith: any[] = note.sharedWithUsers.map(user => user.aspUserId);\r\n        \r\n        const sendForUnshare: SendShareNoteRequest = {\r\n            noteId: note.id,\r\n            classroomId: Number(this.classroom.id),\r\n            studentIds: this.getUserIdsForUnshare(unshareWith, note)\r\n        };\r\n        this.noteActionsService.unShareNote(sendForUnshare, 'Notes unshared successfully');\r\n    });\r\n  \r\n    this.setGroupActions();\r\n}\r\n  \r\n\r\n  downloadMultiple() {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n    for (let note of this.noteService.checkedNotes) {\r\n      this.noteActionsService.download(note);\r\n    }\r\n  }\r\n\r\n  onRemoveNoteMultiple() {\r\n    this.initConfirmDeleteNoteDialogListener();\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `\r\n      Are you sure you want to delete the selected notes?`,\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      action: 'delete-note',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    this.confirmDialogService.show();\r\n\r\n  }\r\n\r\n  private getUserIdsForUnshare(unshareWith: any[], note: any): any[] {\r\n    if (this.authService.isTeacher) {\r\n      return unshareWith;\r\n    } else if (this.noteService.isMyNote(note)) {\r\n      return [this.classroom.teacher!.aspUserId!];\r\n    }\r\n    return [this.user.aspUserId!];\r\n  }\r\n\r\n  private handleTeacherShareMultiple(checkedNotes: Note[]) {\r\n    if (![this.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, this.noteViewAccess.TEACHER_VIEW_NOTES].includes(this.currentNotesView)) {\r\n  \r\n      if (this.classroom.classroomStudents.length === 1) {\r\n        this.shareNotesMultipleWithClassroom(checkedNotes);\r\n      } else {\r\n        this.openShareMultipleWithNoteDialog(checkedNotes);\r\n      }\r\n  \r\n    } else {\r\n      this.openShareMultipleWithNoteDialog(checkedNotes);\r\n    }\r\n  }\r\n  \r\n  private handleStudentShareMultiple(checkedNotes: Note[]) {\r\n    const unshareWith: any[] = [];\r\n      \r\n    for (let note of checkedNotes) {\r\n      for (let user of note.sharedWithUsers) {\r\n        unshareWith.push(user.aspUserId);\r\n      }\r\n      \r\n      const sendForShare: SendShareNoteRequest = {\r\n        noteId: note.id,\r\n        classroomId: +this.classroom.id,\r\n        studentIds: [this.classroom.teacher!.aspUserId!]\r\n      };\r\n      this.noteActionsService.shareNote(sendForShare, 'Notes unshared successfully');\r\n    }\r\n      \r\n    this.setGroupActions();\r\n  }\r\n  \r\n  private shareNotesMultipleWithClassroom(checkedNotes: Note[]) {\r\n    for (let note of checkedNotes) {\r\n      const sendForShare: SendShareNoteRequest = {\r\n          noteId: note.id,\r\n          classroomId: +this.classroom.id,\r\n          studentIds: [this.classroom.classroomStudents[0].aspUserId!]\r\n       };\r\n      this.noteActionsService.shareNote(sendForShare, 'Notes shared successfully');\r\n    }\r\n  }\r\n  \r\n  private openShareMultipleWithNoteDialog(checkedNotes: Note[]) {\r\n    this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440,\r\n      {\r\n        action: 'shareWith',\r\n        dialogsubTitle: '',\r\n        dialogTitle: 'Share Note with...',\r\n        preselectedClassroom: this.classroom,\r\n      }, null, (result: any) => {\r\n      console.log(result);\r\n      if (result && result.action === 'shareWith') {\r\n        if (!this.generalService.isNullishObject(result)) {\r\n  \r\n          for (let note of checkedNotes) {\r\n            this.noteActionsService.sendOrShare(note, result, this.authService.isStudent);\r\n          }\r\n          this.setGroupActions();\r\n          console.log(result);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private initConfirmDeleteNoteDialogListener() {\r\n    this.confirmDeleteNoteSubscription = (this.confirmDialogService.acceptSelected$\r\n      .pipe(tap((res) => {\r\n\r\n        this.confirmDialogService.hide();\r\n        console.log(res);\r\n\r\n        switch (res.action) {\r\n          case 'delete-note':\r\n            console.log('deltenetote')\r\n            this.onRemoveNoteMultipleAccepted();\r\n        }\r\n      })).subscribe());\r\n\r\n    this.subs.add(this.confirmDeleteNoteSubscription);\r\n  }\r\n\r\n  private onRemoveNoteMultipleAccepted() {\r\n    console.log(this.noteService.checkedNotes);\r\n    for (let note of this.noteService.checkedNotes) {\r\n      console.log(note);\r\n      this.noteActionsService.afterAcceptRemoveNote(note);\r\n    }\r\n    this.confirmDeleteNoteSubscription.unsubscribe();\r\n    this.setGroupActions();\r\n  }\r\n\r\n  private validateCheckedNotes(msg: string) {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: msg \r\n      });\r\n      return;\r\n    }\r\n  }\r\n\r\n  private initClassroomListener() {\r\n    this.subs.add(this.noteListenersService.currentSelectedClassroom.subscribe(res => {\r\n      if (this.generalService.isNullishObject(res)) {\r\n        return;\r\n      }\r\n      console.log(this.filteredNotes);\r\n      this.classroom = res;\r\n      this.classroomStudents = this.classroom.classroomStudents;\r\n    }));\r\n  }\r\n}\r\n\r\n\r\n"], "mappings": ";AAAA;;AAEA,SAA4BA,SAAS,EAAEC,KAAK,QAA2B,eAAe;AACtF,SAASC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAOjF,SAASC,OAAO,QAAQ,SAAS;AAEjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,8CAA8C;AACnG,SAASC,iCAAiC,QAAQ,8GAA8G;AAChK,SAASC,aAAa,QAAQ,uBAAuB;AAErD,SAAeC,GAAG,QAAQ,gBAAgB;AAgBnC,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EA6C7BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,gBAAkC,EAClCC,EAAqB,EACrBC,WAAwB,EACzBC,oBAA0C,EAC1CC,kBAAsC,EACtCC,aAA4B,EAC5BC,oBAA0C;IATzC,KAAAT,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IAtDpB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,SAAS,GAAG,UAAU;IACtB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,MAAM,GAAG,IAAI;IACb,KAAAC,SAAS,GAAc,EAAe;IACvC,KAAAC,IAAI,GAAG,IAAIxB,OAAO,EAAE;IAC5B,KAAAyB,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAY,IAAI;IAGhC,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,eAAe,GAAS,EAAU;IAClC,KAAAC,aAAa,GAAS,EAAU;IAChC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,UAAU,GAAgB,EAAE;IAC5B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,0BAA0B,GAAW,CAAC;IACtC,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,iBAAiB,GAAW,EAAE;IACvB,KAAAC,IAAI,GAAS,EAAU;IACvB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,OAAO,GAAW,CAAC;IACnB,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,EAAE,GAAQ,EAAS;IACnB,KAAAC,IAAI,GAAQ,EAAS;IACrB,KAAAC,IAAI,GAAqB,IAAI1D,gBAAgB,CAAC,EAAE,CAAC;IACxD,KAAA2D,QAAQ,GAAY,KAAK;IAClB,KAAAC,cAAc,GAAGvD,cAAc;EAclC;EAEJwD,QAAQA,CAAA;IACN,IAAI,CAACV,IAAI,GAAG,IAAI,CAACnC,WAAW,CAAC8C,eAAe,EAAE;IAC9C,IAAI,CAACV,IAAI,GAAG,IAAI,CAACD,IAAI,CAACC,IAAK;IAC3B,IAAI,CAACW,qBAAqB,EAAE;IAC5B;IACA,IAAI,CAACL,IAAI,GAAG,IAAI1D,gBAAgB,CAAC;MAC/BgE,KAAK,EAAE,IAAIjE,kBAAkB,CAAC,IAAI,EAAE;QAClCkE,UAAU,EAAE,CAAChE,UAAU,CAACiE,QAAQ;OACjC,CAAC;MACFC,IAAI,EAAE,IAAIpE,kBAAkB,CAAC,IAAI,EAAE;QACjCkE,UAAU,EAAE,CAAChE,UAAU,CAACiE,QAAQ;OACjC;KACF,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC3C,SAAS,EAAE;MAClB,IAAI,CAACyB,iBAAiB,GAAG,IAAI,CAACzB,SAAS,CAACyB,iBAAiB;IAC3D;IACA,IAAI,CAACX,aAAa,GAAG,IAAI,CAAClB,KAAK;EACjC;EAEAgD,eAAeA,CAAA;IACb,IAAI,CAACzC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACb,EAAE,CAACuD,aAAa,EAAE;EACzB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC7C,IAAI,CAAC8C,WAAW,EAAE;IACvB,IAAI,CAACvD,oBAAoB,CAACwD,eAAe,CAAC,KAAK,CAAC;IAChD,IAAI,CAAC7D,WAAW,CAACqC,YAAY,GAAG,EAAE;EACpC;EAEAyB,kBAAkBA,CAAA,GAClB;EAEA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC1D,oBAAoB,CAAC2D,kBAAkB,CAAC,IAAI,CAACpD,MAAM,EAAE,IAAI,CAACD,aAAa,CAAC;EACtF;EAEA;EACAkD,eAAeA,CAAA;IACb,IAAI,CAAC7C,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAAChB,WAAW,CAACqC,YAAY,GAAG,EAAE;IACpC;IACA4B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5D,oBAAoB,CAACwD,eAAe,CAAC,IAAI,CAAC7C,gBAAgB,CAAC;IAClE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;;;;EAMAkD,gBAAgBA,CAACC,aAAmB;IAClC,IAAI,IAAI,CAACvD,MAAM,EAAE;MACjB,OAAO,IAAI,CAACe,aAAa,CAACyC,MAAM,CAAEC,IAAU,IAAI;QAC9C,OAAOF,aAAa,GAChBE,IAAI,CAACC,eAAe,CAACC,IAAI,CACxBhC,IAAU,IAAKA,IAAI,CAACiC,SAAS,KAAKL,aAAa,CAACK,SAAS,CAC3D,GACC,IAAI;MACV,CAAC,CAAC;IACF,CAAC,MAAM;MACL,OAAO,IAAI,CAAC7C,aAAa,CAACyC,MAAM,CAAEC,IAAU,IAAI;QAC9C,OAAOF,aAAa,GAChBE,IAAI,CAACI,SAAS,CAACD,SAAS,KAAKL,aAAa,CAACK,SAAS,GAAG,IAAI;MACjE,CAAC,CAAC;IACJ;EAEF;EAEAE,eAAeA,CAAA;IACb,KAAK,IAAIL,IAAI,IAAI,IAAI,CAACrE,WAAW,CAACqC,YAAY,EAAE;MAC9C,IAAIgC,IAAI,CAACM,YAAY,CAACP,MAAM,CAAE7B,IAAU,IAAKA,IAAI,CAACqC,EAAE,IAAI,IAAI,CAACxE,WAAW,CAACyE,SAAS,EAAE,CAAC,CAACC,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;IAChH;IACA,OAAO,KAAK;EACd;EAEAC,YAAYA,CAAA;IACV,KAAK,IAAIV,IAAI,IAAI,IAAI,CAACrE,WAAW,CAACqC,YAAY,EAAE;MAC9C,IAAI,CAACgC,IAAI,CAACW,MAAM,EAAE,OAAO,IAAI;IAC/B;IACA,OAAO,KAAK;EACd;EAGA;;;;;EAKAC,gBAAgBA,CAAA;IACd,IAAItD,aAAa,GAAW,EAAE;IAC9B,KAAK,IAAI0C,IAAI,IAAI,IAAI,CAAC5D,KAAK,EAAE;MAC3B,IAAI,IAAI,CAACa,cAAc,EAAE;QACvB,IAAI,CAAC+C,IAAI,CAACW,MAAM,EAAE;UAChB;QACF;MACF;MACA,IAAI,IAAI,CAACzD,iBAAiB,EAAE;QAC1B,IAAI8C,IAAI,CAACM,YAAY,CAACP,MAAM,CAAE7B,IAAU,IAAKA,IAAI,CAACiC,SAAS,IAAI,IAAI,CAACpE,WAAW,CAACyE,SAAS,EAAE,CAAC,CAACC,MAAM,IAAI,CAAC,EAAE;UACxG;QACF;MACF;MACA;MACA,IAAI,IAAI,CAACjC,IAAI,CAACqC,KAAK,EAAE;QACnB,IAAI,EAAEb,IAAI,CAACc,OAAO,CAACC,OAAO,EAAE,GAAG,IAAI,CAACvC,IAAI,CAACqC,KAAK,CAACE,OAAO,EAAE,CAAC,EAAE;UACzD;QACF;MACF;MACA,IAAI,IAAI,CAACxC,EAAE,CAACsC,KAAK,EAAE;QACjB,IAAI,EAAEb,IAAI,CAACc,OAAO,CAACC,OAAO,EAAE,GAAG7F,MAAM,CAAC,IAAI,CAACqD,EAAE,CAACsC,KAAK,CAAC,CAACG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE,CAACF,OAAO,EAAE,CAAC,EAAE;UACtF;QACF;MACF;MACA,IAAI,IAAI,CAAChE,cAAc,EAAE;QACvB,IAAIiD,IAAI,CAACC,eAAe,CAACQ,MAAM,IAAI,CAAC,EAAE;UACpC;QACF;MACF;MACA,IAAI,IAAI,CAACzD,iBAAiB,EAAE;QAC1B,IAAIgD,IAAI,CAACC,eAAe,CAACQ,MAAM,GAAG,CAAC,EAAE;UACnC;QACF;MACF;MACAnD,aAAa,CAAC4D,IAAI,CAAClB,IAAI,CAAC;IAC1B;IACA,OAAO1C,aAAa;EACtB;EAEA6D,YAAYA,CAACC,KAAa;IACxB,OAAS,IAAI,CAAC3C,IAAI,CAAC4C,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAO,IAAI,IAAI,CAAC7C,IAAI,CAAC4C,GAAG,CAACD,KAAK,CAAC,EAAEG,OAAO,IAAM,IAAI,CAAC1E,SAAS,IAAI,IAAI,CAAC4B,IAAI,CAAC4C,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAQ;EAC/H;EAEAE,WAAWA,CAAA;IACT,IAAI,CAAC1E,aAAa,GAAG,CAAC,IAAI,CAACiB,UAAU,IAAI,IAAI,CAACA,UAAU,KAAK,EAAE,GAAG,IAAI,GAAG,KAAK;EAChF;EAEA0D,KAAKA,CAACC,KAAU;IACd,IAAI,CAAClE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,KAAK;EAC/B;EAEAkE,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;IACjB,IAAI,CAACjF,WAAW,GAAG,KAAK;EAC1B;EAEAkF,aAAaA,CAACJ,KAAU;IACtBE,OAAO,CAACC,GAAG,CAACH,KAAK,CAAC;IAClB,IAAIA,KAAK,CAACK,OAAO,EAAE;MACjB,IAAI,CAACpG,WAAW,CAACqC,YAAY,CAACkD,IAAI,CAACQ,KAAK,CAAC1B,IAAI,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACrE,WAAW,CAACqC,YAAY,GAAG,IAAI,CAACrC,WAAW,CAACqC,YAAY,CAAC+B,MAAM,CAACiC,EAAE,IAAIA,EAAE,CAACzB,EAAE,KAAKmB,KAAK,CAAC1B,IAAI,CAACO,EAAE,CAAC;IACrG;EACF;EAEA0B,eAAeA,CAAA;IACb,MAAMjE,YAAY,GAAG,IAAI,CAACrC,WAAW,CAACqC,YAAY;IAElD,IAAIA,YAAY,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACyB,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IAEA,MAAMC,SAAS,GAAG,IAAI,CAACpG,WAAW,CAACoG,SAAS;IAE5C,IAAIA,SAAS,EAAE;MACb,IAAI,CAACC,0BAA0B,CAACpE,YAAY,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACqE,0BAA0B,CAACrE,YAAY,CAAC;IAC/C;EACF;EAEAsE,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC3G,WAAW,CAACqC,YAAY,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACyB,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACJ;IAEA,IAAI,CAACvG,WAAW,CAACqC,YAAY,CAACuE,OAAO,CAACvC,IAAI,IAAG;MACzC,MAAMwC,WAAW,GAAUxC,IAAI,CAACC,eAAe,CAACwC,GAAG,CAACvE,IAAI,IAAIA,IAAI,CAACiC,SAAS,CAAC;MAE3E,MAAMuC,cAAc,GAAyB;QACzCC,MAAM,EAAE3C,IAAI,CAACO,EAAE;QACfqC,WAAW,EAAEC,MAAM,CAAC,IAAI,CAACrG,SAAS,CAAC+D,EAAE,CAAC;QACtCuC,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACP,WAAW,EAAExC,IAAI;OAC1D;MACD,IAAI,CAAC/D,kBAAkB,CAAC+G,WAAW,CAACN,cAAc,EAAE,6BAA6B,CAAC;IACtF,CAAC,CAAC;IAEF,IAAI,CAAClD,eAAe,EAAE;EAC1B;EAGEyD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACtH,WAAW,CAACqC,YAAY,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACyB,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IACA,KAAK,IAAIlC,IAAI,IAAI,IAAI,CAACrE,WAAW,CAACqC,YAAY,EAAE;MAC9C,IAAI,CAAC/B,kBAAkB,CAACiH,QAAQ,CAAClD,IAAI,CAAC;IACxC;EACF;EAEAmD,oBAAoBA,CAAA;IAClB,IAAI,CAACC,mCAAmC,EAAE;IAC1C,IAAI,IAAI,CAACzH,WAAW,CAACqC,YAAY,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACyB,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IACA,IAAI,CAAC/F,oBAAoB,CAACkH,aAAa,CAAC;MACtCC,cAAc,EAAE;0DACoC;MACpDC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,aAAa;MACrBC,WAAW,EAAE,qCAAqC;MAClDC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,CAAC1H,oBAAoB,CAAC2H,IAAI,EAAE;EAElC;EAEQf,oBAAoBA,CAACP,WAAkB,EAAExC,IAAS;IACxD,IAAI,IAAI,CAACjE,WAAW,CAACoG,SAAS,EAAE;MAC9B,OAAOK,WAAW;IACpB,CAAC,MAAM,IAAI,IAAI,CAAC7G,WAAW,CAACoI,QAAQ,CAAC/D,IAAI,CAAC,EAAE;MAC1C,OAAO,CAAC,IAAI,CAACxD,SAAS,CAACwH,OAAQ,CAAC7D,SAAU,CAAC;IAC7C;IACA,OAAO,CAAC,IAAI,CAACjC,IAAI,CAACiC,SAAU,CAAC;EAC/B;EAEQiC,0BAA0BA,CAACpE,YAAoB;IACrD,IAAI,CAAC,CAAC,IAAI,CAACW,cAAc,CAACsF,qCAAqC,EAAE,IAAI,CAACtF,cAAc,CAACuF,kBAAkB,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACzE,gBAAgB,CAAC,EAAE;MAExI,IAAI,IAAI,CAAClD,SAAS,CAACyB,iBAAiB,CAACwC,MAAM,KAAK,CAAC,EAAE;QACjD,IAAI,CAAC2D,+BAA+B,CAACpG,YAAY,CAAC;MACpD,CAAC,MAAM;QACL,IAAI,CAACqG,+BAA+B,CAACrG,YAAY,CAAC;MACpD;IAEF,CAAC,MAAM;MACL,IAAI,CAACqG,+BAA+B,CAACrG,YAAY,CAAC;IACpD;EACF;EAEQqE,0BAA0BA,CAACrE,YAAoB;IACrD,MAAMwE,WAAW,GAAU,EAAE;IAE7B,KAAK,IAAIxC,IAAI,IAAIhC,YAAY,EAAE;MAC7B,KAAK,IAAIE,IAAI,IAAI8B,IAAI,CAACC,eAAe,EAAE;QACrCuC,WAAW,CAACtB,IAAI,CAAChD,IAAI,CAACiC,SAAS,CAAC;MAClC;MAEA,MAAMmE,YAAY,GAAyB;QACzC3B,MAAM,EAAE3C,IAAI,CAACO,EAAE;QACfqC,WAAW,EAAE,CAAC,IAAI,CAACpG,SAAS,CAAC+D,EAAE;QAC/BuC,UAAU,EAAE,CAAC,IAAI,CAACtG,SAAS,CAACwH,OAAQ,CAAC7D,SAAU;OAChD;MACD,IAAI,CAAClE,kBAAkB,CAACsI,SAAS,CAACD,YAAY,EAAE,6BAA6B,CAAC;IAChF;IAEA,IAAI,CAAC9E,eAAe,EAAE;EACxB;EAEQ4E,+BAA+BA,CAACpG,YAAoB;IAC1D,KAAK,IAAIgC,IAAI,IAAIhC,YAAY,EAAE;MAC7B,MAAMsG,YAAY,GAAyB;QACvC3B,MAAM,EAAE3C,IAAI,CAACO,EAAE;QACfqC,WAAW,EAAE,CAAC,IAAI,CAACpG,SAAS,CAAC+D,EAAE;QAC/BuC,UAAU,EAAE,CAAC,IAAI,CAACtG,SAAS,CAACyB,iBAAiB,CAAC,CAAC,CAAC,CAACkC,SAAU;OAC7D;MACF,IAAI,CAAClE,kBAAkB,CAACsI,SAAS,CAACD,YAAY,EAAE,2BAA2B,CAAC;IAC9E;EACF;EAEQD,+BAA+BA,CAACrG,YAAoB;IAC1D,IAAI,CAACtC,cAAc,CAAC8I,uBAAuB,CAAC,IAAI,CAACtI,aAAa,EAAEb,iCAAiC,EAAE,GAAG,EACpG;MACEoI,MAAM,EAAE,WAAW;MACnBgB,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,oBAAoB;MACjCC,oBAAoB,EAAE,IAAI,CAACnI;KAC5B,EAAE,IAAI,EAAGoI,MAAW,IAAI;MACzBhD,OAAO,CAACC,GAAG,CAAC+C,MAAM,CAAC;MACnB,IAAIA,MAAM,IAAIA,MAAM,CAACnB,MAAM,KAAK,WAAW,EAAE;QAC3C,IAAI,CAAC,IAAI,CAAC/H,cAAc,CAACmJ,eAAe,CAACD,MAAM,CAAC,EAAE;UAEhD,KAAK,IAAI5E,IAAI,IAAIhC,YAAY,EAAE;YAC7B,IAAI,CAAC/B,kBAAkB,CAAC6I,WAAW,CAAC9E,IAAI,EAAE4E,MAAM,EAAE,IAAI,CAAC7I,WAAW,CAACgJ,SAAS,CAAC;UAC/E;UACA,IAAI,CAACvF,eAAe,EAAE;UACtBoC,OAAO,CAACC,GAAG,CAAC+C,MAAM,CAAC;QACrB;MACF;IACF,CAAC,CAAC;EACJ;EAEQxB,mCAAmCA,CAAA;IACzC,IAAI,CAAC4B,6BAA6B,GAAI,IAAI,CAAC7I,oBAAoB,CAAC8I,eAAe,CAC5EC,IAAI,CAAC3J,GAAG,CAAE4J,GAAG,IAAI;MAEhB,IAAI,CAAChJ,oBAAoB,CAACiJ,IAAI,EAAE;MAChCxD,OAAO,CAACC,GAAG,CAACsD,GAAG,CAAC;MAEhB,QAAQA,GAAG,CAAC1B,MAAM;QAChB,KAAK,aAAa;UAChB7B,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;UAC1B,IAAI,CAACwD,4BAA4B,EAAE;MACvC;IACF,CAAC,CAAC,CAAC,CAACC,SAAS,EAAG;IAElB,IAAI,CAAC7I,IAAI,CAACuE,GAAG,CAAC,IAAI,CAACgE,6BAA6B,CAAC;EACnD;EAEQK,4BAA4BA,CAAA;IAClCzD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClG,WAAW,CAACqC,YAAY,CAAC;IAC1C,KAAK,IAAIgC,IAAI,IAAI,IAAI,CAACrE,WAAW,CAACqC,YAAY,EAAE;MAC9C4D,OAAO,CAACC,GAAG,CAAC7B,IAAI,CAAC;MACjB,IAAI,CAAC/D,kBAAkB,CAACsJ,qBAAqB,CAACvF,IAAI,CAAC;IACrD;IACA,IAAI,CAACgF,6BAA6B,CAACzF,WAAW,EAAE;IAChD,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQ0C,oBAAoBA,CAACsD,GAAW;IACtC,IAAI,IAAI,CAAC7J,WAAW,CAACqC,YAAY,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAAC7E,YAAY,CAAC6J,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAEJ;OACT,CAAC;MACF;IACF;EACF;EAEQ1G,qBAAqBA,CAAA;IAC3B,IAAI,CAACrC,IAAI,CAACuE,GAAG,CAAC,IAAI,CAAChF,oBAAoB,CAAC6J,wBAAwB,CAACP,SAAS,CAACH,GAAG,IAAG;MAC/E,IAAI,IAAI,CAACzJ,cAAc,CAACmJ,eAAe,CAACM,GAAG,CAAC,EAAE;QAC5C;MACF;MACAvD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvE,aAAa,CAAC;MAC/B,IAAI,CAACd,SAAS,GAAG2I,GAAG;MACpB,IAAI,CAAClH,iBAAiB,GAAG,IAAI,CAACzB,SAAS,CAACyB,iBAAiB;IAC3D,CAAC,CAAC,CAAC;EACL;CACD;AA1ZU6H,UAAA,EAARjL,KAAK,EAAE,C,gDAAoB;AACnBiL,UAAA,EAARjL,KAAK,EAAE,C,oDAAwB;AACvBiL,UAAA,EAARjL,KAAK,EAAE,C,wDAAuB;AACtBiL,UAAA,EAARjL,KAAK,EAAE,C,iDAAe;AACdiL,UAAA,EAARjL,KAAK,EAAE,C,oDAAwC;AALrCW,kBAAkB,GAAAsK,UAAA,EAP9BlL,SAAS,CAAC;EACTmL,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,6BAA6B,CAAC;EAC1CC,SAAS,EAAE,CAAC5K,aAAa,EAAEH,oBAAoB;CAEhD,CAAC,C,EACWK,kBAAkB,CA2Z9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}