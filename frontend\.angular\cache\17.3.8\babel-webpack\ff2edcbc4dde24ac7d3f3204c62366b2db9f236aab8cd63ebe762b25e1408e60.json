{"ast": null, "code": "import { MODE, URLS } from './constants';\nimport { getVersionedUrl, loadModule } from './utils';\nimport { RevolutPaymentsVersionLoader } from './versionLoader';\nvar loadedUpsellInstance = null;\nexport function RevolutUpsellLoader(token, mode, locale) {\n  if (mode === void 0) {\n    mode = RevolutUpsellLoader.mode;\n  }\n  if (loadedUpsellInstance) {\n    var instance = loadedUpsellInstance({\n      publicToken: token,\n      locale: locale\n    });\n    return Promise.resolve(instance);\n  }\n  return RevolutPaymentsVersionLoader(mode).then(function (version) {\n    return loadRevolutUpsell(version, token, mode, locale);\n  });\n}\nfunction loadRevolutUpsell(version, token, mode, locale) {\n  return loadModule({\n    src: getVersionedUrl(URLS[mode].upsell, version),\n    id: 'revolut-upsell',\n    name: 'RevolutUpsell'\n  }).then(function (scriptElement) {\n    if (typeof RevolutUpsell === 'function') {\n      loadedUpsellInstance = RevolutUpsell;\n      delete window.RevolutUpsell;\n      return loadedUpsellInstance({\n        publicToken: token,\n        locale: locale\n      });\n    } else {\n      document.head.removeChild(scriptElement);\n      throw new Error(\"'RevolutUpsell' failed to load: RevolutUpsell is not a function\");\n    }\n  });\n}\nRevolutUpsellLoader.mode = MODE.PRODUCTION;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}