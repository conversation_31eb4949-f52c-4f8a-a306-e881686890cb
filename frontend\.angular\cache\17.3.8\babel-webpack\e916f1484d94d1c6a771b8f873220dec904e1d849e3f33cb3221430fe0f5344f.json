{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/button\";\nfunction ForgotComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1, \"Passwords do not much\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let ForgotComponent = /*#__PURE__*/(() => {\n  class ForgotComponent {\n    constructor(activatedRoute, authService, router, toastService) {\n      this.activatedRoute = activatedRoute;\n      this.authService = authService;\n      this.router = router;\n      this.toastService = toastService;\n      this.confirmError = false;\n      this.resetEmail = \"\";\n      this.resetCode = \"\";\n      this.ismyTextFieldType = {\n        email: false,\n        password: false,\n        confirm: false\n      };\n    }\n    ngOnInit() {\n      this.activatedRoute.queryParams.pipe(take(1)).subscribe(params => {\n        this.resetEmail = params.email;\n        this.resetCode = params.code;\n      });\n    }\n    onSignup(form) {\n      var _this = this;\n      this.confirmError = this.confirmPassword(form.value.password, form.value.confirm);\n      if (form.invalid || this.confirmError) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Form is invalid. Password must contain at least one capital letter, one special case (e.g. @), and one number.'\n        });\n        return;\n      }\n      this.authService.confirmForgot(this.resetEmail, this.resetCode, form.value.password).subscribe( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (response) {\n          _this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'The password reset was successful.'\n          });\n          _this.router.navigateByUrl('/auth', {\n            replaceUrl: true\n          });\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    }\n    confirmPassword(password, confirm) {\n      return password !== confirm;\n    }\n    togglemyPasswordFieldType(key) {\n      this.ismyTextFieldType[key] = !this.ismyTextFieldType[key];\n    }\n    getKeyValueFieldType(key) {\n      return this.ismyTextFieldType[key];\n    }\n    static #_ = this.ɵfac = function ForgotComponent_Factory(t) {\n      return new (t || ForgotComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i3.ToastService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotComponent,\n      selectors: [[\"app-forgot\"]],\n      decls: 22,\n      vars: 5,\n      consts: [[\"signupForm\", \"ngForm\"], [\"passwordInput\", \"ngModel\"], [1, \"h-full\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"flex-column\", \"md:flex-row\"], [\"src\", \"/assets/icons/security.png\", \"alt\", \"security\"], [1, \"forgot\", 3, \"submit\"], [1, \"input-field\"], [1, \"input-element-title\"], [1, \"p-input-icon-right\", 2, \"width\", \"100%\"], [1, \"pi\", 2, \"margin-top\", \"-2px\", 3, \"click\", \"ngClass\"], [\"name\", \"password\", \"ngModel\", \"\", \"placeholder\", \"Password\", \"required\", \"\", \"pattern\", \"^(?=.*[A-Z])(?=.*[0-9]).*$\", 1, \"input-element\", 3, \"type\"], [2, \"font-size\", \"12px\", \"margin-top\", \"10px\"], [1, \"input-field\", \"mb-3\"], [\"name\", \"confirm\", \"pattern\", \"^(?=.*[A-Z])(?=.*[0-9]).*$\", \"ngModel\", \"\", \"type\", \"password\", \"placeholder\", \"Confirm Password\", \"required\", \"\", 1, \"input-element\", 3, \"type\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"submit\", \"styleClass\", \"bg-primary\", \"label\", \"Reset Password\", 1, \"sign-in-btn\", \"hvr-glow\"], [1, \"input-error\"]],\n      template: function ForgotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵelement(1, \"img\", 3);\n          i0.ɵɵelementStart(2, \"form\", 4, 0);\n          i0.ɵɵlistener(\"submit\", function ForgotComponent_Template_form_submit_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const signupForm_r2 = i0.ɵɵreference(3);\n            return i0.ɵɵresetView(ctx.onSignup(signupForm_r2));\n          });\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6);\n          i0.ɵɵtext(6, \"Password *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 7)(8, \"i\", 8);\n          i0.ɵɵlistener(\"click\", function ForgotComponent_Template_i_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.togglemyPasswordFieldType(\"password\"));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"input\", 9, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10);\n          i0.ɵɵtext(12, \"Password must contain at least one capital letter, one special case (e.g. @), and one number.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 6);\n          i0.ɵɵtext(15, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\", 7)(17, \"i\", 8);\n          i0.ɵɵlistener(\"click\", function ForgotComponent_Template_i_click_17_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.togglemyPasswordFieldType(\"confirm\"));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 12, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, ForgotComponent_div_20_Template, 2, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"button\", 14);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", ctx.getKeyValueFieldType(\"password\") ? \"pi-eye\" : \"pi-eye-slash\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"type\", ctx.getKeyValueFieldType(\"password\") ? \"text\" : \"password\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", ctx.getKeyValueFieldType(\"confirm\") ? \"pi-eye\" : \"pi-eye-slash\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"type\", ctx.getKeyValueFieldType(\"confirm\") ? \"text\" : \"password\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.confirmError);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.PatternValidator, i5.NgModel, i5.NgForm, i6.ButtonDirective],\n      styles: [\"@media screen and (max-width: 1024px){  .app-content{margin-top:0!important}}  .app-content.auth-route{margin-top:0!important;margin-bottom:0!important}.title[_ngcontent-%COMP%]{font-size:clamp(1.5rem,.47vw + 1.41rem,1.88rem)!important;text-align:center}@media only screen and (min-width: 768px){.title[_ngcontent-%COMP%]{text-align:left}}.auth[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border-radius:10px;overflow:hidden;width:100%}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{width:80%;display:flex;justify-content:center;align-items:center}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100px}@media screen and (max-width: 1366px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{width:100%}}@media screen and (max-width: 1024px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{position:relative;flex-direction:column}}@media screen and (max-width: 768px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{flex-direction:column-reverse}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]{height:500px;width:42%;border-top-left-radius:28px;border-bottom-left-radius:28px;background-image:linear-gradient(179deg,#9baaff -6%,#152caf 106%);display:flex;align-items:center;flex-direction:column;color:var(--white);position:relative;left:0}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side.login-is-open[_ngcontent-%COMP%]{background:linear-gradient(#927ffa,#5a5fe9)}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side.has-animated[_ngcontent-%COMP%]{transition:all .3s linear}@media screen and (max-width: 1024px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]{flex-direction:column-reverse;align-items:center;justify-content:flex-start;height:-moz-fit-content;height:fit-content;position:relative;border-radius:0;bottom:0;width:100%}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]   .left-side-content[_ngcontent-%COMP%]{height:100%;flex-direction:column;display:flex;justify-content:space-between;padding-top:32px;padding-bottom:40px;align-items:center;font-size:15px;font-weight:400;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:normal;text-align:center}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]   .left-side-content[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-size:16px}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]{height:500px;width:60%;border-top-right-radius:28px;border-bottom-right-radius:28px;color:var(--main-color);background-color:#fff;position:relative}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side.has-animated[_ngcontent-%COMP%]{transition:all .3s linear}@media screen and (max-width: 768px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]{width:90%;height:auto}}@media screen and (min-width: 1024px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]{right:0}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%]{position:relative;width:100%}@media screen and (max-width: 768px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%]{margin-top:30px;width:90%}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:30px}.input-field[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:30px}.input-field[_ngcontent-%COMP%]{margin-top:.75rem;font-size:1.125rem;box-sizing:border-box}.input-field[_ngcontent-%COMP%]   .input-element-title[_ngcontent-%COMP%]{color:var(--main-color);font-size:clamp(.88rem,.16vw + .84rem,1rem);font-weight:700;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:normal;text-align:left}.input-field[_ngcontent-%COMP%]   .input-element[_ngcontent-%COMP%]{margin-top:10px;color:var(--main-color);width:100%;box-sizing:border-box;border:1px solid var(--my-gray-2);border-radius:8px;padding:11px;font-size:18px}.checkbox-bg[_ngcontent-%COMP%]{background-size:cover;background-repeat:no-repeat;background-position:center;height:44px;align-items:center;display:flex;justify-content:center;border-radius:8px}.teacher-bg[_ngcontent-%COMP%]{background:url(/assets/images/auth-btn-reg-teacher.png) no-repeat center center;background-size:cover}.student-bg[_ngcontent-%COMP%]{background:url(/assets/images/auth-btn-reg-student.png) no-repeat center center;background-size:cover}[_nghost-%COMP%]  .terms-checkbox .p-checkbox{width:18px;height:18px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label{font-size:16px;cursor:pointer}@media screen and (max-width: 768px){[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label{font-size:14px}}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before{top:1px;left:-5px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box{border-radius:50px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight{border-radius:50px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon{transform:scale(1.3)}[_nghost-%COMP%]  .terms-checkbox .p-component .p-checkbox-box{width:18px!important;height:18px!important}[_nghost-%COMP%]  .teacher-checkbox{height:32px;color:#fff}@media screen and (max-width: 768px){[_nghost-%COMP%]  .teacher-checkbox{padding-left:6px}}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox{width:28px;height:28px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox-label{font-size:16px;cursor:pointer}@media screen and (max-width: 768px){[_nghost-%COMP%]  .teacher-checkbox .p-checkbox-label{font-size:14px}}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before{top:0;left:-3px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box{background:transparent;border:1px solid #ffffff;border-radius:50px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box.p-highlight{background:transparent;border-color:#fff;border-radius:50px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon{transform:scale(2.5)}[_nghost-%COMP%]  .teacher-checkbox .p-component .p-checkbox-box{width:25px!important;height:25px!important}.auth-btn[_ngcontent-%COMP%]{border:1px solid white;border-radius:10px;padding:10px;font-size:20px;width:250px;margin:0 auto;cursor:pointer;transition:all .2s ease-in;box-sizing:border-box;white-space:normal;height:50px;color:#fff}.auth-btn[_ngcontent-%COMP%]:hover{background:#fff!important;color:#746ef1!important}.sign-in-btn.p-button[_ngcontent-%COMP%]{padding-right:60px;padding-left:60px;border-radius:8px;background:#2e3d90}.p-button.teacher-bg[_ngcontent-%COMP%]:enabled:hover{background:url(/assets/images/auth-btn-reg-teacher.png)!important}.p-button.student-bg[_ngcontent-%COMP%]:enabled:hover{background:url(/assets/images/auth-btn-reg-student.png) no-repeat center center;background-size:cover}.auth-btn-right[_ngcontent-%COMP%]{width:100%;color:#fff;border-radius:8px;font-size:clamp(1rem,.16vw + .97rem,1.13rem)}.auth-btn-right[_ngcontent-%COMP%]   .teacher-bg[_ngcontent-%COMP%]{background:url(/assets/images/auth-btn-reg-teacher.png)!important}.auth-btn[_ngcontent-%COMP%]:hover{background:#fff;color:var(--main-color)}.alternative[_ngcontent-%COMP%]{font-size:clamp(.88rem,.16vw + .84rem,1rem);margin-top:15px;display:flex}.alternative-btn[_ngcontent-%COMP%]{font-weight:700;cursor:pointer;white-space:break-spaces}.alternative-btn[_ngcontent-%COMP%]:hover{text-decoration:underline}.spinner[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%)}.register-success[_ngcontent-%COMP%]{color:var(--light-purple);font-weight:700;display:flex;align-items:center;font-size:16px;padding:15px 0}.register-success[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:30px;margin-right:15px}.form-container[_ngcontent-%COMP%]{background-color:#fff;top:0;padding-bottom:1rem}@media only screen and (min-width: 768px){.form-container[_ngcontent-%COMP%]{height:100%;transition:all .6s ease-in-out;display:flex;align-items:center;justify-content:start;flex-direction:column;position:absolute;padding-bottom:0}}.sign-up-container[_ngcontent-%COMP%]{left:0;width:100%;z-index:2;display:block}@media only screen and (min-width: 768px){.sign-up-container[_ngcontent-%COMP%]{display:block;width:60%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-up-container[_ngcontent-%COMP%]{display:none}@media only screen and (min-width: 768px){.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-up-container[_ngcontent-%COMP%]{display:block;transform:translate(100%)}}.sign-in-container[_ngcontent-%COMP%]{opacity:0;z-index:1;display:none}@media only screen and (min-width: 768px){.sign-in-container[_ngcontent-%COMP%]{display:block;left:-20%;width:60%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%]{opacity:1;z-index:5;display:block}@media only screen and (min-width: 768px){.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%]{transform:translate(100%)}}@keyframes _ngcontent-%COMP%_show{0%,49.99%{opacity:0;z-index:1}50%,to{opacity:1;z-index:5}}.overlay-container[_ngcontent-%COMP%]{position:relative;top:0;width:100%;height:300px;overflow:hidden;transition:transform .6s ease-in-out;z-index:100}@media only screen and (min-width: 768px){.overlay-container[_ngcontent-%COMP%]{position:absolute;top:0;left:60%;width:50%;height:100%;overflow:hidden;transition:transform .6s ease-in-out}}@media only screen and (min-width: 768px){.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-container[_ngcontent-%COMP%]{transform:translate(-150%);left:60%;width:40%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#8a9af5,#8091ef 10%,#7786e9,#6d7de3,#6071dc,#5766d6,#4e5dd0 65%,#4454ca 77%,#374ac3 88%,#2a40bb)}.overlay[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#8579f6,#8075f5 10%,#7d72f3,#776ef2,#716af0,#6c68ee,#6b68ed 65%,#6565ec 77%,#6163ea 88%,#595ee8);background-repeat:no-repeat;background-size:cover;background-position:0 0;color:#fff;position:relative;left:-100%;height:100%;width:200%;transform:translate(0);transition:transform .6s ease-in-out}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{transform:translate(50%)}.overlay-panel[_ngcontent-%COMP%]{box-sizing:border-box;position:absolute;display:flex;align-items:center;justify-content:center;flex-direction:column;text-align:center;top:0;height:100%;width:50%;transform:translate(0);transition:transform .6s ease-in-out}@media only screen and (min-width: 768px){.overlay-panel[_ngcontent-%COMP%]{width:40%}}.overlay-left[_ngcontent-%COMP%]{display:none;transform:translate(-20%)}@media only screen and (min-width: 768px){.overlay-left[_ngcontent-%COMP%]{display:flex;transform:unset;width:60%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-left[_ngcontent-%COMP%]{position:relative;display:block;display:flex;transform:unset;width:50%}.overlay-right[_ngcontent-%COMP%]{left:50%;right:0;transform:translate(0)}@media only screen and (min-width: 768px){.overlay-right[_ngcontent-%COMP%]{left:initial;right:0;transform:translate(-25%)}}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-right[_ngcontent-%COMP%]{transform:translate(20%)}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}[_nghost-%COMP%]{height:100%;display:flex;align-items:CENTER;justify-content:center;margin:auto;width:100%;padding:0 20px;position:relative}@media only screen and (min-width: 768px){[_nghost-%COMP%]{position:absolute}}.forgot[_ngcontent-%COMP%]{margin:auto}\"]\n    });\n  }\n  return ForgotComponent;\n})();", "map": {"version": 3, "names": ["take", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ForgotComponent", "constructor", "activatedRoute", "authService", "router", "toastService", "confirmError", "resetEmail", "resetCode", "ismyTextFieldType", "email", "password", "confirm", "ngOnInit", "queryParams", "pipe", "subscribe", "params", "code", "onSignup", "form", "_this", "confirmPassword", "value", "invalid", "setShowToastmessage", "severity", "summary", "detail", "confirmForgot", "_ref", "_asyncToGenerator", "response", "navigateByUrl", "replaceUrl", "_x", "apply", "arguments", "togglemyPasswordFieldType", "key", "getKeyValueFieldType", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "AuthService", "Router", "i3", "ToastService", "_2", "selectors", "decls", "vars", "consts", "template", "ForgotComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ForgotComponent_Template_form_submit_2_listener", "ɵɵrestoreView", "_r1", "signupForm_r2", "ɵɵreference", "ɵɵresetView", "ForgotComponent_Template_i_click_8_listener", "ForgotComponent_Template_i_click_17_listener", "ɵɵtemplate", "ForgotComponent_div_20_Template", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\auth\\forgot\\forgot.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\auth\\forgot\\forgot.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NgForm } from '@angular/forms';\r\nimport { ActivatedRoute, Params, Router } from '@angular/router';\r\nimport { take } from 'rxjs/operators';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\n\r\ntype TextFieldType = \"email\" | \"password\" | \"confirm\";\r\n@Component({\r\n  selector: 'app-forgot',\r\n  templateUrl: './forgot.component.html',\r\n  styleUrls: ['./forgot.component.scss']\r\n})\r\nexport class ForgotComponent implements OnInit {\r\n  confirmError: boolean = false;\r\n  resetEmail: string = \"\";\r\n  resetCode: string = \"\";\r\n  ismyTextFieldType: Record<TextFieldType, boolean> = {\r\n    email: false,\r\n    password: false,\r\n    confirm: false,\r\n  };\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private toastService: ToastService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.activatedRoute.queryParams.pipe(take(1)).subscribe((params: Params) => {\r\n      this.resetEmail = params.email;\r\n      this.resetCode = params.code\r\n    });\r\n  }\r\n\r\n  onSignup(form: NgForm) {\r\n    this.confirmError = this.confirmPassword(form.value.password, form.value.confirm);\r\n    if (form.invalid || this.confirmError) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Form is invalid. Password must contain at least one capital letter, one special case (e.g. @), and one number.'\r\n      });\r\n      return;\r\n    }\r\n    this.authService.confirmForgot(this.resetEmail, this.resetCode, form.value.password)\r\n    .subscribe(async response => {\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'The password reset was successful.'\r\n      });\r\n      this.router.navigateByUrl('/auth', { replaceUrl: true })\r\n\r\n    })\r\n  }\r\n\r\n  confirmPassword(password: string, confirm: string) {\r\n    return password !== confirm\r\n  }\r\n\r\n  togglemyPasswordFieldType(key: TextFieldType): void {\r\n    this.ismyTextFieldType[key] = !this.ismyTextFieldType[key];\r\n  }\r\n\r\n  getKeyValueFieldType(key: TextFieldType): boolean {\r\n    return this.ismyTextFieldType[key];\r\n  }\r\n}\r\n", "<div class=\"h-full w-full flex align-items-center justify-content-center flex-column md:flex-row\">\r\n\r\n    <img src=\"/assets/icons/security.png\" alt=\"security\" />\r\n<form (submit)=\"onSignup(signupForm)\" #signupForm=\"ngForm\" class=\"forgot\">\r\n\r\n    <div class=\"input-field\">\r\n        <div class=\"input-element-title\">Password *</div>\r\n        <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n          <i class=\"pi\" (click)=\"togglemyPasswordFieldType('password')\"  [ngClass]=\"getKeyValueFieldType('password')? 'pi-eye': 'pi-eye-slash'\" \r\n          style=\"margin-top:-2px;\"></i>\r\n          <input name=\"password\"  [type]=\"getKeyValueFieldType('password') ? 'text' : 'password'\" ngModel placeholder=\"Password\" #passwordInput=\"ngModel\" required\r\n                    class=\"input-element\"  pattern=\"^(?=.*[A-Z])(?=.*[0-9]).*$\" />\r\n      </span> \r\n      <div style=\"font-size:12px; margin-top:10px;\">Password must contain at least one capital letter, one\r\n        special case (e.g. &#64;), and one number.</div>\r\n\r\n      </div>\r\n\r\n    <div class=\"input-field mb-3\">\r\n        <div class=\"input-element-title\">Confirm Password</div>\r\n\r\n        <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n            <i class=\"pi\" (click)=\"togglemyPasswordFieldType('confirm')\"  [ngClass]=\"getKeyValueFieldType('confirm')? 'pi-eye': 'pi-eye-slash'\" \r\n            style=\"margin-top:-2px;\"></i>\r\n            <input name=\"confirm\" pattern=\"^(?=.*[A-Z])(?=.*[0-9]).*$\" \r\n            [type]=\"getKeyValueFieldType('confirm') ? 'text' : 'password'\" ngModel type=\"password\" placeholder=\"Confirm Password\" #passwordInput=\"ngModel\" required\r\n            class=\"input-element\" />\r\n        </span>\r\n     \r\n        <div *ngIf=\"confirmError\" class=\"input-error\">Passwords do not much</div>\r\n    </div>\r\n    <button pButton type=\"submit\" styleClass=\"bg-primary\" label=\"Reset Password\" class=\"block relative mt-3\" \r\n          class=\"sign-in-btn hvr-glow\"></button>\r\n    <!-- <input type=\"submit\" value=\"Reset Password\" class=\"auth-btn-right hvr-glow\" /> -->\r\n</form>\r\n</div>"], "mappings": ";AAGA,SAASA,IAAI,QAAQ,gBAAgB;;;;;;;;;;IC0B7BC,EAAA,CAAAC,cAAA,cAA8C;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADhBjF,WAAaC,eAAe;EAAtB,MAAOA,eAAe;IAS1BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;MAH1B,KAAAH,cAAc,GAAdA,cAAc;MACd,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MAZtB,KAAAC,YAAY,GAAY,KAAK;MAC7B,KAAAC,UAAU,GAAW,EAAE;MACvB,KAAAC,SAAS,GAAW,EAAE;MACtB,KAAAC,iBAAiB,GAAmC;QAClDC,KAAK,EAAE,KAAK;QACZC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE;OACV;IAMG;IAEJC,QAAQA,CAAA;MACN,IAAI,CAACX,cAAc,CAACY,WAAW,CAACC,IAAI,CAACpB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqB,SAAS,CAAEC,MAAc,IAAI;QACzE,IAAI,CAACV,UAAU,GAAGU,MAAM,CAACP,KAAK;QAC9B,IAAI,CAACF,SAAS,GAAGS,MAAM,CAACC,IAAI;MAC9B,CAAC,CAAC;IACJ;IAEAC,QAAQA,CAACC,IAAY;MAAA,IAAAC,KAAA;MACnB,IAAI,CAACf,YAAY,GAAG,IAAI,CAACgB,eAAe,CAACF,IAAI,CAACG,KAAK,CAACZ,QAAQ,EAAES,IAAI,CAACG,KAAK,CAACX,OAAO,CAAC;MACjF,IAAIQ,IAAI,CAACI,OAAO,IAAI,IAAI,CAAClB,YAAY,EAAE;QACrC,IAAI,CAACD,YAAY,CAACoB,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MACA,IAAI,CAACzB,WAAW,CAAC0B,aAAa,CAAC,IAAI,CAACtB,UAAU,EAAE,IAAI,CAACC,SAAS,EAAEY,IAAI,CAACG,KAAK,CAACZ,QAAQ,CAAC,CACnFK,SAAS;QAAA,IAAAc,IAAA,GAAAC,iBAAA,CAAC,WAAMC,QAAQ,EAAG;UAE1BX,KAAI,CAAChB,YAAY,CAACoB,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACFP,KAAI,CAACjB,MAAM,CAAC6B,aAAa,CAAC,OAAO,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE,CAAC;QAE1D,CAAC;QAAA,iBAAAC,EAAA;UAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IACJ;IAEAf,eAAeA,CAACX,QAAgB,EAAEC,OAAe;MAC/C,OAAOD,QAAQ,KAAKC,OAAO;IAC7B;IAEA0B,yBAAyBA,CAACC,GAAkB;MAC1C,IAAI,CAAC9B,iBAAiB,CAAC8B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC9B,iBAAiB,CAAC8B,GAAG,CAAC;IAC5D;IAEAC,oBAAoBA,CAACD,GAAkB;MACrC,OAAO,IAAI,CAAC9B,iBAAiB,CAAC8B,GAAG,CAAC;IACpC;IAAC,QAAAE,CAAA,G;uBAxDUzC,eAAe,EAAAJ,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlD,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAI,MAAA,GAAAnD,EAAA,CAAA8C,iBAAA,CAAAM,EAAA,CAAAC,YAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAflD,eAAe;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCb5B7D,EAAA,CAAAC,cAAA,aAAkG;UAE9FD,EAAA,CAAA+D,SAAA,aAAuD;UAC3D/D,EAAA,CAAAC,cAAA,iBAA0E;UAApED,EAAA,CAAAgE,UAAA,oBAAAC,gDAAA;YAAAjE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;YAAA,MAAAC,aAAA,GAAApE,EAAA,CAAAqE,WAAA;YAAA,OAAArE,EAAA,CAAAsE,WAAA,CAAUR,GAAA,CAAAvC,QAAA,CAAA6C,aAAA,CAAoB;UAAA,EAAC;UAG7BpE,EADJ,CAAAC,cAAA,aAAyB,aACY;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAE/CH,EADF,CAAAC,cAAA,cAAsD,WAE3B;UADXD,EAAA,CAAAgE,UAAA,mBAAAO,4CAAA;YAAAvE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;YAAA,OAAAnE,EAAA,CAAAsE,WAAA,CAASR,GAAA,CAAApB,yBAAA,CAA0B,UAAU,CAAC;UAAA,EAAC;UACpC1C,EAAA,CAAAG,YAAA,EAAI;UAC7BH,EAAA,CAAA+D,SAAA,kBACwE;UAC5E/D,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,eAA8C;UAAAD,EAAA,CAAAE,MAAA,qGACF;UAE5CF,EAF4C,CAAAG,YAAA,EAAM,EAE5C;UAGJH,EADJ,CAAAC,cAAA,eAA8B,cACO;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGnDH,EADJ,CAAAC,cAAA,eAAsD,YAEzB;UADXD,EAAA,CAAAgE,UAAA,mBAAAQ,6CAAA;YAAAxE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;YAAA,OAAAnE,EAAA,CAAAsE,WAAA,CAASR,GAAA,CAAApB,yBAAA,CAA0B,SAAS,CAAC;UAAA,EAAC;UACnC1C,EAAA,CAAAG,YAAA,EAAI;UAC7BH,EAAA,CAAA+D,SAAA,oBAEwB;UAC5B/D,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAyE,UAAA,KAAAC,+BAAA,kBAA8C;UAClD1E,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA+D,SAAA,kBAC4C;UAGhD/D,EADA,CAAAG,YAAA,EAAO,EACD;;;UA3BmEH,EAAA,CAAA2E,SAAA,GAAsE;UAAtE3E,EAAA,CAAA4E,UAAA,YAAAd,GAAA,CAAAlB,oBAAA,yCAAsE;UAE7G5C,EAAA,CAAA2E,SAAA,EAA+D;UAA/D3E,EAAA,CAAA4E,UAAA,SAAAd,GAAA,CAAAlB,oBAAA,mCAA+D;UAYvB5C,EAAA,CAAA2E,SAAA,GAAqE;UAArE3E,EAAA,CAAA4E,UAAA,YAAAd,GAAA,CAAAlB,oBAAA,wCAAqE;UAGnI5C,EAAA,CAAA2E,SAAA,EAA8D;UAA9D3E,EAAA,CAAA4E,UAAA,SAAAd,GAAA,CAAAlB,oBAAA,kCAA8D;UAI5D5C,EAAA,CAAA2E,SAAA,GAAkB;UAAlB3E,EAAA,CAAA4E,UAAA,SAAAd,GAAA,CAAApD,YAAA,CAAkB;;;;;;;SDhBnBN,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}