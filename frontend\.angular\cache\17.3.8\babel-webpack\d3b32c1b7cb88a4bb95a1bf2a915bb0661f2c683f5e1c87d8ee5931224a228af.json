{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { Injector, computed, inject, input, signal, viewChild } from '@angular/core';\nimport { BehaviorSubject, of, switchMap } from \"rxjs\";\nimport { AuthService } from \"src/app/core/services/auth.service\";\nimport { ChatService } from \"src/app/core/services/chat.service\";\nimport { ClassroomService } from \"src/app/core/services/classroom.service\";\nimport { UserService } from \"src/app/core/services/user.service\";\nimport { SubSink } from \"subsink\";\nimport { serverTimestamp, Timestamp } from 'firebase/firestore';\nimport { animate, transition, trigger } from \"@angular/animations\";\nimport { ButtonModule } from \"primeng/button\";\nimport { GeneralService } from \"src/app/core/services/general.service\";\nimport { SharedModule } from \"src/app/shared/shared.module\";\nimport { DialogService } from \"primeng/dynamicdialog\";\nimport { ChatUploadFileDialogComponent } from \"./components/chat-upload-file-dialog/chat-upload-file-dialog.component\";\nimport { LibraryService } from \"src/app/core/services/library.service\";\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { HttpHeaders, HttpEventType, HttpClient } from \"@angular/common/http\";\nimport { environment } from \"src/environments/environment\";\nimport { InputTextModule } from \"primeng/inputtext\";\nimport { FormControl } from \"@angular/forms\";\nimport { ToastService } from \"src/app/core/services/toast.service\";\nimport { ChatMessagesDisplayComponent } from \"./components/chat-messages-display/chat-messages-display.component\";\nimport { BadgeModule } from \"primeng/badge\";\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { SpeedDialModule } from 'primeng/speeddial';\nimport { toObservable } from \"@angular/core/rxjs-interop\";\nimport { TooltipModule } from \"primeng/tooltip\";\nimport { TabViewModule } from \"primeng/tabview\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"primeng/button\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/tooltip\";\nimport * as i5 from \"primeng/fileupload\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/badge\";\nconst _c0 = [\"scrollMe\"];\nconst _c1 = [\"autoResizeTextArea\"];\nconst _c2 = [\"textareaWrapper\"];\nconst _c3 = [\"editForbitDialog\"];\nconst ChatComponent_Defer_2_DepsFn = () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, ChatMessagesDisplayComponent];\nconst _c4 = (a0, a1) => ({\n  \"col-12\": a0,\n  \"col-4\": a1\n});\nconst _c5 = a0 => ({\n  \"pointer-events-none\": a0\n});\nconst _c6 = (a0, a1) => ({\n  \"editMode\": a0,\n  \"showInnerChat\": a1\n});\nconst _c7 = a0 => ({\n  \"border-round-3xl\": a0\n});\nconst _c8 = (a0, a1) => ({\n  \"pointer-events-none\": a0,\n  \"hidden\": a1\n});\nconst _c9 = a0 => ({\n  \"selected\": a0\n});\nconst _c10 = a0 => ({\n  chatGroup: a0\n});\nfunction ChatComponent_Defer_0_Conditional_0_div_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_div_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, ChatComponent_Defer_0_Conditional_0_div_9_ng_container_2_Template, 1, 0, \"ng-container\", 24)(3, ChatComponent_Defer_0_Conditional_0_div_9_ng_container_3_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    const personSearchTemplate_r2 = i0.ɵɵreference(10);\n    const personBoxTemplate_r3 = i0.ɵɵreference(12);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c4, ctx_r0.isTabletOrMobile, !ctx_r0.isTabletOrMobile));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", personSearchTemplate_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", personBoxTemplate_r3);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_div_10_Conditional_6_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_div_10_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ChatComponent_Defer_0_Conditional_0_div_10_Conditional_6_ng_container_0_Template, 1, 0, \"ng-container\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(4);\n    const emptyMessagesTemplate_r5 = i0.ɵɵreference(14);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", emptyMessagesTemplate_r5);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_div_10_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, ChatComponent_Defer_0_Conditional_0_div_10_ng_container_1_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementStart(2, \"div\", 26, 6)(4, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ChatComponent_Defer_0_Conditional_0_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.loadMoreMessages(ctx_r0.currentChatId));\n    });\n    i0.ɵɵtext(5, \"Load previous messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ChatComponent_Defer_0_Conditional_0_div_10_Conditional_6_Template, 1, 1, \"ng-container\");\n    i0.ɵɵelementStart(7, \"app-chat-messages-display\", 28);\n    i0.ɵɵlistener(\"deleteMessageSelected\", function ChatComponent_Defer_0_Conditional_0_div_10_Template_app_chat_messages_display_deleteMessageSelected_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.deleteMessage($event));\n    })(\"editMessageSelected\", function ChatComponent_Defer_0_Conditional_0_div_10_Template_app_chat_messages_display_editMessageSelected_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.editMessage($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ChatComponent_Defer_0_Conditional_0_div_10_ng_container_8_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    const chatPersonTemplate_r6 = i0.ɵɵreference(6);\n    const inputTextTemplate_r7 = i0.ɵɵreference(8);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", chatPersonTemplate_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c5, ctx_r0.editMessageMode()));\n    i0.ɵɵadvance(4);\n    i0.ɵɵconditional(6, ctx_r0.messages().length === 0 ? 6 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"messages\", ctx_r0.messages())(\"chatsList\", ctx_r0.chatsList());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputTextTemplate_r7);\n  }\n}\nfunction ChatComponent_Defer_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"div\", 15);\n    i0.ɵɵelementStart(4, \"span\", 16)(5, \"span\", 17);\n    i0.ɵɵtext(6, \"Chat\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, ChatComponent_Defer_0_Conditional_0_div_9_Template, 4, 6, \"div\", 20)(10, ChatComponent_Defer_0_Conditional_0_div_10_Template, 9, 8, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showLeftSideMobile());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showRightSideMobile());\n  }\n}\nfunction ChatComponent_Defer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ChatComponent_Defer_0_Conditional_0_Template, 11, 2, \"div\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, !ctx_r0.bubbleMode() ? 0 : -1);\n  }\n}\nfunction ChatComponent_DeferPlaceholder_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_11_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_11_Conditional_6_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_11_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ChatComponent_Conditional_4_div_3_div_11_Conditional_6_ng_container_0_Template, 1, 0, \"ng-container\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(4);\n    const personBoxTemplate_r3 = i0.ɵɵreference(12);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", personBoxTemplate_r3);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"div\", 54)(3, \"div\", 55);\n    i0.ɵɵtemplate(4, ChatComponent_Conditional_4_div_3_div_11_ng_container_4_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56);\n    i0.ɵɵtemplate(6, ChatComponent_Conditional_4_div_3_div_11_Conditional_6_Template, 1, 1, \"ng-container\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    const personSearchTemplate_r2 = i0.ɵɵreference(10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", personSearchTemplate_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(6, !ctx_r0.showInnerChat ? 6 : -1);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_12_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_12_Conditional_10_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_12_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ChatComponent_Conditional_4_div_3_div_12_Conditional_10_ng_container_0_Template, 1, 0, \"ng-container\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(4);\n    const emptyMessagesTemplate_r5 = i0.ɵɵreference(14);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", emptyMessagesTemplate_r5);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58)(2, \"div\", 59);\n    i0.ɵɵelement(3, \"span\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵelement(5, \"i\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 63);\n    i0.ɵɵtemplate(7, ChatComponent_Conditional_4_div_3_div_12_ng_container_7_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementStart(8, \"div\", 64, 6);\n    i0.ɵɵtemplate(10, ChatComponent_Conditional_4_div_3_div_12_Conditional_10_Template, 1, 1, \"ng-container\");\n    i0.ɵɵelementStart(11, \"app-chat-messages-display\", 65);\n    i0.ɵɵlistener(\"editMessageSelected\", function ChatComponent_Conditional_4_div_3_div_12_Template_app_chat_messages_display_editMessageSelected_11_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.editMessage($event));\n    })(\"deleteMessageSelected\", function ChatComponent_Conditional_4_div_3_div_12_Template_app_chat_messages_display_deleteMessageSelected_11_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.deleteMessage($event));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    const chatPersonTemplate_r6 = i0.ɵɵreference(6);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", chatPersonTemplate_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c5, ctx_r0.editMessageMode()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(10, ctx_r0.messages().length === 0 ? 10 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"messages\", ctx_r0.messages())(\"chatsList\", ctx_r0.chatsList())(\"isBubbleMode\", ctx_r0.bubbleMode());\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_19_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"div\", 68);\n    i0.ɵɵtemplate(3, ChatComponent_Conditional_4_div_3_div_19_ng_container_3_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n    const inputTextTemplate_r7 = i0.ɵɵreference(8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", inputTextTemplate_r7);\n  }\n}\nfunction ChatComponent_Conditional_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"span\", 35);\n    i0.ɵɵtext(3, \"Chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"a\", 37)(6, \"i\", 38);\n    i0.ɵɵlistener(\"click\", function ChatComponent_Conditional_4_div_3_Template_i_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleChat());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 39);\n    i0.ɵɵlistener(\"click\", function ChatComponent_Conditional_4_div_3_Template_span_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleChat());\n    });\n    i0.ɵɵelement(8, \"i\", 40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 41)(10, \"div\", 42);\n    i0.ɵɵtemplate(11, ChatComponent_Conditional_4_div_3_div_11_Template, 7, 2, \"div\", 43)(12, ChatComponent_Conditional_4_div_3_div_12_Template, 12, 8, \"div\", 44);\n    i0.ɵɵelementStart(13, \"div\", 45)(14, \"div\", 46)(15, \"span\", 47);\n    i0.ɵɵelement(16, \"i\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"div\", 50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(19, ChatComponent_Conditional_4_div_3_div_19_Template, 4, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@scaleToggle\", ctx_r0.chatBoxState);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"href\", \"/#/chat\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c6, ctx_r0.editMessageMode(), ctx_r0.showInnerChat));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.showInnerChat);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showInnerChat);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showInnerChat);\n  }\n}\nfunction ChatComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function ChatComponent_Conditional_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChat());\n    });\n    i0.ɵɵelement(1, \"i\", 30)(2, \"p-badge\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ChatComponent_Conditional_4_div_3_Template, 20, 9, \"div\", 32);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@scaleToggle\", ctx_r0.chatCircleState)(\"pTooltip\", \"Chat\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r0.chatService.unreadCount());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.chatBoxVisible);\n  }\n}\nfunction ChatComponent_ng_template_5_p_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 78);\n    i0.ɵɵlistener(\"click\", function ChatComponent_ng_template_5_p_button_3_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.showBackUsers());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_ng_template_5_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 75);\n  }\n}\nfunction ChatComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵtemplate(3, ChatComponent_ng_template_5_p_button_3_Template, 1, 0, \"p-button\", 72);\n    i0.ɵɵelementStart(4, \"div\", 73)(5, \"img\", 74);\n    i0.ɵɵlistener(\"error\", function ChatComponent_ng_template_5_Template_img_error_5_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ChatComponent_ng_template_5_Conditional_6_Template, 1, 0, \"span\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 76)(8, \"span\", 77);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTabletOrMobile || ctx_r0.bubbleMode());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.getChatGroupAvatarUrl(ctx_r0.activeUser)), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(6, ctx_r0.isUserOnline(ctx_r0.getChatGroupAspUserId(ctx_r0.activeUser)) ? 6 : -1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.activeUser == null ? null : ctx_r0.activeUser.chatName);\n  }\n}\nfunction ChatComponent_ng_template_7_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 92);\n    i0.ɵɵelement(2, \"i\", 93);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Edit Message\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"i\", 94);\n    i0.ɵɵlistener(\"click\", function ChatComponent_ng_template_7_Conditional_0_Template_i_click_5_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      ctx_r0.disableEditAndTextarea();\n      return i0.ɵɵresetView(ctx_r0.resetTextarea());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_ng_template_7_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ChatComponent_ng_template_7_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nfunction ChatComponent_ng_template_7_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ChatComponent_ng_template_7_ng_template_16_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 99);\n  }\n}\nfunction ChatComponent_ng_template_7_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-fileUpload\", 97);\n    i0.ɵɵlistener(\"onSelect\", function ChatComponent_ng_template_7_ng_template_16_Template_p_fileUpload_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onBasicUploadAuto($event));\n    });\n    i0.ɵɵtemplate(1, ChatComponent_ng_template_7_ng_template_16_ng_template_1_Template, 1, 0, \"ng-template\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"auto\", true);\n  }\n}\nfunction ChatComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, ChatComponent_ng_template_7_Conditional_0_Template, 6, 0, \"div\", 79);\n    i0.ɵɵelementStart(1, \"div\", 80, 7)(3, \"div\", 81)(4, \"div\", 82)(5, \"div\", 83);\n    i0.ɵɵtemplate(6, ChatComponent_ng_template_7_ng_container_6_Template, 2, 0, \"ng-container\", 84)(7, ChatComponent_ng_template_7_ng_template_7_Template, 1, 0, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 85)(10, \"li\")(11, \"div\", 86);\n    i0.ɵɵlistener(\"click\", function ChatComponent_ng_template_7_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openFileUploadDialog());\n    });\n    i0.ɵɵelement(12, \"img\", 87);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\")(14, \"div\", 88);\n    i0.ɵɵtemplate(15, ChatComponent_ng_template_7_ng_container_15_Template, 2, 0, \"ng-container\", 84)(16, ChatComponent_ng_template_7_ng_template_16_Template, 2, 1, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(18, \"textarea\", 89, 10);\n    i0.ɵɵlistener(\"input\", function ChatComponent_ng_template_7_Template_textarea_input_18_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.adjustParentHeight());\n    })(\"paste\", function ChatComponent_ng_template_7_Template_textarea_paste_18_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPaste($event));\n    })(\"focus\", function ChatComponent_ng_template_7_Template_textarea_focus_18_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onFocusTextarea());\n    })(\"click\", function ChatComponent_ng_template_7_Template_textarea_click_18_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const autoResizeTextArea_r16 = i0.ɵɵreference(19);\n      const ctx_r0 = i0.ɵɵnextContext();\n      autoResizeTextArea_r16.focus();\n      return i0.ɵɵresetView(ctx_r0.adjustParentHeight());\n    })(\"keydown.enter\", function ChatComponent_ng_template_7_Template_textarea_keydown_enter_18_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const autoResizeTextArea_r16 = i0.ɵɵreference(19);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.addNewMessage($event, autoResizeTextArea_r16));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 90);\n    i0.ɵɵlistener(\"click\", function ChatComponent_ng_template_7_Template_div_click_20_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const autoResizeTextArea_r16 = i0.ɵɵreference(19);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.addNewMessage($event, autoResizeTextArea_r16));\n    });\n    i0.ɵɵelement(21, \"img\", 91);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notUploading_r17 = i0.ɵɵreference(8);\n    const notUpload_r18 = i0.ɵɵreference(17);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r0.editMessageMode() ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c7, !ctx_r0.bubbleMode()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c8, ctx_r0.editMessageMode(), ctx_r0.editMessageMode()));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.uploadProgress > 0)(\"ngIfElse\", notUploading_r17);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.uploadProgress > 0)(\"ngIfElse\", notUpload_r18);\n  }\n}\nfunction ChatComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"span\", 101)(2, \"input\", 102);\n    i0.ɵɵlistener(\"input\", function ChatComponent_ng_template_9_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"i\", 103);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 108);\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_12_0;\n    const chatGroup_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.countLastMessageCount((tmp_12_0 = i0.ɵɵpipeBind1(2, 1, ctx_r0.lastMessages$)) == null ? null : tmp_12_0[chatGroup_r21.classroomId]));\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_ng_container_11_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 116);\n    i0.ɵɵtext(1, \" File deleted \");\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_ng_container_11_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 117);\n    i0.ɵɵtext(1, \" File \");\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ChatComponent_ng_template_11_div_0_ng_container_11_Conditional_1_Template, 2, 0);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵtemplate(3, ChatComponent_ng_template_11_div_0_ng_container_11_Conditional_3_Template, 2, 0);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_12_0;\n    const chatGroup_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ((tmp_12_0 = i0.ɵɵpipeBind1(2, 1, ctx_r0.lastMessages$)) == null ? null : tmp_12_0[chatGroup_r21.classroomId] == null ? null : tmp_12_0[chatGroup_r21.classroomId].lastMessageDeleted) ? 1 : 3);\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_ng_template_13_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 116);\n    i0.ɵɵtext(1, \" Message deleted \");\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_ng_template_13_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n    i0.ɵɵpipe(1, \"async\");\n  }\n  if (rf & 2) {\n    let tmp_13_0;\n    const chatGroup_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHtml\", (tmp_13_0 = i0.ɵɵpipeBind1(1, 1, ctx_r0.lastMessages$)) == null ? null : tmp_13_0[chatGroup_r21.classroomId] == null ? null : tmp_13_0[chatGroup_r21.classroomId].lastMessage, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ChatComponent_ng_template_11_div_0_ng_template_13_Conditional_0_Template, 2, 0);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵtemplate(2, ChatComponent_ng_template_11_div_0_ng_template_13_Conditional_2_Template, 2, 3);\n  }\n  if (rf & 2) {\n    let tmp_12_0;\n    const chatGroup_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ((tmp_12_0 = i0.ɵɵpipeBind1(1, 1, ctx_r0.lastMessages$)) == null ? null : tmp_12_0[chatGroup_r21.classroomId] == null ? null : tmp_12_0[chatGroup_r21.classroomId].lastMessageDeleted) ? 0 : 2);\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 119);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_12_0;\n    const chatGroup_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 3, ((tmp_12_0 = i0.ɵɵpipeBind1(2, 1, ctx_r0.lastMessages$)) == null ? null : tmp_12_0[chatGroup_r21.classroomId] == null ? null : tmp_12_0[chatGroup_r21.classroomId].lastMessageAt == null ? null : tmp_12_0[chatGroup_r21.classroomId].lastMessageAt.seconds) * 1000, \"short\"));\n  }\n}\nfunction ChatComponent_ng_template_11_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵlistener(\"click\", function ChatComponent_ng_template_11_div_0_Template_div_click_0_listener() {\n      const chatGroup_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.setUserActive(chatGroup_r21));\n    });\n    i0.ɵɵelementStart(1, \"div\", 106)(2, \"img\", 107);\n    i0.ɵɵlistener(\"error\", function ChatComponent_ng_template_11_div_0_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ChatComponent_ng_template_11_div_0_Conditional_3_Template, 1, 0, \"span\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ChatComponent_ng_template_11_div_0_Conditional_4_Template, 3, 3, \"div\", 109);\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵelementStart(6, \"div\", 110);\n    i0.ɵɵtemplate(7, ChatComponent_ng_template_11_div_0_ng_container_7_Template, 1, 0, \"ng-container\", 111);\n    i0.ɵɵelementStart(8, \"span\", 112);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 113);\n    i0.ɵɵtemplate(11, ChatComponent_ng_template_11_div_0_ng_container_11_Template, 4, 3, \"ng-container\", 84);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵtemplate(13, ChatComponent_ng_template_11_div_0_ng_template_13_Template, 3, 3, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 114);\n    i0.ɵɵtemplate(16, ChatComponent_ng_template_11_div_0_span_16_Template, 4, 6, \"span\", 115);\n    i0.ɵɵpipe(17, \"async\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_14_0;\n    let tmp_18_0;\n    let tmp_20_0;\n    const chatGroup_r21 = ctx.$implicit;\n    const notFileChat_r22 = i0.ɵɵreference(14);\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const chatTypeBadgeTemplate_r23 = i0.ɵɵreference(16);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c9, chatGroup_r21.classroomId === ctx_r0.currentChatId && !ctx_r0.bubbleMode()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.getChatGroupAvatarUrl(chatGroup_r21)), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, ctx_r0.isUserOnline(ctx_r0.getChatGroupAspUserId(chatGroup_r21)) ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, ctx_r0.countLastMessageCount((tmp_14_0 = i0.ɵɵpipeBind1(5, 10, ctx_r0.lastMessages$)) == null ? null : tmp_14_0[chatGroup_r21.classroomId]) > 0 ? 4 : -1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", chatTypeBadgeTemplate_r23)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(18, _c10, chatGroup_r21));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(chatGroup_r21.chatName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_18_0 = i0.ɵɵpipeBind1(12, 12, ctx_r0.lastMessages$)) == null ? null : tmp_18_0[chatGroup_r21.classroomId] == null ? null : tmp_18_0[chatGroup_r21.classroomId].lastMessageIsFile)(\"ngIfElse\", notFileChat_r22);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_20_0 = i0.ɵɵpipeBind1(17, 14, ctx_r0.lastMessages$)) == null ? null : tmp_20_0[chatGroup_r21.classroomId] == null ? null : tmp_20_0[chatGroup_r21.classroomId].lastMessage);\n  }\n}\nfunction ChatComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ChatComponent_ng_template_11_div_0_Template, 18, 20, \"div\", 104);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredChats());\n  }\n}\nfunction ChatComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 120)(1, \"li\", 121)(2, \"div\", 122);\n    i0.ɵɵelement(3, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 124)(5, \"span\", 125);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" No messages yet. Feel free to start the conversation with \", ctx_r0.activeUser == null ? null : ctx_r0.activeUser.relatedName, \" \");\n  }\n}\nfunction ChatComponent_ng_template_15_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 126);\n    i0.ɵɵtext(1, \"Group\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_ng_template_15_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 127);\n    i0.ɵɵtext(1, \"Private\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_ng_template_15_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 127);\n    i0.ɵɵtext(1, \"Private\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ChatComponent_ng_template_15_Conditional_0_Template, 2, 0, \"span\", 126)(1, ChatComponent_ng_template_15_Conditional_1_Template, 2, 0)(2, ChatComponent_ng_template_15_Conditional_2_Template, 2, 0);\n  }\n  if (rf & 2) {\n    const chatGroup_r24 = ctx.chatGroup;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, (chatGroup_r24 == null ? null : chatGroup_r24.chatType) === ctx_r0.chatType.CLASSROOM && (chatGroup_r24 == null ? null : chatGroup_r24.classroomStudents.length) > 1 ? 0 : (chatGroup_r24 == null ? null : chatGroup_r24.chatType) === ctx_r0.chatType.USER ? 1 : 2);\n  }\n}\nvar ChatType;\n(function (ChatType) {\n  ChatType[\"CLASSROOM\"] = \"classroom\";\n  ChatType[\"USER\"] = \"user\";\n})(ChatType || (ChatType = {}));\nexport class ChatComponent {\n  constructor() {\n    this.injector = inject(Injector);\n    this.bubbleMode = input(false);\n    this.subs = new SubSink();\n    this.term = signal('');\n    this.currentIndex = -1;\n    this.title = '';\n    this.chatService = inject(ChatService);\n    this.libraryService = inject(LibraryService);\n    this.authService = inject(AuthService);\n    this.classroomService = inject(ClassroomService);\n    this.userService = inject(UserService);\n    this.generalService = inject(GeneralService);\n    this.dialogService = inject(DialogService);\n    this.toastService = inject(ToastService);\n    this.http = inject(HttpClient);\n    this.loading = false;\n    this.files = [];\n    this.onlineUsers = [];\n    this.chatListsLoaded = signal(false);\n    this.messageTimestamps = [];\n    this.editMessageisLastMessage = false;\n    this.editMessageMode = signal(false);\n    this.messages = signal([]);\n    this.lastMessages = signal([]);\n    this.lastMessagesSubject = new BehaviorSubject([]);\n    this.lastMessages$ = this.lastMessagesSubject.asObservable();\n    this.expandStatuses = false;\n    this.expanded = false;\n    this.searchControl = new FormControl('');\n    this.user = this.authService.getLoggedInUser();\n    this.isTabletOrMobile = false;\n    this.myScrollContainer = viewChild('scrollMe');\n    // @ViewChild('scrollMe') private myScrollContainer: ElementRef = {} as ElementRef;\n    this.currentChatId = '';\n    this.chatsList = signal([]);\n    this.filteredChats = computed(() => {\n      if (!this.term()) {\n        return this.chatsList();\n      } else {\n        return this.chatsList().filter(chat => this.getChatGroupName(chat).toLowerCase().includes(this.term().toLowerCase()));\n      }\n    });\n    this.showInnerChat = false;\n    this.chatCircleState = 'shown';\n    this.chatBoxState = 'hidden';\n    this.chatBoxVisible = false;\n    this.chats = signal([]);\n    this.uploadProgress = 0;\n    this.leftTooltipItems = [];\n    this.pastedImage = undefined;\n    this.imageInfo = '';\n    this.chatType = ChatType;\n    this.lastFetchedMessage = null; // Track the last message for pagination\n    this.messageCache = new Map();\n    this.limit = 15; // Number of messages to load at a time\n    this.offset = 0; // Current offset\n    this.isSubscribed = false;\n    this.reloadChats = signal(false);\n  }\n  onSearchChange(event) {\n    const input = event.target;\n    this.term.set(input.value);\n  }\n  listenToReloadChats() {\n    toObservable(this.reloadChats, {\n      injector: this.injector\n    }).subscribe({\n      next: data => {\n        console.log(data);\n        if (data) {\n          this.subscribeToChatMessages(this.currentChatId);\n        }\n      }\n    });\n  }\n  toggleChat() {\n    this.chatCircleState = this.chatCircleState === 'shown' ? 'hidden' : 'shown';\n    this.chatBoxState = this.chatBoxState === 'shown' ? 'shown' : 'hidden';\n    this.chatBoxVisible = !this.chatBoxVisible;\n    this.activeUser = undefined;\n    this.showInnerChat = false;\n    if (this.chatBoxVisible) {\n      this.initChatFunctionality();\n    }\n  }\n  ngOnInit() {\n    this.chatService.getFireBaseUser().subscribe(res => {});\n    // this.chatService.createFirebaseUser('<EMAIL>', '12213213231').subscribe({\n    //     next: (data: any) => {\n    //         console.log(data);\n    //     },\n    //     error: (err: any) => {\n    //         console.log(err);\n    //     }\n    // })\n    // this.chatService.signInAnonymously().subscribe({\n    //     next: (res) => {\n    //         console.log(res);\n    //     },\n    //     error: (err) => {\n    //     }\n    // })\n    this.user = this.authService.getLoggedInUser();\n    if (!this.bubbleMode()) {\n      this.initChatFunctionality();\n    }\n    this.searchControl.valueChanges.subscribe(res => {});\n    this.loadOnlineStatus();\n    this.listenChatListsLoaded();\n    this.listenToReloadChats();\n    this.loadMobileLayoutCheck();\n    this.leftTooltipItems = [{\n      tooltipOptions: {\n        tooltipLabel: 'Add',\n        tooltipPosition: 'left'\n      },\n      icon: 'pi pi-pencil',\n      command: () => {}\n    }, {\n      tooltipOptions: {\n        tooltipLabel: 'Update',\n        tooltipPosition: 'left'\n      },\n      icon: 'pi pi-refresh',\n      command: () => {}\n    }, {\n      tooltipOptions: {\n        tooltipLabel: 'Delete',\n        tooltipPosition: 'left'\n      },\n      icon: 'pi pi-trash',\n      command: () => {}\n    }, {\n      icon: 'pi pi-upload',\n      tooltipOptions: {\n        tooltipLabel: 'Upload',\n        tooltipPosition: 'left'\n      }\n    }, {\n      tooltipOptions: {\n        tooltipLabel: 'Angular Website',\n        tooltipPosition: 'left'\n      },\n      icon: 'pi pi-external-link',\n      url: 'http://angular.io'\n    }];\n  }\n  ngAfterViewChecked() {}\n  ngAfterViewInit() {\n    // Initially set the height of each textarea\n    // this.textareas.forEach(textarea => {\n    //     this.adjustHeight(textarea.nativeElement);\n    // });\n    // // Listen for input event on each textarea to adjust the height\n    // this.textareas.forEach(textarea => {\n    //     console.log(textarea);\n    //     textarea.nativeElement.addEventListener('input', this.onInput.bind(this));\n    // });\n  }\n  ngOnDestroy() {\n    if (this.intervalId) {\n      clearInterval(this.intervalId);\n      console.log('Interval cleared');\n    }\n    this.showInnerChat = false;\n    this.subs.unsubscribe();\n  }\n  onFocusTextarea() {\n    console.log('focus');\n    this.chatService.markMessagesAsRead(this.currentChatId, this.user.aspUserId);\n  }\n  adjustParentHeight() {\n    // Assuming you have the implementation for this method to adjust the textarea height\n    const element = this.autoResizeTextArea.nativeElement;\n    element.style.height = 'auto';\n    element.style.height = `${element.scrollHeight}px`;\n  }\n  onInput(event) {\n    this.adjustHeight(event.target);\n  }\n  adjustHeight(textarea) {\n    textarea.style.height = 'auto'; // Reset height to auto to get the actual scrollHeight\n    textarea.style.height = `${textarea.scrollHeight}px`; // Set height based on scrollHeight\n  }\n  onBasicUploadAuto(event, fromClipboard = false) {\n    console.log(event);\n    let selectedFile = {};\n    if (fromClipboard) {\n      selectedFile = event;\n    } else {\n      selectedFile = event.currentFiles[0];\n    }\n    if (selectedFile) {\n      let headers = new HttpHeaders(); // Add any headers if needed\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      headers = new HttpHeaders({\n        'documentType': selectedFile.name.split('.').pop(),\n        \"Authorization\": \"Bearer \" + this.authService.getToken()\n      });\n      const myFile = {\n        progress: 0,\n        file: selectedFile,\n        index: 0\n      };\n      this.uploadRequest(headers, '', formData, myFile);\n    }\n  }\n  uploadRequest(headers, endpointExtras, formData, myFile) {\n    this.subs.add(this.http.post(environment.apiUrl + '/Upload/UploadLibraryFile' + endpointExtras, formData, {\n      headers: headers,\n      reportProgress: true,\n      observe: 'events'\n    }).subscribe(event => {\n      if (event.type === HttpEventType.UploadProgress) {\n        myFile.progress = Math.round(100 * event.loaded / event.total);\n        this.uploadProgress = myFile.progress;\n      } else if (event.type === HttpEventType.Response) {\n        console.log('Upload complete:', event.body);\n        this.sendMessage(event.body.dbPath, false, true);\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 800);\n        this.uploadProgress = 0;\n        // Handle the response from the server, e.g., store the file path or show a success message\n      }\n      if (event.loaded === event.total) {\n        console.log(HttpEventType);\n      }\n      if (event.type === HttpEventType.UploadProgress) {} else if (event.type === HttpEventType.Response) {\n        // this.uploadedFile = event.body;\n        // if (this.isProfile) {\n        //     console.log('GEGEG');\n        //     this.showCropper = false;\n        // }\n        // this.onUploadFinished.emit({ filePath: event.body, task: this.task });\n      } else {}\n    }, err => {\n      console.log(err);\n    }));\n  }\n  getChatGroupName(chatClassroom) {\n    if (!chatClassroom) {\n      return '';\n    }\n    if (this.authService.isTeacher) {\n      return this.classroomService.getFormattedStudentNames(chatClassroom.classroomStudents);\n    } else {\n      return chatClassroom.language + ' - ' + chatClassroom.teacher?.firstName;\n    }\n  }\n  getChatOppositeName(chatClassroom) {\n    if (!chatClassroom) {\n      return '';\n    }\n    if (this.authService.isTeacher) {\n      return this.classroomService.getFormattedStudentNames(chatClassroom.classroomStudents);\n    } else {\n      return chatClassroom.teacher?.firstName;\n    }\n  }\n  getChatGroupAvatarUrl(chatClassroom) {\n    if (!chatClassroom) {\n      return '';\n    }\n    if (this.authService.isTeacher) {\n      return chatClassroom.classroomStudents[0].avatarUrl;\n    } else {\n      return chatClassroom.teacher?.avatarUrl;\n    }\n  }\n  getChatGroupAspUserId(chatClassroom) {\n    if (!chatClassroom) {\n      return '';\n    }\n    if (this.authService.isTeacher) {\n      return chatClassroom.classroomStudents[0].aspUserId;\n    } else {\n      return chatClassroom.teacher?.aspUserId;\n    }\n  }\n  initChatFunctionality() {\n    this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(res => {\n      if (this.authService.isTeacher) {\n        this.initTeacherChats(res);\n      } else if (this.authService.isStudent) {\n        this.initStudentChats(res);\n      }\n      this.retrieveChats(res);\n    });\n  }\n  initTeacherChats(classrooms) {\n    const uniqueStudentIds = new Set();\n    const chats = classrooms.map(classroom => {\n      const classroomChat = this.createClassroomChat(classroom);\n      // TODO: this needed to prevent repeated student chats \n      if (classroom.classroomStudents.length === 1) {\n        return [classroomChat];\n      }\n      const studentChats = classroom.classroomStudents.filter(student => !uniqueStudentIds.has(+student.id)).map(student => {\n        uniqueStudentIds.add(+student.id);\n        return this.createStudentChat(classroom, student);\n      });\n      return [classroomChat, ...studentChats];\n    });\n    this.chatsList.set(chats.flat());\n  }\n  initStudentChats(classrooms) {\n    const uniqueTeacherIds = new Set();\n    const chats = classrooms.map(classroom => {\n      const classroomChat = this.createClassroomChat(classroom);\n      const individualChat = this.createIndividualChat(classroom);\n      if (classroom.classroomStudents.length > 1) {\n        return [classroomChat, individualChat];\n      }\n      // return [classroomChat];\n      if (!uniqueTeacherIds.has(+individualChat.teacherId)) {\n        uniqueTeacherIds.add(+individualChat.teacherId);\n        return [classroomChat];\n      } else {\n        return [classroomChat];\n      }\n    });\n    this.chatsList.set(chats.flat());\n  }\n  createClassroomChat(classroom) {\n    let name = '';\n    if (this.authService.isTeacher) {\n      name = this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\n    } else {\n      // student\n      if (classroom.classroomStudents.length > 1) {\n        name = this.generalService.getShortFullName(classroom.teacher) + ' - ' + this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\n      } else {\n        name = this.generalService.getShortFullName(classroom.teacher);\n      }\n    }\n    return {\n      classroomId: classroom.id,\n      classroomLevel: classroom.activeLevel,\n      classroomLanguage: classroom.language,\n      teacher: classroom.teacher,\n      chatType: ChatType.CLASSROOM,\n      chatName: name,\n      chatHistory: [],\n      classroomStudents: classroom.classroomStudents,\n      relatedName: this.classroomService.getFormattedStudentNames(classroom.classroomStudents)\n    };\n  }\n  createStudentChat(classroom, student) {\n    let name = '';\n    if (this.authService.isTeacher) {\n      name = this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\n    } else {\n      // student\n      if (classroom.classroomStudents.length > 1) {\n        name = this.generalService.getShortFullName(classroom.teacher) + ' - ' + this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\n      } else {\n        name = this.generalService.getShortFullName(classroom.teacher);\n      }\n    }\n    return {\n      classroomId: `teacher-${classroom.teacher?.id}-student-${student.aspUserId}`,\n      studentId: student.id,\n      relatedName: name,\n      chatType: ChatType.USER,\n      chatName: this.generalService.getShortFullName(student),\n      chatHistory: [],\n      teacher: classroom.teacher,\n      classroomStudents: classroom.classroomStudents\n    };\n  }\n  createIndividualChat(classroom) {\n    let name = '';\n    if (this.authService.isTeacher) {\n      name = this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\n    } else {\n      // student\n      if (classroom.classroomStudents.length > 1) {\n        name = this.generalService.getShortFullName(classroom.teacher);\n      } else {\n        name = this.generalService.getShortFullName(classroom.teacher);\n      }\n    }\n    return {\n      classroomId: `teacher-${classroom.teacher?.id}-student-${this.user.id}`,\n      teacherId: classroom.teacher?.id,\n      studentId: this.user.id,\n      chatType: ChatType.USER,\n      chatName: name,\n      chatHistory: [],\n      teacher: classroom.teacher,\n      classroomStudents: classroom.classroomStudents,\n      relatedName: this.classroomService.getFormattedStudentNames(classroom.classroomStudents)\n    };\n  }\n  loadChatMessages(chatId) {\n    this.unsubscribeFromChatMessages();\n    this.currentChatId = chatId;\n    this.lastFetchedMessage = {}; // Resetear último mensaje para paginación\n    // Obtener los datos en caché y verificar si el caché es válido\n    const cachedData = this.messageCache.get(chatId);\n    if (cachedData && this.isCacheValid(cachedData)) {\n      this.applyCachedMessages(cachedData);\n      console.log('Using cached messages');\n    } else {\n      // Si el caché no es válido, hacer la suscripción a los mensajes del servidor\n      this.subscribeToChatMessages(chatId);\n    }\n  }\n  applyCachedMessages(cachedData) {\n    this.messages.set(cachedData.messages);\n    this.lastFetchedMessage = cachedData.lastFetchedMessage;\n  }\n  unsubscribeFromChatMessages() {\n    if (this.currentChatSubscription) {\n      this.currentChatSubscription.unsubscribe();\n      this.currentChatSubscription = null;\n    }\n    this.isSubscribed = false;\n  }\n  subscribeToChatMessages(chatId) {\n    if (this.isSubscribed) {\n      console.warn('Already subscribed to messages for this chat.');\n      return;\n    }\n    this.currentChatSubscription = this.chatService.getChatMessages(chatId, this.limit).subscribe(messages => {\n      const reversedMessages = messages.reverse();\n      this.messages.set(reversedMessages);\n      console.log('Last fetched message: ', this.messages());\n      this.lastFetchedMessage = reversedMessages[0];\n      this.cacheMessages(chatId, reversedMessages, this.lastFetchedMessage);\n      if (this.showInnerChat) {\n        this.markMessagesAsRead(chatId);\n      }\n      this.scrollToBottom();\n      this.isSubscribed = true;\n    });\n  }\n  loadMoreMessages(chatId) {\n    if (!this.lastFetchedMessage) return;\n    this.chatService.getChatMessages(chatId, this.limit, this.lastFetchedMessage).subscribe(messages => {\n      const existingMessages = this.messages();\n      const updatedMessages = [...messages.reverse(), ...existingMessages];\n      this.messages.set(updatedMessages);\n      if (messages.length > 0) {\n        this.lastFetchedMessage = messages[0];\n        this.cacheMessages(chatId, updatedMessages, this.lastFetchedMessage);\n      }\n    });\n  }\n  isCacheValid(cachedData) {\n    return cachedData?.lastFetchedMessage?.id === cachedData.messages[0]?.id;\n  }\n  cacheMessages(chatId, messages, lastFetchedMessage) {\n    this.messageCache.set(chatId, {\n      messages,\n      lastFetchedMessage\n    });\n  }\n  markMessagesAsRead(chatId) {\n    this.chatService.markMessagesAsRead(chatId, this.user.aspUserId);\n    this.chatService.markLastMessagesAsRead(chatId, this.user.aspUserId);\n  }\n  clearCachedMessages() {\n    this.messageCache.clear();\n  }\n  addNewMessage(event, inputField) {\n    const textarea = event.target;\n    const now = Date.now();\n    // Remove timestamps older than 10 seconds\n    this.messageTimestamps = this.messageTimestamps.filter(timestamp => now - timestamp < 10000);\n    if (this.messageTimestamps.length > 3) {\n      const oldestTimestamp = Math.min(...this.messageTimestamps);\n      const waitTime = 10 - Math.floor((now - oldestTimestamp) / 1000);\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: `You can only send 3 messages every 10 seconds. Please wait ${waitTime} seconds to send the next message.`\n      });\n      return;\n    } else {\n      this.messageTimestamps.push(now); // Add current timestamp\n    }\n    let val = inputField.value?.trim();\n    if (val.length) {\n      // Convert line breaks (\\n) from textarea into <br> tags for HTML rendering\n      val = val.replace(/\\n/g, '<br>');\n      // Create a temporary div element to parse the HTML string\n      const tempDiv = document.createElement('div');\n      tempDiv.innerHTML = val; // Set the HTML input (with <br>) directly into the div\n      if (this.editMessageMode()) {\n        this.editMessageObj.content = tempDiv.innerHTML;\n        this.chatService.editMessage(this.currentChatId, this.editMessageObj.id, this.editMessageObj, this.user, this.editMessageisLastMessage);\n      } else {\n        // Send the sanitized HTML content with line breaks and formatting\n        this.sendMessage(tempDiv.innerHTML);\n      }\n      this.editMessageMode.set(false);\n    }\n    inputField.value = '';\n    setTimeout(() => {\n      textarea.rows = 1;\n      textarea.value = '';\n      // textarea.style.height = '24px'; // Reset the height to its original value\n      this.adjustParentHeight();\n    }, 30);\n  }\n  deleteMessage(message) {\n    console.log(message);\n    if (message[0].isUploadFile) {\n      this.chatService.deleteChatFilesFromLibraryFolder(message[0].content).subscribe({\n        next: res => {\n          console.log(res);\n          this.chatService.deleteMessage(this.currentChatId, message[0], this.user, message[1]);\n        },\n        error: () => {\n          this.chatService.deleteMessage(this.currentChatId, message[0], this.user, message[1]);\n        }\n      });\n    } else {\n      this.chatService.deleteMessage(this.currentChatId, message[0], this.user, message[1]);\n    }\n  }\n  editMessage(message) {\n    this.editMessageMode.set(true);\n    console.log(message);\n    this.editMessageObj = message[0];\n    this.editMessageisLastMessage = message[1];\n    if (this.autoResizeTextArea) {\n      const formattedContent = this.editMessageObj.content.replace(/<br\\s*\\/?>/gi, '\\n');\n      this.autoResizeTextArea.nativeElement.value = formattedContent; // Set the textarea value\n      this.adjustParentHeight(); // Adjust the height of the textarea if necessary\n    }\n  }\n  scrollToBottom() {\n    try {\n      setTimeout(() => {\n        if (this.myScrollContainer() && this.myScrollContainer().nativeElement) {\n          this.myScrollContainer().nativeElement.scrollTop = this.myScrollContainer().nativeElement.scrollHeight;\n        }\n      }, 100);\n    } catch (err) {}\n  }\n  setUserActive(user) {\n    this.activeUser = user;\n    if (!this.activeUser) {\n      return;\n    }\n    this.showInnerChat = true;\n    this.chatService.currentRoom.set(this.activeUser);\n    this.loadChatMessages(this.activeUser.classroomId);\n    this.chatService.markLastMessagesAsRead(this.currentChatId, this.user.aspUserId);\n    this.disableEditAndTextarea();\n    // this.chatService.getChatMessages(this.activeUser.aspUserId);\n  }\n  refreshList() {\n    this.currentTutorial = undefined;\n    this.currentIndex = -1;\n    // this.retrieveChats();\n  }\n  getUnreadMessages() {\n    // Reset unread count before starting the accumulation process\n    this.chatService.unreadCount.set(0);\n    const chatListsWithUnreadCounts = {};\n    this.chatsList().forEach(classroom => {\n      this.chatService.getLastUnreadMessagesCountForUser(classroom.classroomId, this.user.aspUserId).subscribe(count => {\n        const chatListWithUnreadCount = {\n          classroom: classroom,\n          unreadCount: count\n        };\n        chatListsWithUnreadCounts[classroom.classroomId] = chatListWithUnreadCount;\n        console.log(`Unread messages count for classroom ${classroom.classroomId} for user \n                ${this.user.firstName + ' ' + this.user.lastName} with aspUserId ${this.user.aspUserId}: ${count}`);\n        const totalUnreadCount = Object.values(chatListsWithUnreadCounts).reduce((acc, current) => acc + current.unreadCount, 0);\n        this.chatService.unreadCount.set(totalUnreadCount);\n        // this.reloadChats.set(true);\n        // this.clearCachedMessages();\n      });\n    });\n  }\n  retrieveChats(classrooms) {\n    // Wait for all the subscriptions to complete\n    this.getUnreadMessages();\n    // Initialize an object to hold the last messages for sorting\n    const lastMessages = this.lastMessagesSubject.getValue();\n    classrooms.forEach(classroom => {\n      // Retrieve unread message count for the classroom\n      // this.chatService.getUnreadMessagesCount(classroom.classroomId, this.user.aspUserId).subscribe(count => {\n      //     console.log(`Total getUnreadMessagesCount unread messages count for user ${this.user.aspUserId}: ${count}`);\n      // });\n      // Retrieve the last message for the classroom itself\n      this.chatService.getLastMessage(classroom.id).subscribe({\n        next: message => {\n          lastMessages[classroom.id] = message;\n          this.updateChatsListSorting(lastMessages);\n        },\n        error: err => {\n          console.error(`Failed to retrieve last message for classroom ${classroom.id}`, err);\n        }\n      });\n      // If the classroom has more than one student, retrieve last messages for individual chats\n      classroom.classroomStudents.forEach(student => {\n        let chatId = `teacher-${classroom.teacher.id}-student-${student.aspUserId}`;\n        if (classroom.chatType === ChatType.CLASSROOM) {\n          chatId = `${classroom.id}`;\n        }\n        if (classroom.chatType === ChatType.USER) {\n          if (this.authService.isStudent) {\n            chatId = `teacher-${classroom.teacher.id}-student-${this.user.id}`;\n          }\n        }\n        console.log(`Retrieving  ${chatId}`);\n        this.chatService.getLastMessage(chatId).subscribe({\n          next: message => {\n            lastMessages[chatId] = message;\n            this.updateChatsListSorting(lastMessages);\n          },\n          error: err => {\n            console.error(`Failed to retrieve last message for student ${student.id} in classroom ${classroom.id}`, err);\n          }\n        });\n      });\n    });\n  }\n  updateChatsListSorting(lastMessages) {\n    this.chatsList.set(this.chatsList().sort((a, b) => {\n      const aLastMessage = lastMessages[a.classroomId]?.lastMessageAt?.seconds || 0;\n      const bLastMessage = lastMessages[b.classroomId]?.lastMessageAt?.seconds || 0;\n      return bLastMessage - aLastMessage;\n    }));\n    this.lastMessagesSubject.next(lastMessages);\n    this.chatListsLoaded.set(true);\n  }\n  listenChatListsLoaded() {\n    toObservable(this.chatListsLoaded, {\n      injector: this.injector\n    }).subscribe({\n      next: chatListsLoaded => {\n        console.log(chatListsLoaded);\n        if (chatListsLoaded) {\n          setTimeout(() => {\n            if (!this.bubbleMode() && !this.isTabletOrMobile) {\n              this.setUserActive(this.filteredChats()[0]);\n            }\n          }, 100);\n        }\n      }\n    });\n  }\n  sortUserListByChats(chats) {\n    // Create a map of userId to the latest timestamp of their chats\n    const userTimestampMap = {};\n    chats.forEach(chat => {\n      chat.participants.forEach(participant => {\n        // Update the map with the latest timestamp for each user\n        if (!userTimestampMap[participant] || userTimestampMap[participant] < chat.lastMessageAt) {\n          userTimestampMap[participant] = chat.lastMessageAt;\n        }\n      });\n    });\n    // Sort the chatsList by the latest timestamp of their chats\n  }\n  findAvatarUrl(aspUserId) {\n    for (let classroom of this.chatsList()) {\n      // Check if the teacher matches the aspUserId\n      if (classroom.teacher && classroom.teacher.aspUserId === aspUserId) {\n        return classroom.teacher;\n      }\n      // Check if any student matches the aspUserId\n      for (let student of classroom.classroomStudents) {\n        if (student.aspUserId === aspUserId) {\n          return student;\n        }\n      }\n    }\n    return null; // Return null if no match is found\n  }\n  getUserFullNameAndInitials(aspUserId) {\n    for (let classroom of this.chatsList()) {\n      // Check if the teacher matches the aspUserId\n      if (classroom.teacher && classroom.teacher.aspUserId === aspUserId) {\n        const fullName = classroom.teacher.firstName + ' ' + classroom.teacher.lastName;\n        const initials = classroom.teacher.firstName.charAt(0) + classroom.teacher.lastName.charAt(0);\n        return {\n          fullName,\n          initials\n        };\n      }\n      // Check if any student matches the aspUserId\n      for (let student of classroom.classroomStudents) {\n        if (student.aspUserId === aspUserId) {\n          const fullName = student.firstName + ' ' + student.lastName;\n          const initials = student.firstName.charAt(0) + student.lastName.charAt(0);\n          return {\n            fullName,\n            initials\n          };\n        }\n      }\n    }\n    return null; // Return null if no match is found\n  }\n  sendMessage(content, isFileFromLibrary = false, isUploadFile = false) {\n    const message = {\n      senderId: this.user.aspUserId,\n      content: content,\n      timestamp: serverTimestamp(),\n      readBy: [this.user.aspUserId],\n      isFileFromLibrary: isFileFromLibrary,\n      isUploadFile: isUploadFile,\n      readStatus: {\n        [this.user.aspUserId]: true\n      }\n    };\n    console.log(this.user);\n    this.chatService.addMessage(this.currentChatId, message, this.user);\n    this.chatService.markMessagesAsRead(this.currentChatId, this.user.aspUserId);\n  }\n  isImage(filePath) {\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.ico', '.jfif', '.avif'];\n    const fileExtension = filePath.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(`.${fileExtension}`);\n  }\n  goToChat() {\n    this.showInnerChat = true;\n  }\n  openFileUploadDialog() {\n    const divToShowDialog = this.bubbleMode() ? 'mainContainerSideMenuBelow' : 'containerLayout';\n    this.libraryService.isPreviewingFromChat.set(true);\n    this.userService.setNewFileUpload({});\n    setTimeout(() => {\n      const myElement = document.getElementById(divToShowDialog);\n      const dialogWidth = myElement.getBoundingClientRect().width - 10;\n      this.generalService.openDialogWithComponent(this.dialogService, ChatUploadFileDialogComponent, dialogWidth.toFixed(), {\n        action: 'upload',\n        dialogsubTitle: '',\n        dialogTitle: 'Upload to library...'\n      }, divToShowDialog, result => {\n        console.log(result);\n        this.chatService.showUploadFile.set(false);\n        this.libraryService.checkedLibraryFiles.set([]);\n        this.libraryService.isPreviewingFromChat.set(false);\n        // this.dialogService.getInstance(result).close();\n        // this.generalService.destroyComponent(ChatUploadFileDialogComponent);;\n        if (result && Array.isArray(result)) {\n          result.forEach(item => {\n            this.sendMessage(item.path, true, true);\n          });\n          setTimeout(() => {\n            this.scrollToBottom();\n          }, 1000);\n          console.log(result);\n          // this.moveFile(result.map.map);\n        }\n      }, myElement.getBoundingClientRect().height - 290 + 'px');\n    }, 100);\n  }\n  onPaste(event) {\n    const items = (event.clipboardData || event.originalEvent.clipboardData).items;\n    let blob = null;\n    for (const item of items) {\n      if (item.type.indexOf('image') === 0) {\n        blob = item.getAsFile();\n        this.onBasicUploadAuto(blob, true);\n        console.log(blob); // Prints your files\n      }\n    }\n  }\n  recognizeImage(imageData) {\n    // Simulate an API call\n    this.imageInfo = 'Recognized Text: \"Example text from image\"'; // Mock recognition result\n    // Example of a real API call\n    /*\n    this.http.post('YOUR_API_ENDPOINT', { image: imageData }).subscribe(response => {\n      this.imageInfo = response.recognizedText; // Update with actual response\n    });\n    */\n  }\n  loadOnlineStatus() {\n    this.chatService.setUserStatus(this.user.aspUserId, 'online');\n    this.initUserOnlineInterval();\n    this.setUserLastOnlineTime();\n    // Subscribe to online users\n    this.subs.add(this.chatService.getOnlineUsersComparedToLastOnlineTime().subscribe(users => {\n      this.onlineUsers = users;\n    }));\n  }\n  setUserLastOnlineTime() {\n    this.chatService.setUserLastOnlineTime(this.user.aspUserId, serverTimestamp());\n  }\n  initUserOnlineInterval() {\n    this.intervalId = setInterval(() => {\n      this.setUserLastOnlineTime();\n    }, this.chatService.secondsToCheckOnlineStatus * 1000);\n  }\n  isUserOnline(userId) {\n    const currentTime = Timestamp.now();\n    return this.onlineUsers.some(user => {\n      if (user.id === userId) {\n        const lastOnlineTime = user.lastOnlineTime;\n        const timeDifference = currentTime.seconds - lastOnlineTime.seconds;\n        return timeDifference <= this.chatService.secondsToCheckOnlineStatus;\n      }\n      return false;\n    });\n  }\n  loadMobileLayoutCheck() {\n    this.subs.sink = this.generalService.deviceKind.pipe(switchMap(res => {\n      this.isTabletOrMobile = !res.w992up;\n      if (!res.w1024up) {\n        return of(0);\n      } else {\n        return of(null);\n      }\n    })).subscribe(res => {\n      if (res === 0) {} else if (res !== 0) {\n        const menuHeight = res;\n      }\n    });\n  }\n  showLeftSideMobile() {\n    if (this.isTabletOrMobile && this.showInnerChat) {\n      return false;\n    } else {\n      return true;\n    }\n  }\n  showRightSideMobile() {\n    if (this.isTabletOrMobile && !this.showInnerChat) {\n      return false;\n    } else {\n      return true;\n    }\n  }\n  showBackUsers() {\n    this.showInnerChat = false;\n  }\n  autoResize(textArea) {\n    textArea.style.height = 'auto'; // Reset height to auto to shrink when deleting text\n    textArea.style.height = textArea.scrollHeight + 'px'; // Set height to the scrollHeight of the content\n  }\n  disableEditAndTextarea() {\n    this.editMessageMode.set(false);\n  }\n  resetTextarea() {\n    this.autoResizeTextArea.nativeElement.value = '';\n    // this.adjustHeight(this.autoResizeTextArea.nativeElement);\n    this.adjustParentHeight();\n  }\n  countLastMessageCount(lastMessage) {\n    if (!lastMessage) {\n      return;\n    }\n    if (lastMessage.unreadCounts) {\n      return lastMessage.unreadCounts[this.user.aspUserId];\n    } else {\n      return 0;\n    }\n  }\n  static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n    return new (t || ChatComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponent,\n    selectors: [[\"app-chat\"]],\n    viewQuery: function ChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuerySignal(ctx.myScrollContainer, _c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance();\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.autoResizeTextArea = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.textareaWrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editForbitDialog = _t.first);\n      }\n    },\n    inputs: {\n      bubbleMode: [i0.ɵɵInputFlags.SignalBased, \"bubbleMode\"]\n    },\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([DialogService]), i0.ɵɵStandaloneFeature],\n    decls: 17,\n    vars: 1,\n    consts: [[\"chatPersonTemplate\", \"\"], [\"inputTextTemplate\", \"\"], [\"personSearchTemplate\", \"\"], [\"personBoxTemplate\", \"\"], [\"emptyMessagesTemplate\", \"\"], [\"chatTypeBadgeTemplate\", \"\"], [\"scrollMe\", \"\"], [\"textareaWrapper\", \"\"], [\"notUploading\", \"\"], [\"notUpload\", \"\"], [\"autoResizeTextArea\", \"\"], [\"notFileChat\", \"\"], [\"id\", \"notifications-overview\", 1, \"notifications-overview\"], [1, \"surface-section\", \"border-round-3xl\"], [1, \"block-header\", \"relative\", \"justify-content-center\", \"lg:flex\"], [1, \"absolute\", \"pl-1\", \"md:pl-2\", \"left-0\"], [1, \"block-title\"], [1, \"text-0\"], [1, \"chat-wrapper\"], [1, \"chat\", \"flex-column\", \"sm:flex-row\"], [\"class\", \"left\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"right sm:col-8\", 4, \"ngIf\"], [1, \"left\", 3, \"ngClass\"], [1, \"persona-container\"], [4, \"ngTemplateOutlet\"], [1, \"right\", \"sm:col-8\"], [1, \"chat-messages\", \"mt-1\", 3, \"ngClass\"], [3, \"click\"], [3, \"deleteMessageSelected\", \"editMessageSelected\", \"messages\", \"chatsList\"], [\"id\", \"chat-circle\", \"tooltipPosition\", \"top\", 1, \"btn\", \"btn-raised\", 3, \"click\", \"pTooltip\"], [1, \"pi\", \"pi-comments\"], [\"badgeSize\", \"small\", \"severity\", \"success\", \"styleClass\", \"absolute notif-badge\", 3, \"value\"], [\"class\", \"chat-box overflow-hidden border-round-lg\", 4, \"ngIf\"], [1, \"chat-box\", \"overflow-hidden\", \"border-round-lg\"], [1, \"chat-box-header\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"text-lg\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\"], [\"title\", \"Open Chat in new tab\", \"target\", \"_blank\", 1, \"flex\", \"align-items-center\", 3, \"href\"], [1, \"pi\", \"pi-window-maximize\", 3, \"click\"], [1, \"chat-box-toggle\", \"flex\", \"align-items-center\", 3, \"click\"], [1, \"pi\", \"pi-times\"], [1, \"chat-box-body\"], [1, \"main-screen\", 3, \"ngClass\"], [\"id\", \"homeScreen\", \"class\", \"home-screen\", 4, \"ngIf\"], [\"id\", \"chatScreen\", \"class\", \"chat-screen\", 4, \"ngIf\"], [\"id\", \"searchScreen\", 1, \"search-screen\"], [1, \"search-screen-header\"], [\"id\", \"chat-back-button\", 1, \"back-button\"], [1, \"fas\", \"fa-long-arrow-alt-left\"], [\"id\", \"searchInput\", \"type\", \"text\", \"placeholder\", \"Search\", 1, \"search-input\"], [\"id\", \"searchScreenBody\", 1, \"search-screen-body\"], [\"class\", \"chat-input\", 4, \"ngIf\"], [\"id\", \"homeScreen\", 1, \"home-screen\"], [\"id\", \"homeScreenMessagesBody\", 1, \"home-screen-messages\", \"bg-grad-white-style\"], [1, \"flex\", \"flex-column\", \"h-full\", \"px-2\"], [1, \"mt-1\"], [1, \"flex-auto\", \"overflow-y-auto\", \"pr-1\", \"pt-2\", \"px-2\"], [\"id\", \"chatScreen\", 1, \"chat-screen\"], [1, \"chat-screen-header\"], [1, \"chat-screen-header-left\"], [\"id\", \"chatBackbutton\", 1, \"back-button\"], [1, \"search-button\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"right\"], [1, \"chat-messages\", 3, \"ngClass\"], [3, \"editMessageSelected\", \"deleteMessageSelected\", \"messages\", \"chatsList\", \"isBubbleMode\"], [1, \"chat-input\"], [1, \"chat-screen-footer\"], [1, \"input\"], [1, \"chat-person\"], [1, \"inner\", \"border-bottom-1\", \"border-200\", \"relative\", \"p-2\", \"sm:p-2\", \"w-full\"], [1, \"flex\", \"surface-border\", \"align-items-center\"], [\"icon\", \"pi pi-chevron-left\", \"styleClass\", \"text-2xl\", \"severity\", \"info\", 3, \"click\", 4, \"ngIf\"], [1, \"relative\", \"mr-2\", \"flex\", \"align-items-center\"], [1, \"border-circle\", 2, \"width\", \"46px\", \"height\", \"46px\", 3, \"error\", \"src\"], [\"id\", \"pn_id_26_badge\", 1, \"online__badge\"], [1, \"flex\", \"flex-column\", \"align-items-start\"], [1, \"text-base\", \"sm:text-xl\", \"text-900\", \"font-medium\"], [\"icon\", \"pi pi-chevron-left\", \"styleClass\", \"text-2xl\", \"severity\", \"info\", 3, \"click\"], [1, \"w-full\", \"border-round-3xl\", \"bg-yellow-200\", \"py-2\", \"px-3\", \"text-yellow-900\", \"text-sm\", \"flex\", \"align-items-center\", \"justify-content-between\", \"px-2\", \"py-1\", \"font-medium\", \"mb-0\"], [1, \"chat-input\", \"box\", 3, \"ngClass\"], [1, \"speeddial-tooltip-demo\", 2, \"position\", \"relative\", 3, \"ngClass\"], [1, \"fab-container\"], [1, \"fab\", \"fab-icon-holder\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"fab-options\"], [1, \"fab-line\", \"fab-icon-holder\", 3, \"click\"], [\"src\", \"/assets/icons/folder-icon.svg\", \"alt\", \"\", 1, \"send-img\"], [1, \"fab-tel\", \"fab-icon-holder\"], [\"rows\", \"1\", \"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Type a message\", 1, \"p-1\", \"w-full\", \"mx-2\", 3, \"input\", \"paste\", \"focus\", \"click\", \"keydown.enter\"], [1, \"chat-send-btn\", \"send\", 3, \"click\"], [\"src\", \"/assets/icons/paper-plane.svg\", \"alt\", \"\", 1, \"send-img\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [1, \"pi\", \"pi-pencil\"], [1, \"pi\", \"pi-times\", \"ml-2\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\"], [1, \"pi\", \"pi-plus\"], [\"mode\", \"basic\", \"accept\", \"image/*\", \"maxFileSize\", \"4000000\", \"styleClass\", \"p-button-rounded p-button-info purple-icon text-white\", 3, \"onSelect\", \"auto\"], [\"pTemplate\", \"chooseicon\"], [\"src\", \"/assets/icons/camera.svg\", \"alt\", \"\", 1, \"send-img\"], [1, \"search\"], [1, \"p-input-icon-right\", \"w-full\", \"mb-1\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"input\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [\"class\", \"persona box border-1 border-200\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"persona\", \"box\", \"border-1\", \"border-200\", 3, \"click\", \"ngClass\"], [1, \"relative\", \"mr-1\", \"flex\", \"align-items-center\"], [1, \"border-circle\", 2, \"width\", \"40px\", \"height\", \"40px\", 3, \"error\", \"src\"], [1, \"online__badge\"], [1, \"new-message-count\"], [1, \"info-section\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"info-name\"], [1, \"info-message\"], [1, \"detail-section\"], [\"class\", \"detail-date\", 4, \"ngIf\"], [1, \"pi\", \"pi-trash\"], [1, \"pi\", \"pi-file\"], [3, \"innerHtml\"], [1, \"detail-date\"], [1, \"px-1\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"py-2\"], [1, \"fab\", \"w-3rem\", \"h-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-indigo-300\", \"border-circle\", \"mr-3\", \"flex-shrink-0\"], [1, \"pi\", \"pi-comments\", \"text-xl\", \"text-white\"], [1, \"text-900\", \"line-height-3\", \"font-medium\"], [1, \"text-700\", \"font-normal\"], [1, \"group__badge\", \"p-badge\", \"p-component\", \"p-badge-info\"], [1, \"group__badge\", \"p-badge\", \"p-component\", \"p-badge-classroom\"]],\n    template: function ChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ChatComponent_Defer_0_Template, 1, 1)(1, ChatComponent_DeferPlaceholder_1_Template, 2, 0);\n        i0.ɵɵdefer(2, 0, ChatComponent_Defer_2_DepsFn, null, 1);\n        i0.ɵɵdeferOnViewport(0, -1);\n        i0.ɵɵtemplate(4, ChatComponent_Conditional_4_Template, 4, 4)(5, ChatComponent_ng_template_5_Template, 10, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(7, ChatComponent_ng_template_7_Template, 22, 12, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(9, ChatComponent_ng_template_9_Template, 4, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(11, ChatComponent_ng_template_11_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(13, ChatComponent_ng_template_13_Template, 7, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(15, ChatComponent_ng_template_15_Template, 3, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(4, ctx.bubbleMode() ? 4 : -1);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.AsyncPipe, i1.DatePipe, ButtonModule, i2.Button, i3.PrimeTemplate, SharedModule, i4.Tooltip, FileUploadModule, i5.FileUpload, InputTextModule, i6.InputText, InputTextareaModule, SpeedDialModule, BadgeModule, i7.Badge, TooltipModule, TabViewModule, ChatMessagesDisplayComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.bg-grad-white-style[_ngcontent-%COMP%] {\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #c8c8fb 100%);\\n}\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\nul[_ngcontent-%COMP%] {\\n  padding: 0;\\n  list-style-type: none;\\n}\\n\\n.image-class[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  border-radius: 10px;\\n}\\n\\n.block-header[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/notification-overview-header.png\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n  background-position: center;\\n  border-radius: 6px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%] {\\n  height: 50dvh;\\n  width: 100%;\\n  background: white;\\n  overflow: overlay;\\n}\\n@media only screen and (max-width: 768px) {\\n  .main-screen[_ngcontent-%COMP%] {\\n    height: calc(100dvh - 120px);\\n  }\\n  .main-screen[_ngcontent-%COMP%]:not(.showInnerChat) {\\n    height: calc(100dvh - 52px);\\n  }\\n}\\n@media only screen and (max-width: 768px) {\\n  .main-screen.editMode[_ngcontent-%COMP%] {\\n    height: calc(100dvh - 145px);\\n  }\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: block;\\n  padding: 0;\\n  position: relative;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  animation: fadein 1.3s;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding-top: 2rem;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 40px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  \\n\\n  width: 40px;\\n  cursor: pointer;\\n  transition: 0.25s all;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.5);\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-tabbar[_ngcontent-%COMP%] {\\n  padding: 1rem 0 0 0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-tabbar[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #868ca0;\\n  font-size: 16px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-tabbar[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-size: 20px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-fav[_ngcontent-%COMP%] {\\n  padding: 2rem 0 1rem 0;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: start;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-fav[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #868ca8;\\n  font-size: 14px;\\n  margin-bottom: 1rem;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-fav-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: space-between;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-fav-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 60px;\\n  cursor: pointer;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-fav-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 100px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-fav-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.5rem;\\n  text-align: center;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-fav-row[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%] {\\n  border: 2px solid;\\n  border-radius: 100px;\\n  padding: 0px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n  border-color: rgba(227, 228, 232, 0.1803921569);\\n  border-width: 1px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  cursor: pointer;\\n  position: relative;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 60px;\\n  width: 60px;\\n  border-radius: 100%;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message-content-top[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message-content-top[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message-content-top[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message-content-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message-content-bottom[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 400;\\n  color: #868ca0;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message-content-bottom[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 30px;\\n  text-align: center;\\n  background: #4753c7;\\n  color: white;\\n  height: 20px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 10px;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .home-screen-messages-message[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -10px;\\n  width: 80%;\\n  height: 1px;\\n  background: #e3e4e8;\\n  right: 0;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .chat-screen[_ngcontent-%COMP%] {\\n  height: 100%;\\n  padding: 0;\\n  position: relative;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  animation: fadein 1.3s;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding-top: 2rem;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: -webkit-sticky;\\n  \\n\\n  top: 0;\\n  background: white;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header-left-username[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-bottom: 0.2rem;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header-left-username[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  font-size: 12px;\\n  justify-content: start !important;\\n  width: 75px !important;\\n  margin: 0 !important;\\n  height: auto !important;\\n  color: #868ca0;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header-left-username[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  transform: none !important;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header-left-username[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  right: 0;\\n  top: 0;\\n  height: 8px;\\n  width: 8px;\\n  border-radius: 100px;\\n  background: #08b827;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 40px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  \\n\\n  width: 40px;\\n  cursor: pointer;\\n  transition: 0.25s all;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.5);\\n}\\n\\n.chat-screen[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 2rem;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-sender[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 40px;\\n  width: 40px;\\n  align-self: flex-end;\\n  border-radius: 100px;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-sender-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-sender-content-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  padding: 1rem;\\n  background: #f9f9f9;\\n  border-radius: 0 10px 10px 10px;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-sender-content-time[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n  font-size: 10px;\\n  color: #acacac;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   #typing[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-reciever[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-reciever[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 40px;\\n  width: 40px;\\n  align-self: flex-end;\\n  border-radius: 100px;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-reciever-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-reciever-content-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  padding: 1rem;\\n  background: #f9f9f9;\\n  border-radius: 0 10px 10px 10px;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-reciever-content-time[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n  font-size: 10px;\\n  color: #acacac;\\n  text-align: right;\\n}\\n\\n.chat-screen[_ngcontent-%COMP%]   .message-typing[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  padding: 1rem;\\n  background: #f9f9f9;\\n  border-radius: 0 10px 10px 10px;\\n  margin-bottom: 2rem;\\n}\\n\\n.chat-screen-footer[_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 16px;\\n  width: 100%;\\n  \\n\\n  left: 0;\\n  \\n\\n  right: 0;\\n  margin: 0 auto;\\n}\\n\\n.chat-screen-footer[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex-direction: column;\\n}\\n\\n.chat-screen-footer[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: relative;\\n  padding: 0.8rem;\\n  border-radius: 1rem;\\n  border: none;\\n  background: #f4f4f4;\\n}\\n\\n.chat-screen-footer[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 5px;\\n  \\n\\n  \\n\\n  height: 30px;\\n  \\n\\n  width: 30px;\\n  border-radius: 100px;\\n  border: none;\\n  background: #4753c7;\\n  color: white;\\n  cursor: pointer;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen[_ngcontent-%COMP%] {\\n  height: 100%;\\n  padding: 0 1rem;\\n  position: relative;\\n  display: none;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  animation: fadein 1.3s;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding-top: 2rem;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: -webkit-sticky;\\n  z-index: 10;\\n  padding-bottom: 1rem;\\n  background: white;\\n  \\n\\n  top: 0;\\n  background: white;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header-left-username[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-bottom: 0.2rem;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header-left-username[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  font-size: 12px;\\n  justify-content: start !important;\\n  width: 75px !important;\\n  margin: 0 !important;\\n  height: auto !important;\\n  color: #868ca0;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header-left-username[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  transform: none !important;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header-left-username[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  right: 0;\\n  top: 0;\\n  height: 8px;\\n  width: 8px;\\n  border-radius: 100px;\\n  background: #08b827;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 40px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  \\n\\n  width: 40px;\\n  cursor: pointer;\\n  transition: 0.25s all;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.5);\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-header[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: relative;\\n  padding: 0.8rem;\\n  border-radius: 1rem;\\n  border: none;\\n  background: #f4f4f4;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-[_ngcontent-%COMP%] {\\n  padding: 1rem 0;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-body-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  cursor: pointer;\\n  position: relative;\\n}\\n\\n.main-screen[_ngcontent-%COMP%]   .search-screen-body-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 60px;\\n  width: 60px;\\n  margin-right: 10px;\\n  border-radius: 2rem;\\n}\\n\\n#center-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .notif-badge {\\n  top: -5px;\\n  background-color: #09c;\\n  border: 1px solid #fff;\\n}\\n\\n#chat-circle[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 24px;\\n  right: 30px;\\n  background: linear-gradient(to top right, #ff4081, #8e8bf3);\\n  line-height: 1;\\n  border-radius: 50%;\\n  color: white;\\n  padding: 16px;\\n  cursor: pointer;\\n  box-shadow: 0px 3px 16px 0px rgba(0, 0, 0, 0.6), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);\\n  width: 50px;\\n  height: 50px;\\n  z-index: 9999;\\n}\\n\\n.btn#my-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  padding-top: 13px;\\n  padding-bottom: 12px;\\n  border-radius: 45px;\\n  padding-right: 40px;\\n  padding-left: 40px;\\n  color: #5865C3;\\n}\\n\\n#chat-overlay[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  display: none;\\n}\\n\\n.chat-box[_ngcontent-%COMP%] {\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #F1F3FF 100%);\\n  position: fixed;\\n  right: 30px;\\n  bottom: 50px;\\n  width: 350px;\\n  max-width: 85vw;\\n  max-height: 100dvh;\\n  border-radius: 5px;\\n  \\n\\n  box-shadow: 0px 5px 35px 9px #ccc;\\n  z-index: 9999;\\n}\\n@media only screen and (max-width: 768px) {\\n  .chat-box[_ngcontent-%COMP%] {\\n    bottom: 0;\\n    width: 100%;\\n    right: 0;\\n    top: 0;\\n    max-width: 100vw;\\n    max-height: 100dvh;\\n  }\\n}\\n\\n.chat-box-toggle[_ngcontent-%COMP%] {\\n  float: right;\\n  margin-right: 5px;\\n  cursor: pointer;\\n}\\n\\n.chat-box-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to top right, #7c79ea, #6683ec);\\n  display: flex;\\n  flex-wrap: wrap;\\n  align-items: center;\\n  justify-content: space-between;\\n  contain: layout;\\n  color: white;\\n  color: #ffffff;\\n  box-shadow: 0 0.1rem 1rem hsla(0, 0%, 0%, 0.4);\\n  overflow: hidden;\\n  padding: 1rem 1rem;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 11;\\n  will-change: transform;\\n  transition: transform 0.15s ease-in-out;\\n}\\n\\n.chat-box-body[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 370px;\\n  height: auto;\\n  overflow: hidden;\\n}\\n\\n.chat-box-body[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  background-image: url(\\\"data:image/svg+xml;base64,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\\\");\\n  opacity: 0.1;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  right: 0;\\n  height: 100%;\\n  position: absolute;\\n  z-index: -1;\\n}\\n\\n#chat-input[_ngcontent-%COMP%] {\\n  background: #f4f7f9;\\n  width: 100%;\\n  position: relative;\\n  height: 47px;\\n  padding-top: 10px;\\n  padding-right: 50px;\\n  padding-bottom: 10px;\\n  padding-left: 15px;\\n  border: none;\\n  resize: none;\\n  outline: none;\\n  border: 1px solid #ccc;\\n  color: #888;\\n  border-top: none;\\n  border-bottom-right-radius: 5px;\\n  border-bottom-left-radius: 5px;\\n  overflow: hidden;\\n}\\n\\n.chat-input[_ngcontent-%COMP%]    > form[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n#chat-input[_ngcontent-%COMP%]::-webkit-input-placeholder {\\n  \\n\\n  color: #ccc;\\n}\\n\\n#chat-input[_ngcontent-%COMP%]::-moz-placeholder {\\n  \\n\\n  color: #ccc;\\n}\\n\\n#chat-input[_ngcontent-%COMP%]:-ms-input-placeholder {\\n  \\n\\n  color: #ccc;\\n}\\n\\n#chat-input[_ngcontent-%COMP%]:-moz-placeholder {\\n  \\n\\n  color: #ccc;\\n}\\n\\n.chat-submit[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 3px;\\n  right: 10px;\\n  background: transparent;\\n  box-shadow: none;\\n  border: none;\\n  border-radius: 50%;\\n  color: #5A5EB9;\\n  width: 35px;\\n  height: 35px;\\n}\\n\\n.chat-logs[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  height: 370px;\\n  overflow-y: scroll;\\n}\\n\\na[_ngcontent-%COMP%]:active {\\n  outline: 0;\\n}\\n\\n.clear[_ngcontent-%COMP%] {\\n  clear: both;\\n}\\n\\n.chat-wrapper[_ngcontent-%COMP%] {\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  top: 0;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n.chat[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: calc(100vh - 160px);\\n  display: flex;\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #F1F3FF 100%) !important;\\n  border: 1px solid #dedede;\\n  border-bottom-left-radius: 20px;\\n  border-bottom-right-radius: 20px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .chat[_ngcontent-%COMP%] {\\n    height: calc(100dvh - 120px);\\n  }\\n}\\n\\n.box[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 3px;\\n  padding: 24px 10px;\\n  display: flex;\\n  cursor: pointer;\\n  background-color: #fff;\\n  border-radius: 0.75rem;\\n  margin-bottom: 10px;\\n  border: 1px solid transparent;\\n  transition: background-color 0.3s;\\n}\\n\\n[_nghost-%COMP%]     .p-button-info {\\n  color: #8180f7;\\n  background-color: transparent !important;\\n}\\n[_nghost-%COMP%]     .img-btn-upload .p-button {\\n  background-color: transparent !important;\\n}\\n\\n.avatar[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  background: linear-gradient(to top right, #6683ec, #7c79ea);\\n  display: flex;\\n  width: 25px;\\n  height: 20px;\\n  vertical-align: top;\\n  color: #fff;\\n  font-size: 10px;\\n  text-align: center;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.left[_ngcontent-%COMP%] {\\n  justify-content: left;\\n  flex-direction: column;\\n  height: 100%;\\n  padding: 10px 0px 10px 12px;\\n  border-radius: 20px;\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #F1F3FF 100%) !important;\\n  border-right: 1px solid #efefff;\\n}\\n@media only screen and (max-width: 768px) {\\n  .left[_ngcontent-%COMP%] {\\n    height: calc(100dvh - 120px);\\n  }\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  margin: 0;\\n}\\n\\n.search[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 50px;\\n}\\n\\n.search[_ngcontent-%COMP%]    > i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n.search[_ngcontent-%COMP%]    > input[_ngcontent-%COMP%] {\\n  height: 20px;\\n  outline: none;\\n  font-size: 16px;\\n  font-weight: 100;\\n  padding: 10px;\\n  display: flex;\\n  flex-grow: 1;\\n  border-bottom: 2px solid rgba(0, 0, 0, 0.05);\\n  border-top: transparent;\\n  border-left: transparent;\\n  border-right: transparent;\\n  background: transparent;\\n}\\n\\n.persona-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  overflow-y: auto;\\n  padding-right: 0.5rem;\\n}\\n@media only screen and (max-width: 768px) {\\n  .persona-container[_ngcontent-%COMP%] {\\n    height: calc(100% - 50px);\\n  }\\n}\\n\\n.persona[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n  cursor: pointer;\\n  box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.16);\\n}\\n.persona[_ngcontent-%COMP%]:not(.selected) {\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #F1F3FF 100%) !important;\\n}\\n.persona[_ngcontent-%COMP%]:not(.selected):hover {\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #d1d6f9 100%) !important;\\n}\\n\\n.persona[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  height: 50px;\\n  width: 50px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.info-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-left: 10px;\\n  width: calc(100% - 50px);\\n}\\n\\n.info-name[_ngcontent-%COMP%] {\\n  font-weight: 300;\\n}\\n\\n.info-message[_ngcontent-%COMP%] {\\n  color: #B2B4BA;\\n  font-size: 0.85em;\\n  font-weight: 300;\\n  text-wrap: pretty;\\n  overflow: hidden;\\n  width: 100%;\\n  max-height: 17px;\\n}\\n\\n.detail-date[_ngcontent-%COMP%] {\\n  color: #B2B4BA;\\n  line-height: 1;\\n}\\n\\n.detail-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  font-size: 0.75em;\\n  font-weight: 300;\\n  position: absolute;\\n  right: 10px;\\n  bottom: 3px;\\n}\\n\\n.detail-section[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.unread[_ngcontent-%COMP%] {\\n  border-right: 7px solid #4f95ff;\\n}\\n\\n.selected[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  background: linear-gradient(to top right, #4f5adb, #8e8bf3);\\n}\\n.selected[_ngcontent-%COMP%]   .info-message[_ngcontent-%COMP%] {\\n  color: #c1d0ff;\\n}\\n.selected[_ngcontent-%COMP%]   .detail-date[_ngcontent-%COMP%] {\\n  color: #c1d0ff;\\n}\\n\\n\\n\\n\\n\\n.unread[_ngcontent-%COMP%]    > .detail-section[_ngcontent-%COMP%] {\\n  margin-right: -7px;\\n}\\n\\n.right[_ngcontent-%COMP%] {\\n  justify-content: right;\\n  flex-grow: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  border-radius: 20px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .right[_ngcontent-%COMP%] {\\n    height: calc(100dvh - 120px);\\n  }\\n}\\n\\n.chat-person[_ngcontent-%COMP%]    > h3[_ngcontent-%COMP%] {\\n  font-weight: 300;\\n  text-align: center;\\n  width: 100%;\\n  padding-bottom: 3px;\\n  border-bottom: 2px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.chat-person[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.chat-person[_ngcontent-%COMP%]   .inner[_ngcontent-%COMP%] {\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #F1F3FF 100%) !important;\\n  border-radius: 2rem;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex-grow: 1;\\n  width: 100%;\\n  margin-top: 10px;\\n  margin-bottom: 10px;\\n  overflow-y: auto;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  border-radius: 20px;\\n  max-width: 40%;\\n  margin: 5px;\\n  font-weight: 300;\\n}\\n\\n.msg-me[_ngcontent-%COMP%] {\\n  background-color: #4f95ff;\\n  color: #fff;\\n  margin-left: auto;\\n}\\n\\n.msg-me-time[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  padding: 0 10px 0 10px;\\n  color: #B2B4BA;\\n  font-size: 0.7em;\\n  margin-bottom: 5px;\\n  justify-content: flex-end;\\n}\\n\\n.msg-other-time[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n  padding: 0 10px 0 10px;\\n  color: #B2B4BA;\\n  font-size: 0.7em;\\n  margin-bottom: 5px;\\n  justify-content: flex-start;\\n}\\n\\n.msg-other[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  color: #0E0D0D;\\n  margin-right: auto;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  margin-top: auto;\\n  align-items: center;\\n  margin-bottom: 0;\\n  padding: 6px 8px;\\n}\\n\\n.chat-input[_ngcontent-%COMP%]    > i[_ngcontent-%COMP%] {\\n  color: #B2B4BA;\\n  margin-right: 10px;\\n  cursor: pointer;\\n  margin-right: 0;\\n}\\n\\n.chat-input[_ngcontent-%COMP%]    > textarea[_ngcontent-%COMP%] {\\n  outline: none;\\n  font-size: 13px;\\n  font-weight: 100;\\n  display: flex;\\n  flex-grow: 1;\\n  border-bottom: transparent;\\n  border-top: transparent;\\n  border-left: transparent;\\n  border-right: transparent;\\n  background: transparent;\\n  max-height: 100px;\\n  resize: none !important;\\n}\\n@media only screen and (max-width: 768px) {\\n  .chat-input[_ngcontent-%COMP%]    > textarea[_ngcontent-%COMP%] {\\n    max-height: 48px;\\n  }\\n}\\n.chat-input[_ngcontent-%COMP%]    > textarea[_ngcontent-%COMP%]::placeholder {\\n  \\n\\n  color: var(--blue-9);\\n  opacity: 0.5;\\n  \\n\\n}\\n.chat-input[_ngcontent-%COMP%]    > textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border: none;\\n  box-shadow: none;\\n}\\n\\n.chat-send-btn[_ngcontent-%COMP%] {\\n  color: #4f95ff;\\n  cursor: pointer;\\n  height: 100%;\\n  padding: 0 10px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.send[_ngcontent-%COMP%] {\\n  color: #fff;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  position: relative;\\n  font-size: 1.4em;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.send-img[_ngcontent-%COMP%] {\\n  width: 26px;\\n  height: 26px;\\n}\\n\\n.col-header[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: auto;\\n  min-height: 6rem;\\n  margin: 0;\\n  font-size: 1rem;\\n  padding: 1rem 1rem 1rem 1.5rem;\\n  border-bottom: 1px solid #4d5fc5;\\n}\\n\\nfigure[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem;\\n}\\n\\n.avatar[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-bottom: 0;\\n  height: 2.7rem;\\n  width: 2.7rem;\\n  border-radius: 50%;\\n}\\n.avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: inherit;\\n}\\n\\n.layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n}\\n\\n.layout[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n}\\n\\n.online__badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  margin: 0;\\n  height: 0.8rem;\\n  width: 0.8rem;\\n  background-color: #7fd138;\\n  color: #ffffff;\\n  border-radius: 50px;\\n}\\n\\n.purple-icon[_ngcontent-%COMP%] {\\n  color: #4f5adb;\\n}\\n\\n.speeddial-tooltip-demo[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.speed__btn[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n*[_ngcontent-%COMP%], [_ngcontent-%COMP%]:before, [_ngcontent-%COMP%]:after {\\n  text-decoration: none;\\n  box-sizing: inherit;\\n  transition-property: none;\\n  transition-duration: 0.3s;\\n}\\n\\n.fab-container[_ngcontent-%COMP%] {\\n  right: 0px;\\n  z-index: 999;\\n  cursor: pointer;\\n}\\n\\n.fab-icon-holder[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 100%;\\n  background: #016fb9;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n\\n.fab-icon-holder[_ngcontent-%COMP%]:hover {\\n  opacity: 0.8;\\n}\\n\\n.fab-icon-holder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  font-style: 25px;\\n  color: #ffffff;\\n}\\n\\n.fab[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  background: linear-gradient(to top right, #9991f7, #676CE4);\\n}\\n\\n.fab-options[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n  margin: 0;\\n  position: absolute;\\n  bottom: 40px;\\n  right: -11px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n  transform: scale(0);\\n  transform-origin: 85% bottom;\\n}\\n\\n.fab[_ngcontent-%COMP%]:hover    + .fab-options[_ngcontent-%COMP%], .fab-options[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n\\n.fab-options[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 5px;\\n}\\n\\n.fab-label[_ngcontent-%COMP%] {\\n  padding: 2px 5px;\\n  align-self: center;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  white-space: nowrap;\\n  border-radius: 3px;\\n  font-size: 16px;\\n  background: #666666;\\n  color: #ffffff;\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n  margin-right: 10px;\\n}\\n\\n.fab-line[_ngcontent-%COMP%] {\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #F1F3FF 100%) !important;\\n}\\n\\n.fab-tel[_ngcontent-%COMP%] {\\n  background: linear-gradient(rgb(255, 255, 255) 0%, #F1F3FF 100%) !important;\\n}\\n\\n.fab-email[_ngcontent-%COMP%] {\\n  background: #FE7A16;\\n}\\n\\n.flact__cont[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:before, .flact__cont[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:after {\\n  font-family: inherit;\\n  color: inherit;\\n}\\n\\n.flact[_ngcontent-%COMP%] {\\n  position: relative;\\n  bottom: 70px;\\n  right: 0;\\n  text-align: center;\\n}\\n.flact[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n  position: relative;\\n  border-radius: 50%;\\n  border: none;\\n  cursor: pointer;\\n  color: transparent;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\\n  transition-property: box-shadow, opacity, transform;\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  background-color: #6555d5;\\n  z-index: 1;\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_option[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background-color: #E0E0E0;\\n  opacity: 0;\\n  transform: translateY(50px);\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_option[_ngcontent-%COMP%]:nth-last-child(1) {\\n  transition-delay: 100ms;\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_option[_ngcontent-%COMP%]:nth-last-child(2) {\\n  transition-delay: 200ms;\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_option[_ngcontent-%COMP%]:nth-last-child(3) {\\n  transition-delay: 300ms;\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_option[_ngcontent-%COMP%]:nth-last-child(4) {\\n  transition-delay: 400ms;\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_option[_ngcontent-%COMP%]:nth-last-child(5) {\\n  transition-delay: 500ms;\\n}\\n.flact[_ngcontent-%COMP%]   button.flact_option[_ngcontent-%COMP%]:nth-last-child(6) {\\n  transition-delay: 600ms;\\n}\\n.flact[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);\\n}\\n.flact[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:active, .flact[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);\\n}\\n.flact[_ngcontent-%COMP%]:hover   .flact_option[_ngcontent-%COMP%], .flact[_ngcontent-%COMP%]:focus   .flact_option[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: none;\\n}\\n\\n  .p-tabview .p-tabview-title {\\n  color: #4354b4;\\n}\\n\\n.group__badge[_ngcontent-%COMP%] {\\n  position: ABSOLUTE;\\n  right: 15px;\\n  top: 4px;\\n  font-size: 0.7rem;\\n  height: 1.325rem;\\n  line-height: 1.325rem;\\n}\\n.group__badge.p-badge-classroom[_ngcontent-%COMP%] {\\n  background-color: #7d79ea;\\n}\\n.group__badge.p-badge-info[_ngcontent-%COMP%] {\\n  background-color: #3F51B5;\\n}\\n\\n.new-message-count[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 22px;\\n  height: 22px;\\n  position: absolute;\\n  font-size: 12px;\\n  background-color: #e6e6e6;\\n  color: #000;\\n  border: 2px solid #e6e6e6;\\n  border-radius: 50%;\\n  right: -6px;\\n  top: -10px;\\n  background-color: #09c !important;\\n  color: #fff !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [trigger('scaleToggle', [transition('shown <=> hidden', [animate('0.3s ease-in-out')])])]\n    }\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "Injector", "computed", "inject", "input", "signal", "viewChild", "BehaviorSubject", "of", "switchMap", "AuthService", "ChatService", "ClassroomService", "UserService", "SubSink", "serverTimestamp", "Timestamp", "animate", "transition", "trigger", "ButtonModule", "GeneralService", "SharedModule", "DialogService", "ChatUploadFileDialogComponent", "LibraryService", "FileUploadModule", "HttpHeaders", "HttpEventType", "HttpClient", "environment", "InputTextModule", "FormControl", "ToastService", "ChatMessagesDisplayComponent", "BadgeModule", "InputTextareaModule", "SpeedDialModule", "toObservable", "TooltipModule", "TabViewModule", "i0", "ɵɵelementContainer", "ɵɵelementStart", "ɵɵtemplate", "ChatComponent_Defer_0_Conditional_0_div_9_ng_container_2_Template", "ChatComponent_Defer_0_Conditional_0_div_9_ng_container_3_Template", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction2", "_c4", "ctx_r0", "isTabletOrMobile", "ɵɵadvance", "personSearchTemplate_r2", "personBoxTemplate_r3", "ChatComponent_Defer_0_Conditional_0_div_10_Conditional_6_ng_container_0_Template", "emptyMessagesTemplate_r5", "ChatComponent_Defer_0_Conditional_0_div_10_ng_container_1_Template", "ɵɵlistener", "ChatComponent_Defer_0_Conditional_0_div_10_Template_button_click_4_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "loadMoreMessages", "currentChatId", "ɵɵtext", "ChatComponent_Defer_0_Conditional_0_div_10_Conditional_6_Template", "ChatComponent_Defer_0_Conditional_0_div_10_Template_app_chat_messages_display_deleteMessageSelected_7_listener", "$event", "deleteMessage", "ChatComponent_Defer_0_Conditional_0_div_10_Template_app_chat_messages_display_editMessageSelected_7_listener", "editMessage", "ChatComponent_Defer_0_Conditional_0_div_10_ng_container_8_Template", "chatPersonTemplate_r6", "ɵɵpureFunction1", "_c5", "editMessageMode", "ɵɵconditional", "messages", "length", "chatsList", "inputTextTemplate_r7", "ɵɵelement", "ChatComponent_Defer_0_Conditional_0_div_9_Template", "ChatComponent_Defer_0_Conditional_0_div_10_Template", "showLeftSideMobile", "showRightSideMobile", "ChatComponent_Defer_0_Conditional_0_Template", "bubbleMode", "ChatComponent_Conditional_4_div_3_div_11_Conditional_6_ng_container_0_Template", "ChatComponent_Conditional_4_div_3_div_11_ng_container_4_Template", "ChatComponent_Conditional_4_div_3_div_11_Conditional_6_Template", "showInnerChat", "ChatComponent_Conditional_4_div_3_div_12_Conditional_10_ng_container_0_Template", "ChatComponent_Conditional_4_div_3_div_12_ng_container_7_Template", "ChatComponent_Conditional_4_div_3_div_12_Conditional_10_Template", "ChatComponent_Conditional_4_div_3_div_12_Template_app_chat_messages_display_editMessageSelected_11_listener", "_r10", "ChatComponent_Conditional_4_div_3_div_12_Template_app_chat_messages_display_deleteMessageSelected_11_listener", "ChatComponent_Conditional_4_div_3_div_19_ng_container_3_Template", "ChatComponent_Conditional_4_div_3_Template_i_click_6_listener", "_r9", "toggleChat", "ChatComponent_Conditional_4_div_3_Template_span_click_7_listener", "ChatComponent_Conditional_4_div_3_div_11_Template", "ChatComponent_Conditional_4_div_3_div_12_Template", "ChatComponent_Conditional_4_div_3_div_19_Template", "chatBoxState", "ɵɵsanitizeUrl", "_c6", "ChatComponent_Conditional_4_Template_div_click_0_listener", "_r8", "ChatComponent_Conditional_4_div_3_Template", "chatCircleState", "chatService", "unreadCount", "chatBoxVisible", "ChatComponent_ng_template_5_p_button_3_Template_p_button_click_0_listener", "_r12", "showBackUsers", "ChatComponent_ng_template_5_p_button_3_Template", "ChatComponent_ng_template_5_Template_img_error_5_listener", "_r11", "generalService", "setDefaultUserAvatar", "ChatComponent_ng_template_5_Conditional_6_Template", "getDomainFileNamePath", "getChatGroupAvatarUrl", "activeUser", "isUserOnline", "getChatGroupAspUserId", "ɵɵtextInterpolate", "chatName", "ChatComponent_ng_template_7_Conditional_0_Template_i_click_5_listener", "_r14", "disableEditAndTextarea", "resetTextarea", "ɵɵelementContainerStart", "ChatComponent_ng_template_7_ng_template_16_Template_p_fileUpload_onSelect_0_listener", "_r15", "onBasicUploadAuto", "ChatComponent_ng_template_7_ng_template_16_ng_template_1_Template", "ChatComponent_ng_template_7_Conditional_0_Template", "ChatComponent_ng_template_7_ng_container_6_Template", "ChatComponent_ng_template_7_ng_template_7_Template", "ɵɵtemplateRefExtractor", "ChatComponent_ng_template_7_Template_div_click_11_listener", "_r13", "openFileUploadDialog", "ChatComponent_ng_template_7_ng_container_15_Template", "ChatComponent_ng_template_7_ng_template_16_Template", "ChatComponent_ng_template_7_Template_textarea_input_18_listener", "adjustParentHeight", "ChatComponent_ng_template_7_Template_textarea_paste_18_listener", "onPaste", "ChatComponent_ng_template_7_Template_textarea_focus_18_listener", "onFocusTextarea", "ChatComponent_ng_template_7_Template_textarea_click_18_listener", "autoResizeTextArea_r16", "ɵɵreference", "focus", "ChatComponent_ng_template_7_Template_textarea_keydown_enter_18_listener", "addNewMessage", "ChatComponent_ng_template_7_Template_div_click_20_listener", "_c7", "_c8", "uploadProgress", "notUploading_r17", "notUpload_r18", "ChatComponent_ng_template_9_Template_input_input_2_listener", "_r19", "onSearchChange", "countLastMessageCount", "tmp_12_0", "ɵɵpipeBind1", "lastMessages$", "chatGroup_r21", "classroomId", "ChatComponent_ng_template_11_div_0_ng_container_11_Conditional_1_Template", "ChatComponent_ng_template_11_div_0_ng_container_11_Conditional_3_Template", "lastMessageDeleted", "tmp_13_0", "lastMessage", "ɵɵsanitizeHtml", "ChatComponent_ng_template_11_div_0_ng_template_13_Conditional_0_Template", "ChatComponent_ng_template_11_div_0_ng_template_13_Conditional_2_Template", "ɵɵpipeBind2", "lastMessageAt", "seconds", "ChatComponent_ng_template_11_div_0_Template_div_click_0_listener", "_r20", "$implicit", "setUserActive", "ChatComponent_ng_template_11_div_0_Template_img_error_2_listener", "ChatComponent_ng_template_11_div_0_Conditional_3_Template", "ChatComponent_ng_template_11_div_0_Conditional_4_Template", "ChatComponent_ng_template_11_div_0_ng_container_7_Template", "ChatComponent_ng_template_11_div_0_ng_container_11_Template", "ChatComponent_ng_template_11_div_0_ng_template_13_Template", "ChatComponent_ng_template_11_div_0_span_16_Template", "_c9", "tmp_14_0", "chatTypeBadgeTemplate_r23", "_c10", "tmp_18_0", "lastMessageIsFile", "notFileChat_r22", "tmp_20_0", "ChatComponent_ng_template_11_div_0_Template", "filteredChats", "ɵɵtextInterpolate1", "relatedName", "ChatComponent_ng_template_15_Conditional_0_Template", "ChatComponent_ng_template_15_Conditional_1_Template", "ChatComponent_ng_template_15_Conditional_2_Template", "chatGroup_r24", "chatType", "CLASSROOM", "classroomStudents", "USER", "ChatType", "ChatComponent", "constructor", "injector", "subs", "term", "currentIndex", "title", "libraryService", "authService", "classroomService", "userService", "dialogService", "toastService", "http", "loading", "files", "onlineUsers", "chatListsLoaded", "messageTimestamps", "editMessageisLastMessage", "lastMessages", "lastMessagesSubject", "asObservable", "expandStatuses", "expanded", "searchControl", "user", "getLoggedInUser", "myScrollContainer", "filter", "chat", "getChatGroupName", "toLowerCase", "includes", "chats", "leftTooltipItems", "pastedImage", "undefined", "imageInfo", "lastFetchedMessage", "messageCache", "Map", "limit", "offset", "isSubscribed", "reloadChats", "event", "target", "set", "value", "listenToReloadChats", "subscribe", "next", "data", "console", "log", "subscribeToChatMessages", "initChatFunctionality", "ngOnInit", "getFireBaseUser", "res", "valueChanges", "loadOnlineStatus", "listenChatListsLoaded", "loadMobileLayoutCheck", "tooltipOptions", "tooltipLabel", "tooltipPosition", "icon", "command", "url", "ngAfterViewChecked", "ngAfterViewInit", "ngOnDestroy", "intervalId", "clearInterval", "unsubscribe", "markMessagesAsRead", "aspUserId", "element", "autoResizeTextArea", "nativeElement", "style", "height", "scrollHeight", "onInput", "adjustHeight", "textarea", "fromClipboard", "selectedFile", "currentFiles", "headers", "formData", "FormData", "append", "name", "split", "pop", "getToken", "myFile", "progress", "file", "index", "uploadRequest", "endpointExtras", "add", "post", "apiUrl", "reportProgress", "observe", "type", "UploadProgress", "Math", "round", "loaded", "total", "Response", "body", "sendMessage", "db<PERSON><PERSON>", "setTimeout", "scrollToBottom", "err", "chatClassroom", "<PERSON><PERSON><PERSON>er", "getFormattedStudentNames", "language", "teacher", "firstName", "getChatOppositeName", "avatarUrl", "sink", "getLMSUserClassrooms", "id", "initTeacherChats", "isStudent", "initStudentChats", "retrieveChats", "classrooms", "uniqueStudentIds", "Set", "map", "classroom", "classroomChat", "createClassroomChat", "studentChats", "student", "has", "createStudentChat", "flat", "uniqueTeacherIds", "individualChat", "createIndividualChat", "teacherId", "getShortFullName", "classroomLevel", "activeLevel", "classroomLanguage", "chatHistory", "studentId", "loadChatMessages", "chatId", "unsubscribeFromChatMessages", "cachedData", "get", "isCache<PERSON><PERSON>d", "applyCachedMessages", "currentChatSubscription", "warn", "getChatMessages", "reversedMessages", "reverse", "cacheMessages", "existingMessages", "updatedMessages", "markLastMessagesAsRead", "clearCachedMessages", "clear", "inputField", "now", "Date", "timestamp", "oldestTimestamp", "min", "waitTime", "floor", "setShowToastmessage", "severity", "summary", "detail", "push", "val", "trim", "replace", "tempDiv", "document", "createElement", "innerHTML", "editMessageObj", "content", "rows", "message", "isUploadFile", "deleteChatFilesFromLibraryFolder", "error", "formattedContent", "scrollTop", "currentRoom", "refreshList", "currentTutorial", "getUnreadMessages", "chatListsWithUnreadCounts", "for<PERSON>ach", "getLastUnreadMessagesCountForUser", "count", "chatListWithUnreadCount", "lastName", "totalUnreadCount", "Object", "values", "reduce", "acc", "current", "getValue", "getLastMessage", "updateChatsListSorting", "sort", "a", "b", "aLastMessage", "bLastMessage", "sortUserListByChats", "userTimestampMap", "participants", "participant", "findAvatarUrl", "getUserFullNameAndInitials", "fullName", "initials", "char<PERSON>t", "isFileFromLibrary", "senderId", "readBy", "readStatus", "addMessage", "isImage", "filePath", "imageExtensions", "fileExtension", "goToChat", "divToShowDialog", "isPreviewingFromChat", "setNewFileUpload", "myElement", "getElementById", "dialogWidth", "getBoundingClientRect", "width", "openDialogWithComponent", "toFixed", "action", "dialogsubTitle", "dialogTitle", "result", "showUploadFile", "checkedLibraryFiles", "Array", "isArray", "item", "path", "items", "clipboardData", "originalEvent", "blob", "indexOf", "getAsFile", "recognizeImage", "imageData", "setUserStatus", "initUserOnlineInterval", "setUserLastOnlineTime", "getOnlineUsersComparedToLastOnlineTime", "users", "setInterval", "secondsToCheckOnlineStatus", "userId", "currentTime", "some", "lastOnlineTime", "timeDifference", "deviceKind", "pipe", "w992up", "w1024up", "menuHeight", "autoResize", "textArea", "unreadCounts", "_", "_2", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ChatComponent_Template", "ChatComponent_Defer_0_Template", "ChatComponent_DeferPlaceholder_1_Template", "ɵɵdefer", "ChatComponent_Defer_2_DepsFn", "ɵɵdeferOnViewport", "ChatComponent_Conditional_4_Template", "ChatComponent_ng_template_5_Template", "ChatComponent_ng_template_7_Template", "ChatComponent_ng_template_9_Template", "ChatComponent_ng_template_11_Template", "ChatComponent_ng_template_13_Template", "ChatComponent_ng_template_15_Template", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "AsyncPipe", "DatePipe", "i2", "<PERSON><PERSON>", "i3", "PrimeTemplate", "i4", "<PERSON><PERSON><PERSON>", "i5", "FileUpload", "i6", "InputText", "i7", "Badge", "styles", "animation"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\chat\\chat.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\chat\\chat.component.html"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\r\nimport { ChangeDetectionStrategy, Component, ElementRef, HostListener, Inject, Injector, QueryList, Signal, ViewChild, ViewChildren, computed, inject, input, signal, viewChild } from '@angular/core';\r\nimport * as firebase from \"firebase/compat\";\r\nimport * as moment from \"moment\";\r\nimport { BehaviorSubject, Observable, distinct, filter, forkJoin, map, of, switchMap, take } from \"rxjs\";\r\nimport { Classroom } from \"src/app/core/models/classroom.model\";\r\nimport { ChatMessage, FireDBChat } from \"src/app/core/models/firebase-chat.model\";\r\nimport { AuthService } from \"src/app/core/services/auth.service\";\r\nimport { ChatService, ChatUserStatus, Message } from \"src/app/core/services/chat.service\";\r\nimport { ClassroomService } from \"src/app/core/services/classroom.service\";\r\nimport { UserService } from \"src/app/core/services/user.service\";\r\nimport { SubSink } from \"subsink\";\r\nimport { collection, addDoc, serverTimestamp, FieldValue, Timestamp } from 'firebase/firestore';\r\nimport { User } from \"src/app/core/models/user.model\";\r\nimport { animate, state, style, transition, trigger } from \"@angular/animations\";\r\nimport { ButtonModule } from \"primeng/button\";\r\nimport { GeneralService } from \"src/app/core/services/general.service\";\r\nimport { LibraryFile } from \"src/app/core/models/library.model\";\r\nimport { SharedModule } from \"src/app/shared/shared.module\";\r\nimport { DialogService, DynamicDialogRef } from \"primeng/dynamicdialog\";\r\nimport { ChatUploadFileDialogComponent } from \"./components/chat-upload-file-dialog/chat-upload-file-dialog.component\";\r\nimport { LibraryService } from \"src/app/core/services/library.service\";\r\nimport { FileUploadModule, UploadEvent } from 'primeng/fileupload';\r\nimport { HttpHeaders, HttpEventType, HttpClient } from \"@angular/common/http\";\r\nimport { MyFile } from \"src/app/shared/upload-files/upload-files.component\";\r\nimport { environment } from \"src/environments/environment\";\r\nimport { InputTextModule } from \"primeng/inputtext\";\r\nimport { FormControl } from \"@angular/forms\";\r\nimport { ToastService } from \"src/app/core/services/toast.service\";\r\nimport { ChatMessagesDisplayComponent } from \"./components/chat-messages-display/chat-messages-display.component\";\r\nimport { BadgeModule } from \"primeng/badge\";\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { SpeedDialModule } from 'primeng/speeddial';\r\nimport { Dialog } from \"primeng/dialog\";\r\nimport { toObservable } from \"@angular/core/rxjs-interop\";\r\nimport { TooltipModule } from \"primeng/tooltip\";\r\nimport { TabViewModule } from \"primeng/tabview\";\r\n\r\ninterface LastMessage {\r\n    lastMessageFromImage: string;\r\n    lastMessage: string;\r\n    lastMessageAt: { seconds: number };\r\n    // add other properties if necessary\r\n}\r\n\r\ninterface LastMessagesState {\r\n    [key: string]: LastMessage;\r\n}\r\n\r\nenum ChatType {\r\n    CLASSROOM = \"classroom\",\r\n    USER = \"user\",\r\n}\r\n\r\n@Component({\r\n    selector: 'app-chat',\r\n    standalone: true,\r\n    imports: [\r\n        CommonModule,\r\n        ButtonModule,\r\n        SharedModule,\r\n        FileUploadModule,\r\n        InputTextModule,\r\n        InputTextareaModule,\r\n        SpeedDialModule,\r\n        BadgeModule,\r\n        TooltipModule,\r\n        TabViewModule,\r\n        ChatMessagesDisplayComponent,\r\n    ],\r\n\r\n    templateUrl: './chat.component.html',\r\n    styleUrls: ['./chat.component.scss'],\r\n    animations: [\r\n        trigger('scaleToggle', [\r\n            transition('shown <=> hidden', [animate('0.3s ease-in-out')]),\r\n        ]),\r\n    ],\r\n    providers: [DialogService]\r\n})\r\nexport class ChatComponent {\r\n    @ViewChild('autoResizeTextArea', { static: false }) autoResizeTextArea!: ElementRef;\r\n    @ViewChild('textareaWrapper', { static: false }) textareaWrapper!: ElementRef;\r\n    @ViewChild('editForbitDialog', { static: false }) editForbitDialog!: Dialog;\r\n\r\n    private injector = inject(Injector);\r\n    bubbleMode = input(false);\r\n    private subs = new SubSink();\r\n    term = signal('');\r\n    tutorials?: any[];\r\n    currentTutorial?: any;\r\n    currentIndex = -1;\r\n    title = '';\r\n    chatService = inject(ChatService);\r\n    libraryService = inject(LibraryService);\r\n    authService = inject(AuthService);\r\n    classroomService = inject(ClassroomService);\r\n    userService = inject(UserService);\r\n    generalService = inject(GeneralService);\r\n    dialogService = inject(DialogService);\r\n    toastService = inject(ToastService);\r\n    http = inject(HttpClient);\r\n    loading = false;\r\n    files = [] as LibraryFile[];\r\n    onlineUsers: ChatUserStatus[] = [];\r\n\r\n    chatListsLoaded = signal(false);\r\n\r\n    messageTimestamps: number[] = [];\r\n    activeUser!: any;\r\n    editMessageObj!: any;\r\n    editMessageisLastMessage = false;\r\n    editMessageMode = signal(false);\r\n    messages = signal([] as Message[]);\r\n    lastMessages = signal([] as any[]);\r\n    private lastMessagesSubject = new BehaviorSubject<any[]>([]);\r\n    lastMessages$ = this.lastMessagesSubject.asObservable();\r\n    expandStatuses = false;\r\n    expanded = false;\r\n    searchControl = new FormControl('');\r\n    chatMessages$!: Observable<ChatMessage[]>;\r\n    user = this.authService.getLoggedInUser();\r\n\r\n    isTabletOrMobile = false;\r\n    myScrollContainer: Signal<ElementRef | undefined> = viewChild('scrollMe');\r\n    // @ViewChild('scrollMe') private myScrollContainer: ElementRef = {} as ElementRef;\r\n    currentChatId: string = '';\r\n    chatsList = signal([] as any[]);\r\n    filteredChats = computed(() => {\r\n        if (!this.term()) {\r\n            return this.chatsList();\r\n        } else {\r\n            return this.chatsList().filter(chat =>\r\n                this.getChatGroupName(chat).toLowerCase().includes(this.term().toLowerCase())\r\n            );\r\n        }\r\n    });\r\n    currentChatSubscription: any;\r\n    showInnerChat = false;\r\n\r\n    chatCircleState: string = 'shown';\r\n    chatBoxState: string = 'hidden';\r\n    chatBoxVisible = false;\r\n    chats = signal([] as any[]);\r\n    uploadProgress = 0;\r\n    leftTooltipItems = [] as any[];\r\n    pastedImage: any = undefined;\r\n    imageInfo: string = '';\r\n    chatType = ChatType;\r\n    private intervalId: any;\r\n\r\n    private lastFetchedMessage: Message | null = null;  // Track the last message for pagination\r\n    private messageCache: Map<string, { messages: Message[]; lastFetchedMessage: Message | null }> = new Map();\r\n    limit = 15; // Number of messages to load at a time\r\n    offset = 0; // Current offset\r\n    isSubscribed: boolean = false;\r\n    reloadChats = signal(false);\r\n    onSearchChange(event: any) {\r\n        const input = event.target as HTMLInputElement;\r\n        this.term.set(input.value);\r\n    }\r\n\r\n    listenToReloadChats() {\r\n        toObservable(this.reloadChats, {\r\n            injector: this.injector\r\n        }).subscribe({\r\n            next: (data) => {\r\n                console.log(data);\r\n                if (data) {\r\n                    this.subscribeToChatMessages(this.currentChatId);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    toggleChat() {\r\n        this.chatCircleState = this.chatCircleState === 'shown' ? 'hidden' : 'shown';\r\n        this.chatBoxState = this.chatBoxState === 'shown' ? 'shown' : 'hidden';\r\n        this.chatBoxVisible = !this.chatBoxVisible;\r\n        this.activeUser = undefined;\r\n        this.showInnerChat = false;\r\n        if (this.chatBoxVisible) {\r\n            this.initChatFunctionality();\r\n        }\r\n    }\r\n\r\n    ngOnInit(): void {\r\n\r\n        this.chatService.getFireBaseUser().subscribe((res) => {\r\n        })\r\n        // this.chatService.createFirebaseUser('<EMAIL>', '12213213231').subscribe({\r\n        //     next: (data: any) => {\r\n        //         console.log(data);\r\n        //     },\r\n        //     error: (err: any) => {\r\n        //         console.log(err);\r\n        //     }\r\n        // })\r\n        // this.chatService.signInAnonymously().subscribe({\r\n        //     next: (res) => {\r\n        //         console.log(res);\r\n        //     },\r\n        //     error: (err) => {\r\n\r\n        //     }\r\n        // })\r\n        this.user = this.authService.getLoggedInUser();\r\n\r\n        if (!this.bubbleMode()) {\r\n\r\n            this.initChatFunctionality();\r\n\r\n        }\r\n        this.searchControl.valueChanges.subscribe((res) => {\r\n        });\r\n\r\n        this.loadOnlineStatus();\r\n        this.listenChatListsLoaded();\r\n        this.listenToReloadChats();\r\n        this.loadMobileLayoutCheck();\r\n\r\n        this.leftTooltipItems = [\r\n            {\r\n                tooltipOptions: {\r\n                    tooltipLabel: 'Add',\r\n                    tooltipPosition: 'left'\r\n                },\r\n                icon: 'pi pi-pencil',\r\n                command: () => {\r\n                }\r\n            },\r\n            {\r\n                tooltipOptions: {\r\n                    tooltipLabel: 'Update',\r\n                    tooltipPosition: 'left'\r\n                },\r\n                icon: 'pi pi-refresh',\r\n                command: () => {\r\n                }\r\n            },\r\n            {\r\n                tooltipOptions: {\r\n                    tooltipLabel: 'Delete',\r\n                    tooltipPosition: 'left'\r\n                },\r\n                icon: 'pi pi-trash',\r\n                command: () => {\r\n                }\r\n            },\r\n            {\r\n                icon: 'pi pi-upload',\r\n                tooltipOptions: {\r\n                    tooltipLabel: 'Upload',\r\n                    tooltipPosition: 'left'\r\n                }\r\n            },\r\n            {\r\n                tooltipOptions: {\r\n                    tooltipLabel: 'Angular Website',\r\n                    tooltipPosition: 'left'\r\n                },\r\n                icon: 'pi pi-external-link',\r\n                url: 'http://angular.io'\r\n            }\r\n        ];\r\n\r\n    }\r\n    ngAfterViewChecked() {\r\n    }\r\n\r\n    ngAfterViewInit(): void {\r\n        // Initially set the height of each textarea\r\n        // this.textareas.forEach(textarea => {\r\n        //     this.adjustHeight(textarea.nativeElement);\r\n        // });\r\n\r\n        // // Listen for input event on each textarea to adjust the height\r\n        // this.textareas.forEach(textarea => {\r\n        //     console.log(textarea);\r\n        //     textarea.nativeElement.addEventListener('input', this.onInput.bind(this));\r\n        // });\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        if (this.intervalId) {\r\n            clearInterval(this.intervalId);\r\n            console.log('Interval cleared');\r\n        }\r\n        this.showInnerChat = false;\r\n        this.subs.unsubscribe();\r\n    }\r\n\r\n    onFocusTextarea() {\r\n        console.log('focus');\r\n        this.chatService.markMessagesAsRead(this.currentChatId, this.user.aspUserId);\r\n    }\r\n\r\n    adjustParentHeight() {\r\n        // Assuming you have the implementation for this method to adjust the textarea height\r\n        const element = this.autoResizeTextArea.nativeElement;\r\n        element.style.height = 'auto';\r\n        element.style.height = `${element.scrollHeight}px`;\r\n    }\r\n\r\n    onInput(event: any): void {\r\n        this.adjustHeight(event.target);\r\n    }\r\n\r\n    adjustHeight(textarea: HTMLTextAreaElement): void {\r\n        textarea.style.height = 'auto'; // Reset height to auto to get the actual scrollHeight\r\n        textarea.style.height = `${textarea.scrollHeight}px`; // Set height based on scrollHeight\r\n    }\r\n\r\n    onBasicUploadAuto(event: any, fromClipboard = false) {\r\n        console.log(event);\r\n        let selectedFile = {} as File;\r\n        if (fromClipboard) {\r\n            selectedFile = event;\r\n        } else {\r\n            selectedFile = event.currentFiles[0] as File;\r\n        }\r\n\r\n        if (selectedFile) {\r\n            let headers = new HttpHeaders();  // Add any headers if needed\r\n            const formData = new FormData();\r\n            formData.append('file', selectedFile);\r\n            headers = new HttpHeaders({\r\n                'documentType': selectedFile.name.split('.').pop()!,\r\n                \"Authorization\": \"Bearer \" + this.authService.getToken(),\r\n            });\r\n            const myFile: MyFile = {\r\n                progress: 0,\r\n                file: selectedFile,\r\n                index: 0\r\n            };\r\n            this.uploadRequest(headers, '', formData, myFile);\r\n        }\r\n    }\r\n\r\n    uploadRequest(headers: HttpHeaders, endpointExtras: string, formData: FormData, myFile: MyFile) {\r\n        this.subs.add(this.http.post(environment.apiUrl + '/Upload/UploadLibraryFile' + endpointExtras, formData, {\r\n            headers: headers,\r\n            reportProgress: true,\r\n            observe: 'events'\r\n        })\r\n            .subscribe((event: any) => {\r\n\r\n                if (event.type === HttpEventType.UploadProgress) {\r\n                    myFile.progress = Math.round(100 * event.loaded / event.total!);\r\n                    this.uploadProgress = myFile.progress;\r\n                } else if (event.type === HttpEventType.Response) {\r\n                    console.log('Upload complete:', event.body);\r\n                    this.sendMessage(event.body.dbPath, false, true);\r\n                    setTimeout(() => {\r\n                        this.scrollToBottom();\r\n                    }, 800);\r\n                    this.uploadProgress = 0;\r\n                    // Handle the response from the server, e.g., store the file path or show a success message\r\n                }\r\n\r\n                if (event.loaded === event.total) {\r\n                    console.log(HttpEventType);\r\n                }\r\n                if (event.type === HttpEventType.UploadProgress) {\r\n                }\r\n                else if (event.type === HttpEventType.Response) {\r\n\r\n                    // this.uploadedFile = event.body;\r\n                    // if (this.isProfile) {\r\n                    //     console.log('GEGEG');\r\n                    //     this.showCropper = false;\r\n                    // }\r\n                    // this.onUploadFinished.emit({ filePath: event.body, task: this.task });\r\n                } else {\r\n                }\r\n            }, (err: any) => {\r\n                console.log(err)\r\n            }));\r\n    }\r\n\r\n    getChatGroupName(chatClassroom: Classroom) {\r\n        if (!chatClassroom) {\r\n            return '';\r\n        }\r\n        if (this.authService.isTeacher) {\r\n            return this.classroomService.getFormattedStudentNames(chatClassroom.classroomStudents);\r\n        } else {\r\n            return chatClassroom.language + ' - ' + chatClassroom.teacher?.firstName;\r\n        }\r\n    }\r\n\r\n    getChatOppositeName(chatClassroom: Classroom) {\r\n        if (!chatClassroom) {\r\n            return '';\r\n        }\r\n        if (this.authService.isTeacher) {\r\n            return this.classroomService.getFormattedStudentNames(chatClassroom.classroomStudents);\r\n        } else {\r\n            return chatClassroom.teacher?.firstName;\r\n        }\r\n    }\r\n\r\n    getChatGroupAvatarUrl(chatClassroom: Classroom) {\r\n        if (!chatClassroom) {\r\n            return '';\r\n        }\r\n        if (this.authService.isTeacher) {\r\n            return chatClassroom.classroomStudents[0].avatarUrl;\r\n        } else {\r\n            return chatClassroom.teacher?.avatarUrl;\r\n        }\r\n    }\r\n\r\n    getChatGroupAspUserId(chatClassroom: Classroom) {\r\n        if (!chatClassroom) {\r\n            return '';\r\n        }\r\n        if (this.authService.isTeacher) {\r\n            return chatClassroom.classroomStudents[0].aspUserId;\r\n        } else {\r\n            return chatClassroom.teacher?.aspUserId;\r\n        }\r\n    }\r\n\r\n    initChatFunctionality() {\r\n        this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(res => {\r\n            if (this.authService.isTeacher) {\r\n                this.initTeacherChats(res);\r\n            } else if (this.authService.isStudent) {\r\n                this.initStudentChats(res);\r\n            }\r\n            this.retrieveChats(res);\r\n\r\n        });\r\n\r\n    }\r\n\r\n    initTeacherChats(classrooms: Classroom[]) {\r\n        const uniqueStudentIds = new Set<number>();\r\n        const chats = classrooms.map(classroom => {\r\n            const classroomChat = this.createClassroomChat(classroom);\r\n            // TODO: this needed to prevent repeated student chats \r\n            if (classroom.classroomStudents.length === 1) {\r\n                return [classroomChat];\r\n            }\r\n            const studentChats = classroom.classroomStudents\r\n                .filter(student => !uniqueStudentIds.has(+student.id))\r\n                .map(student => {\r\n                    uniqueStudentIds.add(+student.id);\r\n                    return this.createStudentChat(classroom, student);\r\n                });\r\n            return [classroomChat, ...studentChats];\r\n        });\r\n        this.chatsList.set(chats.flat());\r\n    }\r\n\r\n    initStudentChats(classrooms: Classroom[]) {\r\n        const uniqueTeacherIds = new Set<number>();\r\n        const chats = classrooms.map(classroom => {\r\n            const classroomChat = this.createClassroomChat(classroom);\r\n            const individualChat = this.createIndividualChat(classroom);\r\n\r\n\r\n            if (classroom.classroomStudents.length > 1) {\r\n                return [classroomChat, individualChat];\r\n            }\r\n            // return [classroomChat];\r\n            if (!uniqueTeacherIds.has(+individualChat.teacherId!)) {\r\n                uniqueTeacherIds.add(+individualChat.teacherId!);\r\n                return [classroomChat];\r\n            } else {\r\n                return [classroomChat];\r\n            }\r\n        });\r\n        this.chatsList.set(chats.flat());\r\n    }\r\n\r\n    createClassroomChat(classroom: Classroom) {\r\n\r\n        let name = '';\r\n\r\n        if (this.authService.isTeacher) {\r\n            name = this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\r\n        } else {\r\n            // student\r\n            if (classroom.classroomStudents.length > 1) {\r\n                name = this.generalService.getShortFullName(classroom.teacher) + ' - ' + this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\r\n            } else {\r\n                name = this.generalService.getShortFullName(classroom.teacher);\r\n            }\r\n        }\r\n\r\n        return {\r\n            classroomId: classroom.id,\r\n            classroomLevel: classroom.activeLevel,\r\n            classroomLanguage: classroom.language,\r\n            teacher: classroom.teacher,\r\n            chatType: ChatType.CLASSROOM,\r\n            chatName: name,\r\n            chatHistory: [],\r\n            classroomStudents: classroom.classroomStudents,\r\n            relatedName: this.classroomService.getFormattedStudentNames(classroom.classroomStudents),\r\n        };\r\n    }\r\n\r\n    createStudentChat(classroom: Classroom, student: User) {\r\n\r\n        let name = '';\r\n\r\n        if (this.authService.isTeacher) {\r\n            name = this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\r\n        } else {\r\n            // student\r\n            if (classroom.classroomStudents.length > 1) {\r\n                name = this.generalService.getShortFullName(classroom.teacher) + ' - ' + this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\r\n            } else {\r\n                name = this.generalService.getShortFullName(classroom.teacher);\r\n            }\r\n        }\r\n        return {\r\n            classroomId: `teacher-${classroom.teacher?.id}-student-${student.aspUserId}`,\r\n            studentId: student.id,\r\n            relatedName: name,\r\n            chatType: ChatType.USER,\r\n            chatName: this.generalService.getShortFullName(student),\r\n            chatHistory: [],\r\n            teacher: classroom.teacher,\r\n            classroomStudents: classroom.classroomStudents,\r\n        };\r\n    }\r\n\r\n    createIndividualChat(classroom: Classroom) {\r\n        let name = '';\r\n\r\n        if (this.authService.isTeacher) {\r\n            name = this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\r\n        } else {\r\n            // student\r\n            if (classroom.classroomStudents.length > 1) {\r\n                name = this.generalService.getShortFullName(classroom.teacher);\r\n            } else {\r\n                name = this.generalService.getShortFullName(classroom.teacher);\r\n            }\r\n        }\r\n\r\n        return {\r\n            classroomId: `teacher-${classroom.teacher?.id}-student-${this.user.id}`,\r\n            teacherId: classroom.teacher?.id,\r\n            studentId: this.user.id,\r\n            chatType: ChatType.USER,\r\n            chatName: name,\r\n            chatHistory: [],\r\n            teacher: classroom.teacher,\r\n            classroomStudents: classroom.classroomStudents,\r\n            relatedName: this.classroomService.getFormattedStudentNames(classroom.classroomStudents),\r\n        };\r\n    }\r\n    loadChatMessages(chatId: string) {\r\n        this.unsubscribeFromChatMessages();\r\n        this.currentChatId = chatId;\r\n        this.lastFetchedMessage = {} as Message;  // Resetear último mensaje para paginación\r\n\r\n        // Obtener los datos en caché y verificar si el caché es válido\r\n        const cachedData = this.messageCache.get(chatId);\r\n        if (cachedData && this.isCacheValid(cachedData)) {\r\n            this.applyCachedMessages(cachedData);\r\n            console.log('Using cached messages');\r\n        } else {\r\n            // Si el caché no es válido, hacer la suscripción a los mensajes del servidor\r\n            this.subscribeToChatMessages(chatId);\r\n        }\r\n    }\r\n\r\n    private applyCachedMessages(cachedData: { messages: Message[], lastFetchedMessage: Message | null; }) {\r\n        this.messages.set(cachedData.messages);\r\n        this.lastFetchedMessage = cachedData.lastFetchedMessage;\r\n    }\r\n\r\n    unsubscribeFromChatMessages() {\r\n        if (this.currentChatSubscription) {\r\n            this.currentChatSubscription.unsubscribe();\r\n            this.currentChatSubscription = null;\r\n        }\r\n        this.isSubscribed = false;\r\n    }\r\n\r\n    subscribeToChatMessages(chatId: string) {\r\n        if (this.isSubscribed) {\r\n            console.warn('Already subscribed to messages for this chat.');\r\n            return;\r\n        }\r\n\r\n        this.currentChatSubscription = this.chatService.getChatMessages(chatId, this.limit).subscribe((messages: Message[]) => {\r\n            const reversedMessages = messages.reverse();\r\n            this.messages.set(reversedMessages);\r\n\r\n            console.log('Last fetched message: ', this.messages());\r\n            this.lastFetchedMessage = reversedMessages[0];\r\n            this.cacheMessages(chatId, reversedMessages, this.lastFetchedMessage);\r\n\r\n            if (this.showInnerChat) {\r\n                this.markMessagesAsRead(chatId);\r\n            }\r\n            this.scrollToBottom();\r\n            this.isSubscribed = true;\r\n        });\r\n    }\r\n\r\n    loadMoreMessages(chatId: string) {\r\n        if (!this.lastFetchedMessage) return;\r\n\r\n        this.chatService.getChatMessages(chatId, this.limit, this.lastFetchedMessage).subscribe((messages: Message[]) => {\r\n            const existingMessages = this.messages();\r\n            const updatedMessages = [...messages.reverse(), ...existingMessages];\r\n            this.messages.set(updatedMessages);\r\n\r\n            if (messages.length > 0) {\r\n                this.lastFetchedMessage = messages[0];\r\n                this.cacheMessages(chatId, updatedMessages, this.lastFetchedMessage);\r\n            }\r\n        });\r\n    }\r\n\r\n    private isCacheValid(cachedData: { messages: Message[], lastFetchedMessage: Message | null; }): boolean {\r\n        return cachedData?.lastFetchedMessage?.id === cachedData.messages[0]?.id;\r\n    }\r\n\r\n    private cacheMessages(chatId: string, messages: Message[], lastFetchedMessage: Message) {\r\n        this.messageCache.set(chatId, { messages, lastFetchedMessage });\r\n    }\r\n\r\n    private markMessagesAsRead(chatId: string) {\r\n        this.chatService.markMessagesAsRead(chatId, this.user.aspUserId);\r\n        this.chatService.markLastMessagesAsRead(chatId, this.user.aspUserId);\r\n    }\r\n\r\n    clearCachedMessages(): void {\r\n        this.messageCache.clear();\r\n    }\r\n\r\n\r\n    addNewMessage(event: Event, inputField: HTMLInputElement): void {\r\n        const textarea = event.target as HTMLTextAreaElement;\r\n        const now = Date.now();\r\n        // Remove timestamps older than 10 seconds\r\n        this.messageTimestamps = this.messageTimestamps.filter(timestamp => now - timestamp < 10000);\r\n\r\n        if (this.messageTimestamps.length > 3) {\r\n\r\n            const oldestTimestamp = Math.min(...this.messageTimestamps);\r\n            const waitTime = 10 - Math.floor((now - oldestTimestamp) / 1000);\r\n\r\n            this.toastService.setShowToastmessage({\r\n                severity: 'warn',\r\n                summary: '',\r\n                detail: `You can only send 3 messages every 10 seconds. Please wait ${waitTime} seconds to send the next message.`,\r\n            });\r\n\r\n            return;\r\n        } else {\r\n            this.messageTimestamps.push(now); // Add current timestamp\r\n        }\r\n\r\n        let val = inputField.value?.trim();\r\n\r\n        if (val.length) {\r\n            // Convert line breaks (\\n) from textarea into <br> tags for HTML rendering\r\n            val = val.replace(/\\n/g, '<br>');\r\n\r\n            // Create a temporary div element to parse the HTML string\r\n            const tempDiv = document.createElement('div');\r\n            tempDiv.innerHTML = val; // Set the HTML input (with <br>) directly into the div\r\n\r\n            if (this.editMessageMode()) {\r\n                this.editMessageObj.content = tempDiv.innerHTML;\r\n                this.chatService.editMessage(this.currentChatId, this.editMessageObj.id, this.editMessageObj, this.user, this.editMessageisLastMessage);\r\n            } else {\r\n\r\n                // Send the sanitized HTML content with line breaks and formatting\r\n                this.sendMessage(tempDiv.innerHTML);\r\n            }\r\n            this.editMessageMode.set(false);\r\n        }\r\n\r\n        inputField.value = '';\r\n\r\n        setTimeout(() => {\r\n            textarea.rows = 1;\r\n            textarea.value = '';\r\n            // textarea.style.height = '24px'; // Reset the height to its original value\r\n            this.adjustParentHeight();\r\n        }, 30);\r\n    }\r\n\r\n    deleteMessage(message: [Message, boolean]) {\r\n        console.log(message);\r\n        if (message[0].isUploadFile) {\r\n            this.chatService.deleteChatFilesFromLibraryFolder(message[0].content).subscribe({\r\n                next: (res) => {\r\n                    console.log(res);\r\n                    this.chatService.deleteMessage(this.currentChatId, message[0], this.user, message[1]);\r\n                },\r\n                error: () => {\r\n                    this.chatService.deleteMessage(this.currentChatId, message[0], this.user, message[1]);\r\n                }\r\n            });\r\n        } else {\r\n            this.chatService.deleteMessage(this.currentChatId, message[0], this.user, message[1]);\r\n        }\r\n\r\n    }\r\n\r\n    editMessage(message: any) {\r\n        this.editMessageMode.set(true);\r\n        console.log(message);\r\n        this.editMessageObj = message[0];\r\n        this.editMessageisLastMessage = message[1];\r\n        if (this.autoResizeTextArea) {\r\n            const formattedContent = this.editMessageObj.content.replace(/<br\\s*\\/?>/gi, '\\n');\r\n            this.autoResizeTextArea.nativeElement.value = formattedContent; // Set the textarea value\r\n            this.adjustParentHeight(); // Adjust the height of the textarea if necessary\r\n        }\r\n    }\r\n\r\n    scrollToBottom(): void {\r\n        try {\r\n            setTimeout(() => {\r\n                if (this.myScrollContainer() && this.myScrollContainer()!.nativeElement) {\r\n                    this.myScrollContainer()!.nativeElement.scrollTop = this.myScrollContainer()!.nativeElement.scrollHeight;\r\n                }\r\n            }, 100);\r\n        } catch (err) { }\r\n    }\r\n\r\n    setUserActive(user: any) {\r\n        this.activeUser = user;\r\n        if (!this.activeUser) {\r\n            return;\r\n        }\r\n        this.showInnerChat = true;\r\n        this.chatService.currentRoom.set(this.activeUser);\r\n        this.loadChatMessages(this.activeUser.classroomId);\r\n        this.chatService.markLastMessagesAsRead(this.currentChatId, this.user.aspUserId);\r\n\r\n\r\n        this.disableEditAndTextarea();\r\n        // this.chatService.getChatMessages(this.activeUser.aspUserId);\r\n    }\r\n\r\n    refreshList(): void {\r\n        this.currentTutorial = undefined;\r\n        this.currentIndex = -1;\r\n        // this.retrieveChats();\r\n    }\r\n\r\n    private getUnreadMessages() {\r\n        // Reset unread count before starting the accumulation process\r\n        this.chatService.unreadCount.set(0);\r\n\r\n        const chatListsWithUnreadCounts = {} as any;\r\n\r\n        this.chatsList().forEach((classroom: any) => {\r\n            this.chatService.getLastUnreadMessagesCountForUser(classroom.classroomId, this.user.aspUserId)\r\n                .subscribe(count => {\r\n                    const chatListWithUnreadCount = {\r\n                        classroom: classroom,\r\n                        unreadCount: count\r\n                    };\r\n\r\n                    chatListsWithUnreadCounts[classroom.classroomId] = chatListWithUnreadCount;\r\n\r\n                    console.log(`Unread messages count for classroom ${classroom.classroomId} for user \r\n                ${this.user.firstName + ' ' + this.user.lastName} with aspUserId ${this.user.aspUserId}: ${count}`);\r\n                    const totalUnreadCount = Object.values(chatListsWithUnreadCounts).reduce((acc, current: any) => acc + current.unreadCount, 0) as number;\r\n                    this.chatService.unreadCount.set(totalUnreadCount);\r\n                    // this.reloadChats.set(true);\r\n                    // this.clearCachedMessages();\r\n                });\r\n\r\n        });\r\n\r\n    }\r\n\r\n    retrieveChats(classrooms: any[]): void {\r\n\r\n        // Wait for all the subscriptions to complete\r\n        this.getUnreadMessages();\r\n\r\n        // Initialize an object to hold the last messages for sorting\r\n        const lastMessages = this.lastMessagesSubject.getValue();\r\n\r\n        classrooms.forEach(classroom => {\r\n            // Retrieve unread message count for the classroom\r\n            // this.chatService.getUnreadMessagesCount(classroom.classroomId, this.user.aspUserId).subscribe(count => {\r\n            //     console.log(`Total getUnreadMessagesCount unread messages count for user ${this.user.aspUserId}: ${count}`);\r\n            // });\r\n            // Retrieve the last message for the classroom itself\r\n            this.chatService.getLastMessage(classroom.id).subscribe({\r\n                next: (message: any) => {\r\n                    lastMessages[classroom.id] = message;\r\n                    this.updateChatsListSorting(lastMessages);\r\n                },\r\n                error: (err) => {\r\n                    console.error(`Failed to retrieve last message for classroom ${classroom.id}`, err);\r\n                }\r\n            });\r\n\r\n            // If the classroom has more than one student, retrieve last messages for individual chats\r\n            classroom.classroomStudents.forEach((student: any) => {\r\n                let chatId = `teacher-${classroom.teacher.id}-student-${student.aspUserId}`;\r\n                if (classroom.chatType === ChatType.CLASSROOM) {\r\n                    chatId = `${classroom.id}`;\r\n                }\r\n                if (classroom.chatType === ChatType.USER) {\r\n                    if (this.authService.isStudent) {\r\n                        chatId = `teacher-${classroom.teacher.id}-student-${this.user.id}`;\r\n                    }\r\n                }\r\n\r\n                console.log(`Retrieving  ${chatId}`);\r\n                this.chatService.getLastMessage(chatId).subscribe({\r\n                    next: (message: any) => {\r\n                        lastMessages[chatId as any] = message;\r\n                        this.updateChatsListSorting(lastMessages);\r\n                    },\r\n                    error: (err) => {\r\n                        console.error(`Failed to retrieve last message for student ${student.id} in classroom ${classroom.id}`, err);\r\n                    }\r\n                });\r\n            });\r\n        });\r\n    }\r\n\r\n    updateChatsListSorting(lastMessages: any): void {\r\n        this.chatsList.set(this.chatsList().sort((a: any, b: any) => {\r\n            const aLastMessage = lastMessages[a.classroomId]?.lastMessageAt?.seconds || 0;\r\n            const bLastMessage = lastMessages[b.classroomId]?.lastMessageAt?.seconds || 0;\r\n            return bLastMessage - aLastMessage;\r\n        }));\r\n\r\n        this.lastMessagesSubject.next(lastMessages);\r\n        this.chatListsLoaded.set(true);\r\n    }\r\n\r\n    private listenChatListsLoaded() {\r\n        toObservable(this.chatListsLoaded, {\r\n            injector: this.injector\r\n        }).subscribe({\r\n            next: (chatListsLoaded) => {\r\n                console.log(chatListsLoaded);\r\n                if (chatListsLoaded) {\r\n                    setTimeout(() => {\r\n                        if (!this.bubbleMode() && !this.isTabletOrMobile) {\r\n                            this.setUserActive(this.filteredChats()[0]);\r\n                        }\r\n                    }, 100);\r\n\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private sortUserListByChats(chats: any) {\r\n\r\n        // Create a map of userId to the latest timestamp of their chats\r\n        const userTimestampMap: { [key: string]: string } = {};\r\n\r\n        chats.forEach((chat: any) => {\r\n            chat.participants.forEach((participant: string) => {\r\n                // Update the map with the latest timestamp for each user\r\n                if (!userTimestampMap[participant] || userTimestampMap[participant] < chat.lastMessageAt) {\r\n                    userTimestampMap[participant] = chat.lastMessageAt;\r\n                }\r\n            });\r\n        });\r\n\r\n        // Sort the chatsList by the latest timestamp of their chats\r\n\r\n    }\r\n\r\n    findAvatarUrl(aspUserId: string): string | null {\r\n        for (let classroom of this.chatsList()) {\r\n            // Check if the teacher matches the aspUserId\r\n            if (classroom.teacher && classroom.teacher.aspUserId === aspUserId) {\r\n                return classroom.teacher;\r\n            }\r\n\r\n            // Check if any student matches the aspUserId\r\n            for (let student of classroom.classroomStudents) {\r\n                if (student.aspUserId === aspUserId) {\r\n                    return student;\r\n                }\r\n            }\r\n        }\r\n        return null; // Return null if no match is found\r\n    }\r\n\r\n\r\n    getUserFullNameAndInitials(aspUserId: string): any | null {\r\n        for (let classroom of this.chatsList()) {\r\n            // Check if the teacher matches the aspUserId\r\n            if (classroom.teacher && classroom.teacher.aspUserId === aspUserId) {\r\n                const fullName = classroom.teacher.firstName + ' ' + classroom.teacher.lastName;\r\n                const initials = classroom.teacher.firstName.charAt(0) + classroom.teacher.lastName.charAt(0);\r\n                return { fullName, initials };\r\n            }\r\n\r\n            // Check if any student matches the aspUserId\r\n            for (let student of classroom.classroomStudents) {\r\n                if (student.aspUserId === aspUserId) {\r\n                    const fullName = student.firstName + ' ' + student.lastName;\r\n                    const initials = student.firstName.charAt(0) + student.lastName.charAt(0);\r\n                    return { fullName, initials };\r\n                }\r\n            }\r\n        }\r\n        return null; // Return null if no match is found\r\n    }\r\n\r\n    sendMessage(content: string, isFileFromLibrary: boolean = false, isUploadFile: boolean = false) {\r\n        const message: Message = {\r\n            senderId: this.user.aspUserId,\r\n            content: content,\r\n            timestamp: serverTimestamp(),\r\n            readBy: [this.user.aspUserId],\r\n            isFileFromLibrary: isFileFromLibrary,\r\n            isUploadFile: isUploadFile,\r\n            readStatus: { [this.user.aspUserId]: true }\r\n\r\n        };\r\n\r\n        console.log(this.user);\r\n        this.chatService.addMessage(this.currentChatId, message, this.user);\r\n        this.chatService.markMessagesAsRead(this.currentChatId, this.user.aspUserId);\r\n    }\r\n\r\n    isImage(filePath: string): boolean {\r\n        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.ico', '.jfif', '.avif'];\r\n        const fileExtension = filePath.split('.').pop()?.toLowerCase();\r\n        return imageExtensions.includes(`.${fileExtension}`);\r\n    }\r\n\r\n    goToChat() {\r\n        this.showInnerChat = true;\r\n    }\r\n\r\n    openFileUploadDialog() {\r\n\r\n        const divToShowDialog = this.bubbleMode() ? 'mainContainerSideMenuBelow' : 'containerLayout';\r\n        this.libraryService.isPreviewingFromChat.set(true);\r\n        this.userService.setNewFileUpload({});\r\n        setTimeout(() => {\r\n            const myElement = document.getElementById(divToShowDialog);\r\n            const dialogWidth = myElement!.getBoundingClientRect().width - 10;\r\n            this.generalService.openDialogWithComponent(this.dialogService, ChatUploadFileDialogComponent, dialogWidth.toFixed(),\r\n                {\r\n                    action: 'upload',\r\n                    dialogsubTitle: '',\r\n                    dialogTitle: 'Upload to library...',\r\n                }, divToShowDialog, (result: any) => {\r\n                    console.log(result);\r\n                    this.chatService.showUploadFile.set(false);\r\n                    this.libraryService.checkedLibraryFiles.set([]);\r\n                    this.libraryService.isPreviewingFromChat.set(false);\r\n                    // this.dialogService.getInstance(result).close();\r\n                    // this.generalService.destroyComponent(ChatUploadFileDialogComponent);;\r\n                    if (result && Array.isArray(result)) {\r\n                        result.forEach((item: any) => {\r\n                            this.sendMessage(item.path, true, true);\r\n                        });\r\n                        setTimeout(() => {\r\n                            this.scrollToBottom();\r\n                        }, 1000);\r\n                        console.log(result);\r\n                        // this.moveFile(result.map.map);\r\n                    }\r\n                }, myElement!.getBoundingClientRect().height - 290 + 'px');\r\n        }, 100);\r\n\r\n    }\r\n\r\n\r\n    onPaste(event: any) {\r\n        const items = (event.clipboardData || event.originalEvent.clipboardData).items;\r\n        let blob = null;\r\n        for (const item of items) {\r\n            if (item.type.indexOf('image') === 0) {\r\n                blob = item.getAsFile();\r\n                this.onBasicUploadAuto(blob, true);\r\n                console.log(blob); // Prints your files\r\n            }\r\n        }\r\n    }\r\n\r\n    recognizeImage(imageData: string | ArrayBuffer | null) {\r\n        // Simulate an API call\r\n        this.imageInfo = 'Recognized Text: \"Example text from image\"'; // Mock recognition result\r\n\r\n        // Example of a real API call\r\n        /*\r\n        this.http.post('YOUR_API_ENDPOINT', { image: imageData }).subscribe(response => {\r\n          this.imageInfo = response.recognizedText; // Update with actual response\r\n        });\r\n        */\r\n    }\r\n\r\n    loadOnlineStatus() {\r\n        this.chatService.setUserStatus(this.user.aspUserId, 'online');\r\n        this.initUserOnlineInterval();\r\n        this.setUserLastOnlineTime();\r\n        // Subscribe to online users\r\n        this.subs.add(this.chatService.getOnlineUsersComparedToLastOnlineTime().subscribe(users => {\r\n            this.onlineUsers = users;\r\n        }));\r\n    }\r\n\r\n    setUserLastOnlineTime() {\r\n        this.chatService.setUserLastOnlineTime(this.user.aspUserId, serverTimestamp());\r\n    }\r\n\r\n    initUserOnlineInterval() {\r\n        this.intervalId = setInterval(() => {\r\n            this.setUserLastOnlineTime();\r\n        }, this.chatService.secondsToCheckOnlineStatus * 1000);\r\n    }\r\n\r\n    isUserOnline(userId: string): boolean {\r\n        const currentTime = Timestamp.now();\r\n        return this.onlineUsers.some(user => {\r\n            if (user.id === userId) {\r\n                const lastOnlineTime = user.lastOnlineTime as Timestamp;\r\n                const timeDifference = currentTime.seconds - lastOnlineTime.seconds;\r\n                return timeDifference <= this.chatService.secondsToCheckOnlineStatus;\r\n            }\r\n            return false;\r\n        });\r\n    }\r\n\r\n    loadMobileLayoutCheck() {\r\n        this.subs.sink = this.generalService.deviceKind.pipe(\r\n            switchMap(res => {\r\n                this.isTabletOrMobile = !res.w992up;\r\n                if (!res.w1024up) {\r\n                    return of(0);\r\n                } else {\r\n                    return of(null);\r\n                }\r\n            })\r\n        ).subscribe((res) => {\r\n            if (res === 0) {\r\n            } else if (res !== 0) {\r\n                const menuHeight = res;\r\n            }\r\n        });\r\n    }\r\n\r\n    showLeftSideMobile() {\r\n        if (this.isTabletOrMobile && this.showInnerChat) {\r\n            return false;\r\n        } else {\r\n            return true;\r\n        }\r\n    }\r\n\r\n    showRightSideMobile() {\r\n        if (this.isTabletOrMobile && !this.showInnerChat) {\r\n            return false;\r\n        } else {\r\n            return true;\r\n        }\r\n    }\r\n\r\n    showBackUsers() {\r\n        this.showInnerChat = false;\r\n    }\r\n\r\n    autoResize(textArea: HTMLTextAreaElement): void {\r\n        textArea.style.height = 'auto';  // Reset height to auto to shrink when deleting text\r\n        textArea.style.height = textArea.scrollHeight + 'px';  // Set height to the scrollHeight of the content\r\n    }\r\n\r\n    disableEditAndTextarea() {\r\n        this.editMessageMode.set(false);\r\n    }\r\n\r\n    resetTextarea() {\r\n        this.autoResizeTextArea.nativeElement.value = '';\r\n        // this.adjustHeight(this.autoResizeTextArea.nativeElement);\r\n        this.adjustParentHeight();\r\n    }\r\n\r\n    countLastMessageCount(lastMessage: any) {\r\n        if (!lastMessage) {\r\n            return;\r\n        }\r\n        if (lastMessage.unreadCounts) {\r\n            return lastMessage.unreadCounts[this.user.aspUserId];\r\n        } else {\r\n            return 0;\r\n        }\r\n    }\r\n}\r\n", "@defer(on viewport) {\r\n\r\n@if (!bubbleMode()) {\r\n\r\n\r\n<div id=\"notifications-overview\" class=\"notifications-overview\">\r\n  <div class=\"surface-section border-round-3xl\">\r\n    <div class=\"block-header relative justify-content-center  lg:flex\">\r\n\r\n      <div class=\"absolute pl-1 md:pl-2 left-0\">\r\n\r\n        <!-- <button routerLink=\"/dashboard\" pButton pRipple type=\"button\" label=\"Back to Dashboard\" icon=\"pi pi-angle-left\" iconPos=\"left\"\r\n                             class=\"p-button-outlined p-button-sm p-button-rounded text-white\" styleClass=\"\"></button> -->\r\n      </div>\r\n      <span class=\"block-title\">\r\n        <span class=\"text-0\">Chat</span>\r\n      </span>\r\n    </div>\r\n\r\n\r\n    <div class=\"chat-wrapper\">\r\n      <div class=\"chat flex-column sm:flex-row\">\r\n\r\n        <div class=\"left\" *ngIf=\"showLeftSideMobile()\"\r\n          [ngClass]=\"{ 'col-12': isTabletOrMobile, 'col-4': !isTabletOrMobile }\">\r\n\r\n          <div class=\"persona-container\">\r\n\r\n\r\n            <ng-container *ngTemplateOutlet=\"personSearchTemplate\"></ng-container>\r\n\r\n            <ng-container *ngTemplateOutlet=\"personBoxTemplate\"></ng-container>\r\n\r\n\r\n          </div>\r\n        </div>\r\n        <!-- ends left -->\r\n\r\n        <div class=\"right sm:col-8\" *ngIf=\"showRightSideMobile()\">\r\n          <ng-container *ngTemplateOutlet=\"chatPersonTemplate\"></ng-container>\r\n          <div class=\"chat-messages mt-1\" #scrollMe [ngClass]=\"{ 'pointer-events-none': editMessageMode() }\">\r\n\r\n            <button (click)=\"loadMoreMessages(currentChatId)\">Load previous messages</button>\r\n            @if (messages().length === 0) {\r\n            <ng-container *ngTemplateOutlet=\"emptyMessagesTemplate\"></ng-container>\r\n            }\r\n            <app-chat-messages-display [messages]=\"messages()\" [chatsList]=\"chatsList()\"\r\n              (deleteMessageSelected)=\"deleteMessage($event)\"\r\n              (editMessageSelected)=\"editMessage($event)\"></app-chat-messages-display>\r\n\r\n          </div>\r\n\r\n          <ng-container *ngTemplateOutlet=\"inputTextTemplate\"></ng-container>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n}\r\n}@placeholder {\r\n<span>loading...</span>\r\n}\r\n\r\n\r\n\r\n@if (bubbleMode()) {\r\n\r\n<div id=\"chat-circle\" [@scaleToggle]=\"chatCircleState\" (click)=\"toggleChat()\" class=\"btn btn-raised\" [pTooltip]=\"'Chat'\"\r\n  tooltipPosition=\"top\">\r\n  <i class=\"pi pi-comments\"></i>\r\n  <p-badge [value]=\"this.chatService.unreadCount()\" badgeSize=\"small\" severity=\"success\"\r\n    styleClass=\"absolute notif-badge\" />\r\n\r\n</div>\r\n\r\n<div class=\"chat-box overflow-hidden border-round-lg\" *ngIf=\"chatBoxVisible\" [@scaleToggle]=\"chatBoxState\">\r\n  <div class=\"chat-box-header flex align-items-center justify-content-between\">\r\n    <span class=\"text-lg\">Chat</span>\r\n\r\n\r\n    <div class=\"flex align-items-center justify-content-center gap-3\">\r\n      <a class=\"flex align-items-center\" title=\"Open Chat in new tab\" [href]=\"'/#/chat'\" target=\"_blank\">\r\n        <i class=\"pi pi-window-maximize\" (click)=\"toggleChat()\"></i>\r\n      </a>\r\n      <span class=\"chat-box-toggle flex align-items-center\" (click)=\"toggleChat()\"><i class=\"pi pi-times\"></i></span>\r\n    </div>\r\n  </div>\r\n  <div class=\"chat-box-body\">\r\n    <div class=\"main-screen\" [ngClass]=\"{ 'editMode': editMessageMode(), 'showInnerChat': showInnerChat }\">\r\n      <div id=\"homeScreen\" class=\"home-screen\" *ngIf=\"!showInnerChat\">\r\n\r\n        <div id=\"homeScreenMessagesBody\" class=\"home-screen-messages bg-grad-white-style\">\r\n\r\n          <div class=\"flex flex-column h-full px-2\">\r\n            <!-- <div class=\"flex align-items-center justify-content-between p-3\">\r\n              <div>\r\n                <div class=\"text-900 text-xl font-medium mb-1\">Conversations</div>\r\n                <p class=\"text-600 m-0\">{{chatsList().length}} Groups</p>\r\n              </div>\r\n            </div> -->\r\n\r\n            <div class=\"mt-1\">\r\n              <ng-container *ngTemplateOutlet=\"personSearchTemplate\"></ng-container>\r\n            </div>\r\n            <!-- <hr class=\"m-0 mb-1 border-bottom-1 border-200\"> -->\r\n            <div class=\"flex-auto overflow-y-auto pr-1 pt-2 px-2\">\r\n\r\n\r\n              @if (!showInnerChat) {\r\n              <ng-container *ngTemplateOutlet=\"personBoxTemplate\"></ng-container>\r\n              }\r\n\r\n            </div>\r\n          </div>\r\n\r\n\r\n\r\n        </div>\r\n      </div>\r\n      <div id=\"chatScreen\" class=\"chat-screen\" *ngIf=\"showInnerChat\">\r\n        <div class=\"chat-screen-header\">\r\n          <div class=\"chat-screen-header-left\">\r\n            <span id=\"chatBackbutton\" class=\"back-button\">\r\n            </span>\r\n\r\n          </div>\r\n          <span class=\"search-button\">\r\n            <i class=\"fas fa-ellipsis-h\"></i>\r\n          </span>\r\n        </div>\r\n\r\n\r\n        <div class=\"right\">\r\n\r\n          <ng-container *ngTemplateOutlet=\"chatPersonTemplate\"></ng-container>\r\n\r\n          <div class=\"chat-messages\" [ngClass]=\"{ 'pointer-events-none': editMessageMode() }\" #scrollMe>\r\n            @if (messages().length === 0) {\r\n            <ng-container *ngTemplateOutlet=\"emptyMessagesTemplate\"></ng-container>\r\n            }\r\n            <app-chat-messages-display [messages]=\"messages()\" [chatsList]=\"chatsList()\" [isBubbleMode]=\"bubbleMode()\"\r\n              (editMessageSelected)=\"editMessage($event)\"\r\n              (deleteMessageSelected)=\"deleteMessage($event)\"></app-chat-messages-display>\r\n\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n      <div id=\"searchScreen\" class=\"search-screen\">\r\n        <div class=\"search-screen-header\">\r\n          <span id=\"chat-back-button\" class=\"back-button\">\r\n            <i class=\"fas fa-long-arrow-alt-left\"></i>\r\n          </span>\r\n          <input id=\"searchInput\" type=\"text\" placeholder=\"Search\" class=\"search-input\">\r\n        </div>\r\n        <div id=\"searchScreenBody\" class=\"search-screen-body\">\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"chat-input\" *ngIf=\"showInnerChat\">\r\n    <div class=\"chat-screen-footer\">\r\n      <div class=\"input\">\r\n\r\n        <ng-container *ngTemplateOutlet=\"inputTextTemplate\"></ng-container>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n}\r\n\r\n\r\n<ng-template #chatPersonTemplate>\r\n  <div class=\"chat-person\">\r\n    <div class=\"inner border-bottom-1 border-200 relative p-2 sm:p-2 w-full\">\r\n      <!-- <ng-container *ngTemplateOutlet=\"chatTypeBadgeTemplate; context: { chatGroup: activeUser }\"></ng-container> -->\r\n      <div class=\"flex surface-border align-items-center\">\r\n        <p-button *ngIf=\"isTabletOrMobile || bubbleMode()\" icon=\"pi pi-chevron-left\" styleClass=\"text-2xl\"\r\n          (click)=\"showBackUsers()\" severity=\"info\" />\r\n        <!-- <i class=\"pi pi-chevron-left text-3xl\" (click)=\"showBackUsers()\"></i> -->\r\n        <div class=\"relative mr-2 flex align-items-center\">\r\n          <img [src]=\"generalService.getDomainFileNamePath(getChatGroupAvatarUrl(activeUser))\"\r\n            (error)=\"generalService.setDefaultUserAvatar($event)\" class=\"border-circle\"\r\n            style=\"width: 46px; height: 46px;\">\r\n          @if (isUserOnline(getChatGroupAspUserId(activeUser))) {\r\n          <span id=\"pn_id_26_badge\" class=\" online__badge\"></span>\r\n          }\r\n        </div>\r\n        <div class=\"flex flex-column align-items-start\">\r\n          <span class=\"text-base sm:text-xl text-900 font-medium\">{{activeUser?.chatName}}</span>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #inputTextTemplate>\r\n  @if (editMessageMode()) {\r\n  <div\r\n    class=\"w-full border-round-3xl bg-yellow-200 py-2 px-3 text-yellow-900 text-sm flex align-items-center justify-content-between px-2 py-1 font-medium mb-0\">\r\n    <div class=\"flex align-items-center gap-1\"><i class=\"pi pi-pencil\"></i><span>Edit Message</span></div>\r\n\r\n    <i class=\"pi pi-times ml-2 cursor-pointer\" (click)=\"disableEditAndTextarea(); resetTextarea();\"></i>\r\n  </div>\r\n  }\r\n\r\n  <div #textareaWrapper class=\"chat-input box \" [ngClass]=\"{ 'border-round-3xl': !bubbleMode() }\">\r\n\r\n    <div style=\"position: relative;\" class=\"speeddial-tooltip-demo\"\r\n      [ngClass]=\"{ 'pointer-events-none': editMessageMode(), 'hidden': editMessageMode() }\">\r\n\r\n      <div class=\"fab-container\">\r\n        <div class=\"fab fab-icon-holder\">\r\n          <ng-container *ngIf=\"uploadProgress > 0; else notUploading\">\r\n            <i class=\"pi pi-spin pi-spinner\"></i>\r\n          </ng-container>\r\n          <ng-template #notUploading>\r\n            <i class=\"pi pi-plus\"></i>\r\n          </ng-template>\r\n        </div>\r\n        <ul class=\"fab-options\">\r\n          <li>\r\n            <!-- <span class=\"fab-label\">File:</span> -->\r\n            <div class=\"fab-line fab-icon-holder\" (click)=\"openFileUploadDialog()\">\r\n              <img src=\"/assets/icons/folder-icon.svg\" class=\"send-img\" alt=\"\">\r\n            </div>\r\n          </li>\r\n          <li>\r\n            <!-- <span class=\"fab-label\">Photo</span> -->\r\n            <div class=\"fab-tel fab-icon-holder\">\r\n              <ng-container *ngIf=\"uploadProgress > 0; else notUpload\">\r\n                <i class=\"pi pi-spin pi-spinner\"></i>\r\n              </ng-container>\r\n              <ng-template #notUpload>\r\n                <p-fileUpload mode=\"basic\" accept=\"image/*\" maxFileSize=\"4000000\" (onSelect)=\"onBasicUploadAuto($event)\"\r\n                  [auto]=\"true\" styleClass=\"p-button-rounded p-button-info purple-icon text-white\">\r\n\r\n                  <ng-template pTemplate=\"chooseicon\">\r\n                    <img src=\"/assets/icons/camera.svg\" class=\"send-img\" alt=\"\">\r\n                  </ng-template>\r\n\r\n\r\n                </p-fileUpload>\r\n              </ng-template>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n\r\n      <!-- <div class=\"flact__cont\">\r\n      <div class=\"flact\">\r\n        <button class=\"flact_option\" data-icon=\"text\">Text</button>\r\n        <button class=\"flact_option\" data-icon=\"link\">Link</button>\r\n        <button class=\"flact_button\" data-icon=\"add\">Submit</button>\r\n      </div>\r\n    </div> -->\r\n\r\n      <!-- <p-speedDial buttonClassName=\"p-button-rounded p-button-info speed__btn\" [model]=\"leftTooltipItems\"\r\n      className=\"speeddial-right\" direction=\"up\" /> -->\r\n    </div>\r\n\r\n\r\n    <!-- <i id=\"emojiBtn\" class=\"pi pi-paperclip purple-icon\" (click)=\"openFileUploadDialog()\"></i> -->\r\n\r\n\r\n\r\n    <textarea #autoResizeTextArea rows=\"1\" (input)=\"adjustParentHeight()\" (paste)=\"onPaste($event)\" type=\"text\"\r\n      pInputText class=\"p-1 w-full mx-2\" placeholder=\"Type a message\"\r\n      (focus)=\"onFocusTextarea()\"\r\n      (click)=\"autoResizeTextArea.focus(); adjustParentHeight()\"\r\n      (keydown.enter)=\"addNewMessage($event, autoResizeTextArea)\"></textarea>\r\n    <div class=\"chat-send-btn send\" (click)=\"addNewMessage($event, autoResizeTextArea)\">\r\n\r\n      <img src=\"/assets/icons/paper-plane.svg\" class=\"send-img\" alt=\"\">\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n\r\n<ng-template #personSearchTemplate>\r\n  <div class=\"search\">\r\n    <span class=\"p-input-icon-right w-full mb-1\">\r\n      <input type=\"text\" pInputText placeholder=\"Search\" (input)=\"onSearchChange($event)\"\r\n        class=\"h-2rem w-full input-blue gradient-blue rounded\" />\r\n      <i class=\"pi pi-search text-primary\"></i>\r\n    </span>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #personBoxTemplate>\r\n  <div class=\"persona box border-1 border-200\" *ngFor=\"let chatGroup of filteredChats(); let i = index\"\r\n    (click)=\"setUserActive(chatGroup)\"\r\n    [ngClass]=\"{ 'selected': chatGroup.classroomId === currentChatId && !bubbleMode() }\">\r\n    <div class=\"relative mr-1 flex align-items-center\">\r\n      <img [src]=\"generalService.getDomainFileNamePath(getChatGroupAvatarUrl(chatGroup))\"\r\n        (error)=\"generalService.setDefaultUserAvatar($event)\" class=\"border-circle\" style=\"width: 40px; height: 40px;\">\r\n      @if (isUserOnline(getChatGroupAspUserId(chatGroup))) {\r\n      <span class=\" online__badge\"></span>\r\n      }\r\n    </div>\r\n    @if (countLastMessageCount((lastMessages$ | async)?.[chatGroup.classroomId]) > 0) {\r\n    <div class=\"new-message-count\">{{countLastMessageCount((lastMessages$ | async)?.[chatGroup.classroomId])}}</div>\r\n    }\r\n    <!-- <img src=\"https://content-static.upwork.com/uploads/2014/10/02123010/profilephoto_goodcrop.jpg\" /> -->\r\n    <div class=\"info-section\">\r\n\r\n\r\n      <ng-container *ngTemplateOutlet=\"chatTypeBadgeTemplate; context: { chatGroup: chatGroup }\"></ng-container>\r\n      <span class=\"info-name\">{{chatGroup.chatName}}</span>\r\n      <span class=\"info-message\">\r\n        <ng-container *ngIf=\"(lastMessages$ | async)?.[chatGroup.classroomId]?.lastMessageIsFile; else notFileChat\">\r\n          @if((lastMessages$ | async)?.[chatGroup.classroomId]?.lastMessageDeleted) {\r\n          <i class=\"pi pi-trash\"></i> File deleted\r\n          } @else {\r\n          <i class=\"pi pi-file\"></i> File\r\n          }\r\n        </ng-container>\r\n\r\n        <ng-template #notFileChat>\r\n          @if((lastMessages$ | async)?.[chatGroup.classroomId]?.lastMessageDeleted) {\r\n          <i class=\"pi pi-trash\"></i> Message deleted\r\n          } @else {\r\n          <span [innerHtml]=\"(lastMessages$ | async)?.[chatGroup.classroomId]?.lastMessage\"></span>\r\n          }\r\n        </ng-template>\r\n      </span>\r\n    </div>\r\n    <div class=\"detail-section\">\r\n      <span *ngIf=\"(lastMessages$ | async)?.[chatGroup.classroomId]?.lastMessage\" class=\"detail-date\">{{(lastMessages$\r\n        | async)?.[chatGroup.classroomId]?.lastMessageAt?.seconds * 1000 |\r\n        date:\r\n        'short'}}</span>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #emptyMessagesTemplate>\r\n  <ul class=\"px-1 m-0 list-none\">\r\n    <li class=\"flex align-items-center py-2\">\r\n      <div class=\"fab w-3rem h-3rem flex align-items-center justify-content-center bg-indigo-300\t\r\n         border-circle mr-3 flex-shrink-0\">\r\n        <i class=\"pi pi-comments text-xl text-white\"></i>\r\n      </div><span class=\"text-900 line-height-3 font-medium\"> <span class=\"text-700 font-normal\">\r\n          No messages yet. Feel free to start the conversation with\r\n          {{activeUser?.relatedName}}\r\n          <!-- {{getChatOppositeName(activeUser)}} -->\r\n        </span></span>\r\n    </li>\r\n  </ul>\r\n</ng-template>\r\n\r\n<ng-template #chatTypeBadgeTemplate let-chatGroup=\"chatGroup\">\r\n  @if (chatGroup?.chatType === chatType.CLASSROOM && chatGroup?.classroomStudents.length > 1) {\r\n  <span class=\"group__badge p-badge p-component p-badge-info\">Group</span>\r\n  } @else if (chatGroup?.chatType === chatType.USER) {\r\n  <span class=\"group__badge p-badge p-component p-badge-classroom\">Private</span>\r\n  } @else {\r\n  <span class=\"group__badge p-badge p-component p-badge-classroom\">Private</span>\r\n  }\r\n</ng-template>"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAA+EC,QAAQ,EAA8CC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAGtM,SAASC,eAAe,EAA+CC,EAAE,EAAEC,SAAS,QAAc,MAAM;AAGxG,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,WAAW,QAAiC,oCAAoC;AACzF,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,OAAO,QAAQ,SAAS;AACjC,SAA6BC,eAAe,EAAcC,SAAS,QAAQ,oBAAoB;AAE/F,SAASC,OAAO,EAAgBC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,uCAAuC;AAEtE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,aAAa,QAA0B,uBAAuB;AACvE,SAASC,6BAA6B,QAAQ,wEAAwE;AACtH,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,gBAAgB,QAAqB,oBAAoB;AAClE,SAASC,WAAW,EAAEC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAE7E,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,4BAA4B,QAAQ,oEAAoE;AACjH,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,eAAe,QAAQ,mBAAmB;AAEnD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;;;;;;;;;;;;;sFAgCvCN,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;ICvCxBO,EAAA,CAAAC,kBAAA,GAAsE;;;;;IAEtED,EAAA,CAAAC,kBAAA,GAAmE;;;;;IALrED,EAHF,CAAAE,cAAA,cACyE,cAExC;IAK7BF,EAFA,CAAAG,UAAA,IAAAC,iEAAA,2BAAuD,IAAAC,iEAAA,2BAEH;IAIxDL,EADE,CAAAM,YAAA,EAAM,EACF;;;;;;IAXJN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,gBAAA,GAAAD,MAAA,CAAAC,gBAAA,EAAsE;IAKrDX,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAAO,UAAA,qBAAAM,uBAAA,CAAsC;IAEtCb,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAAO,UAAA,qBAAAO,oBAAA,CAAmC;;;;;IAQpDd,EAAA,CAAAC,kBAAA,GAAoE;;;;;IAKlED,EAAA,CAAAC,kBAAA,GAAuE;;;;;IAAvED,EAAA,CAAAG,UAAA,IAAAY,gFAAA,2BAAwD;;;;;IAAzCf,EAAA,CAAAO,UAAA,qBAAAS,wBAAA,CAAuC;;;;;IAQxDhB,EAAA,CAAAC,kBAAA,GAAmE;;;;;;IAdrED,EAAA,CAAAE,cAAA,cAA0D;IACxDF,EAAA,CAAAG,UAAA,IAAAc,kEAAA,2BAAqD;IAGnDjB,EAFF,CAAAE,cAAA,iBAAmG,iBAE/C;IAA1CF,EAAA,CAAAkB,UAAA,mBAAAC,4EAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAc,gBAAA,CAAAd,MAAA,CAAAe,aAAA,CAA+B;IAAA,EAAC;IAACzB,EAAA,CAAA0B,MAAA,6BAAsB;IAAA1B,EAAA,CAAAM,YAAA,EAAS;IACjFN,EAAA,CAAAG,UAAA,IAAAwB,iEAAA,uBAA+B;IAG/B3B,EAAA,CAAAE,cAAA,oCAE8C;IAA5CF,EADA,CAAAkB,UAAA,mCAAAU,+GAAAC,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAyBb,MAAA,CAAAoB,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC,iCAAAE,6GAAAF,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CACxBb,MAAA,CAAAsB,WAAA,CAAAH,MAAA,CAAmB;IAAA,EAAC;IAE/C7B,EAFgD,CAAAM,YAAA,EAA4B,EAEtE;IAENN,EAAA,CAAAG,UAAA,IAAA8B,kEAAA,2BAAoD;IAEtDjC,EAAA,CAAAM,YAAA,EAAM;;;;;;IAfWN,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAAO,UAAA,qBAAA2B,qBAAA,CAAoC;IACTlC,EAAA,CAAAY,SAAA,EAAwD;IAAxDZ,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAmC,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAA2B,eAAA,IAAwD;IAGhGrC,EAAA,CAAAY,SAAA,GAEC;IAFDZ,EAAA,CAAAsC,aAAA,IAAA5B,MAAA,CAAA6B,QAAA,GAAAC,MAAA,gBAEC;IAC0BxC,EAAA,CAAAY,SAAA,EAAuB;IAACZ,EAAxB,CAAAO,UAAA,aAAAG,MAAA,CAAA6B,QAAA,GAAuB,cAAA7B,MAAA,CAAA+B,SAAA,GAA0B;IAM/DzC,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAAO,UAAA,qBAAAmC,oBAAA,CAAmC;;;;;IA7CxD1C,EAFJ,CAAAE,cAAA,cAAgE,cAChB,cACuB;IAEjEF,EAAA,CAAA2C,SAAA,cAIM;IAEJ3C,EADF,CAAAE,cAAA,eAA0B,eACH;IAAAF,EAAA,CAAA0B,MAAA,WAAI;IAE7B1B,EAF6B,CAAAM,YAAA,EAAO,EAC3B,EACH;IAIJN,EADF,CAAAE,cAAA,cAA0B,cACkB;IAiBxCF,EAfA,CAAAG,UAAA,IAAAyC,kDAAA,kBACyE,KAAAC,mDAAA,kBAcf;IAsBlE7C,EALM,CAAAM,YAAA,EAAM,EACF,EAGF,EACF;;;;IArCqBN,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAO,UAAA,SAAAG,MAAA,CAAAoC,kBAAA,GAA0B;IAehB9C,EAAA,CAAAY,SAAA,EAA2B;IAA3BZ,EAAA,CAAAO,UAAA,SAAAG,MAAA,CAAAqC,mBAAA,GAA2B;;;;;IApChE/C,EAAA,CAAAG,UAAA,IAAA6C,4CAAA,mBAAqB;;;;IAArBhD,EAAA,CAAAsC,aAAA,KAAA5B,MAAA,CAAAuC,UAAA,YA8DC;;;;;IAEDjD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAA0B,MAAA,iBAAU;IAAA1B,EAAA,CAAAM,YAAA,EAAO;;;;;IA0CTN,EAAA,CAAAC,kBAAA,GAAsE;;;;;IAOtED,EAAA,CAAAC,kBAAA,GAAmE;;;;;IAAnED,EAAA,CAAAG,UAAA,IAAA+C,8EAAA,2BAAoD;;;;;IAArClD,EAAA,CAAAO,UAAA,qBAAAO,oBAAA,CAAmC;;;;;IARpDd,EAZN,CAAAE,cAAA,cAAgE,cAEoB,cAEtC,cAQtB;IAChBF,EAAA,CAAAG,UAAA,IAAAgD,gEAAA,2BAAuD;IACzDnD,EAAA,CAAAM,YAAA,EAAM;IAENN,EAAA,CAAAE,cAAA,cAAsD;IAGpDF,EAAA,CAAAG,UAAA,IAAAiD,+DAAA,uBAAsB;IAU9BpD,EANM,CAAAM,YAAA,EAAM,EACF,EAIF,EACF;;;;;IAhBiBN,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAAO,UAAA,qBAAAM,uBAAA,CAAsC;IAMrDb,EAAA,CAAAY,SAAA,GAEC;IAFDZ,EAAA,CAAAsC,aAAA,KAAA5B,MAAA,CAAA2C,aAAA,UAEC;;;;;IAwBLrD,EAAA,CAAAC,kBAAA,GAAoE;;;;;IAIlED,EAAA,CAAAC,kBAAA,GAAuE;;;;;IAAvED,EAAA,CAAAG,UAAA,IAAAmD,+EAAA,2BAAwD;;;;;IAAzCtD,EAAA,CAAAO,UAAA,qBAAAS,wBAAA,CAAuC;;;;;;IAjBxDhB,EAFJ,CAAAE,cAAA,cAA+D,cAC7B,cACO;IACnCF,EAAA,CAAA2C,SAAA,eACO;IAET3C,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAE,cAAA,eAA4B;IAC1BF,EAAA,CAAA2C,SAAA,YAAiC;IAErC3C,EADE,CAAAM,YAAA,EAAO,EACH;IAGNN,EAAA,CAAAE,cAAA,cAAmB;IAEjBF,EAAA,CAAAG,UAAA,IAAAoD,gEAAA,2BAAqD;IAErDvD,EAAA,CAAAE,cAAA,iBAA8F;IAC5FF,EAAA,CAAAG,UAAA,KAAAqD,gEAAA,uBAA+B;IAG/BxD,EAAA,CAAAE,cAAA,qCAEkD;IAAhDF,EADA,CAAAkB,UAAA,iCAAAuC,4GAAA5B,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAsC,IAAA;MAAA,MAAAhD,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAuBb,MAAA,CAAAsB,WAAA,CAAAH,MAAA,CAAmB;IAAA,EAAC,mCAAA8B,8GAAA9B,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAsC,IAAA;MAAA,MAAAhD,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAClBb,MAAA,CAAAoB,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC;IAKvD7B,EALwD,CAAAM,YAAA,EAA4B,EAE1E,EACF,EAEF;;;;;IAbaN,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAO,UAAA,qBAAA2B,qBAAA,CAAoC;IAExBlC,EAAA,CAAAY,SAAA,EAAwD;IAAxDZ,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAmC,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAA2B,eAAA,IAAwD;IACjFrC,EAAA,CAAAY,SAAA,GAEC;IAFDZ,EAAA,CAAAsC,aAAA,KAAA5B,MAAA,CAAA6B,QAAA,GAAAC,MAAA,iBAEC;IAC0BxC,EAAA,CAAAY,SAAA,EAAuB;IAA2BZ,EAAlD,CAAAO,UAAA,aAAAG,MAAA,CAAA6B,QAAA,GAAuB,cAAA7B,MAAA,CAAA+B,SAAA,GAA0B,iBAAA/B,MAAA,CAAAuC,UAAA,GAA8B;;;;;IAyB9GjD,EAAA,CAAAC,kBAAA,GAAmE;;;;;IAFrED,EAFJ,CAAAE,cAAA,cAA8C,cACZ,cACX;IAEjBF,EAAA,CAAAG,UAAA,IAAAyD,gEAAA,2BAAoD;IAG1D5D,EAFI,CAAAM,YAAA,EAAM,EACF,EACF;;;;;IAHeN,EAAA,CAAAY,SAAA,GAAmC;IAAnCZ,EAAA,CAAAO,UAAA,qBAAAmC,oBAAA,CAAmC;;;;;;IAxFtD1C,EAFJ,CAAAE,cAAA,cAA2G,cAC5B,eACrD;IAAAF,EAAA,CAAA0B,MAAA,WAAI;IAAA1B,EAAA,CAAAM,YAAA,EAAO;IAK7BN,EAFJ,CAAAE,cAAA,cAAkE,YACmC,YACzC;IAAvBF,EAAA,CAAAkB,UAAA,mBAAA2C,8DAAA;MAAA7D,EAAA,CAAAoB,aAAA,CAAA0C,GAAA;MAAA,MAAApD,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAqD,UAAA,EAAY;IAAA,EAAC;IACzD/D,EAD0D,CAAAM,YAAA,EAAI,EAC1D;IACJN,EAAA,CAAAE,cAAA,eAA6E;IAAvBF,EAAA,CAAAkB,UAAA,mBAAA8C,iEAAA;MAAAhE,EAAA,CAAAoB,aAAA,CAAA0C,GAAA;MAAA,MAAApD,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAqD,UAAA,EAAY;IAAA,EAAC;IAAC/D,EAAA,CAAA2C,SAAA,YAA2B;IAE5G3C,EAF4G,CAAAM,YAAA,EAAO,EAC3G,EACF;IAEJN,EADF,CAAAE,cAAA,cAA2B,eAC8E;IA+BrGF,EA9BA,CAAAG,UAAA,KAAA8D,iDAAA,kBAAgE,KAAAC,iDAAA,mBA8BD;IA+B3DlE,EAFJ,CAAAE,cAAA,eAA6C,eACT,gBACgB;IAC9CF,EAAA,CAAA2C,SAAA,aAA0C;IAC5C3C,EAAA,CAAAM,YAAA,EAAO;IACPN,EAAA,CAAA2C,SAAA,iBAA8E;IAChF3C,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAA2C,SAAA,eAEM;IAGZ3C,EAFI,CAAAM,YAAA,EAAM,EACF,EACF;IACNN,EAAA,CAAAG,UAAA,KAAAgE,iDAAA,kBAA8C;IAQhDnE,EAAA,CAAAM,YAAA,EAAM;;;;IA9FuEN,EAAA,CAAAO,UAAA,iBAAAG,MAAA,CAAA0D,YAAA,CAA6B;IAMpCpE,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAAO,UAAA,oBAAAP,EAAA,CAAAqE,aAAA,CAAkB;IAO3DrE,EAAA,CAAAY,SAAA,GAA6E;IAA7EZ,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAA8D,GAAA,EAAA5D,MAAA,CAAA2B,eAAA,IAAA3B,MAAA,CAAA2C,aAAA,EAA6E;IAC1DrD,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAAO,UAAA,UAAAG,MAAA,CAAA2C,aAAA,CAAoB;IA8BpBrD,EAAA,CAAAY,SAAA,EAAmB;IAAnBZ,EAAA,CAAAO,UAAA,SAAAG,MAAA,CAAA2C,aAAA,CAAmB;IA0CxCrD,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAAO,UAAA,SAAAG,MAAA,CAAA2C,aAAA,CAAmB;;;;;;IA9F9CrD,EAAA,CAAAE,cAAA,cACwB;IAD+BF,EAAA,CAAAkB,UAAA,mBAAAqD,0DAAA;MAAAvE,EAAA,CAAAoB,aAAA,CAAAoD,GAAA;MAAA,MAAA9D,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAqD,UAAA,EAAY;IAAA,EAAC;IAG3E/D,EADA,CAAA2C,SAAA,YAA8B,kBAEQ;IAExC3C,EAAA,CAAAM,YAAA,EAAM;IAENN,EAAA,CAAAG,UAAA,IAAAsE,0CAAA,mBAA2G;;;;IARNzE,EAA/E,CAAAO,UAAA,iBAAAG,MAAA,CAAAgE,eAAA,CAAgC,oBAAkE;IAG7G1E,EAAA,CAAAY,SAAA,GAAwC;IAAxCZ,EAAA,CAAAO,UAAA,UAAAG,MAAA,CAAAiE,WAAA,CAAAC,WAAA,GAAwC;IAKI5E,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAAO,UAAA,SAAAG,MAAA,CAAAmE,cAAA,CAAoB;;;;;;IAuGnE7E,EAAA,CAAAE,cAAA,mBAC8C;IAA5CF,EAAA,CAAAkB,UAAA,mBAAA4D,0EAAA;MAAA9E,EAAA,CAAAoB,aAAA,CAAA2D,IAAA;MAAA,MAAArE,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAsE,aAAA,EAAe;IAAA,EAAC;IAD3BhF,EAAA,CAAAM,YAAA,EAC8C;;;;;IAO5CN,EAAA,CAAA2C,SAAA,eAAwD;;;;;;IAT5D3C,EAHJ,CAAAE,cAAA,cAAyB,cACkD,cAEnB;IAClDF,EAAA,CAAAG,UAAA,IAAA8E,+CAAA,uBAC8C;IAG5CjF,EADF,CAAAE,cAAA,cAAmD,cAGZ;IADnCF,EAAA,CAAAkB,UAAA,mBAAAgE,0DAAArD,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAA+D,IAAA;MAAA,MAAAzE,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAA0E,cAAA,CAAAC,oBAAA,CAAAxD,MAAA,CAA2C;IAAA,EAAC;IADvD7B,EAAA,CAAAM,YAAA,EAEqC;IACrCN,EAAA,CAAAG,UAAA,IAAAmF,kDAAA,mBAAuD;IAGzDtF,EAAA,CAAAM,YAAA,EAAM;IAEJN,EADF,CAAAE,cAAA,cAAgD,eACU;IAAAF,EAAA,CAAA0B,MAAA,GAAwB;IAKxF1B,EALwF,CAAAM,YAAA,EAAO,EACnF,EACF,EAEF,EACF;;;;IAjBWN,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAAO,UAAA,SAAAG,MAAA,CAAAC,gBAAA,IAAAD,MAAA,CAAAuC,UAAA,GAAsC;IAI1CjD,EAAA,CAAAY,SAAA,GAA+E;IAA/EZ,EAAA,CAAAO,UAAA,QAAAG,MAAA,CAAA0E,cAAA,CAAAG,qBAAA,CAAA7E,MAAA,CAAA8E,qBAAA,CAAA9E,MAAA,CAAA+E,UAAA,IAAAzF,EAAA,CAAAqE,aAAA,CAA+E;IAGpFrE,EAAA,CAAAY,SAAA,EAEC;IAFDZ,EAAA,CAAAsC,aAAA,IAAA5B,MAAA,CAAAgF,YAAA,CAAAhF,MAAA,CAAAiF,qBAAA,CAAAjF,MAAA,CAAA+E,UAAA,YAEC;IAGuDzF,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAA4F,iBAAA,CAAAlF,MAAA,CAAA+E,UAAA,kBAAA/E,MAAA,CAAA+E,UAAA,CAAAI,QAAA,CAAwB;;;;;;IAYtF7F,EAFF,CAAAE,cAAA,cAC6J,cAChH;IAAAF,EAAA,CAAA2C,SAAA,YAA4B;IAAA3C,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAA0B,MAAA,mBAAY;IAAO1B,EAAP,CAAAM,YAAA,EAAO,EAAM;IAEtGN,EAAA,CAAAE,cAAA,YAAgG;IAArDF,EAAA,CAAAkB,UAAA,mBAAA4E,sEAAA;MAAA9F,EAAA,CAAAoB,aAAA,CAAA2E,IAAA;MAAA,MAAArF,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAASZ,MAAA,CAAAsF,sBAAA,EAAwB;MAAA,OAAAhG,EAAA,CAAAuB,WAAA,CAAEb,MAAA,CAAAuF,aAAA,EAAe;IAAA,EAAE;IACjGjG,EADkG,CAAAM,YAAA,EAAI,EAChG;;;;;IAUEN,EAAA,CAAAkG,uBAAA,GAA4D;IAC1DlG,EAAA,CAAA2C,SAAA,YAAqC;;;;;;IAGrC3C,EAAA,CAAA2C,SAAA,YAA0B;;;;;IAaxB3C,EAAA,CAAAkG,uBAAA,GAAyD;IACvDlG,EAAA,CAAA2C,SAAA,YAAqC;;;;;;IAOjC3C,EAAA,CAAA2C,SAAA,cAA4D;;;;;;IAJhE3C,EAAA,CAAAE,cAAA,uBACmF;IADjBF,EAAA,CAAAkB,UAAA,sBAAAiF,qFAAAtE,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAgF,IAAA;MAAA,MAAA1F,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAYb,MAAA,CAAA2F,iBAAA,CAAAxE,MAAA,CAAyB;IAAA,EAAC;IAGtG7B,EAAA,CAAAG,UAAA,IAAAmG,iEAAA,0BAAoC;IAKtCtG,EAAA,CAAAM,YAAA,EAAe;;;IAPbN,EAAA,CAAAO,UAAA,cAAa;;;;;;IAtC7BP,EAAA,CAAAG,UAAA,IAAAoG,kDAAA,kBAAyB;IAenBvG,EANN,CAAAE,cAAA,iBAAgG,cAGN,cAE3D,cACQ;IAI/BF,EAHA,CAAAG,UAAA,IAAAqG,mDAAA,2BAA4D,IAAAC,kDAAA,gCAAAzG,EAAA,CAAA0G,sBAAA,CAGjC;IAG7B1G,EAAA,CAAAM,YAAA,EAAM;IAIFN,EAHJ,CAAAE,cAAA,aAAwB,UAClB,eAEqE;IAAjCF,EAAA,CAAAkB,UAAA,mBAAAyF,2DAAA;MAAA3G,EAAA,CAAAoB,aAAA,CAAAwF,IAAA;MAAA,MAAAlG,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAmG,oBAAA,EAAsB;IAAA,EAAC;IACpE7G,EAAA,CAAA2C,SAAA,eAAiE;IAErE3C,EADE,CAAAM,YAAA,EAAM,EACH;IAGHN,EAFF,CAAAE,cAAA,UAAI,eAEmC;IAInCF,EAHA,CAAAG,UAAA,KAAA2G,oDAAA,2BAAyD,KAAAC,mDAAA,gCAAA/G,EAAA,CAAA0G,sBAAA,CAGjC;IA0BlC1G,EAfQ,CAAAM,YAAA,EAAM,EACH,EACF,EACD,EAYF;IAONN,EAAA,CAAAE,cAAA,wBAI8D;IAA5DF,EAJqC,CAAAkB,UAAA,mBAAA8F,gEAAA;MAAAhH,EAAA,CAAAoB,aAAA,CAAAwF,IAAA;MAAA,MAAAlG,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAuG,kBAAA,EAAoB;IAAA,EAAC,mBAAAC,gEAAArF,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAwF,IAAA;MAAA,MAAAlG,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAUb,MAAA,CAAAyG,OAAA,CAAAtF,MAAA,CAAe;IAAA,EAAC,mBAAAuF,gEAAA;MAAApH,EAAA,CAAAoB,aAAA,CAAAwF,IAAA;MAAA,MAAAlG,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAEpFb,MAAA,CAAA2G,eAAA,EAAiB;IAAA,EAAC,mBAAAC,gEAAA;MAAAtH,EAAA,CAAAoB,aAAA,CAAAwF,IAAA;MAAA,MAAAW,sBAAA,GAAAvH,EAAA,CAAAwH,WAAA;MAAA,MAAA9G,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAClBiG,sBAAA,CAAAE,KAAA,EAA0B;MAAA,OAAAzH,EAAA,CAAAuB,WAAA,CAAEb,MAAA,CAAAuG,kBAAA,EAAoB;IAAA,EAAC,2BAAAS,wEAAA7F,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAwF,IAAA;MAAA,MAAAW,sBAAA,GAAAvH,EAAA,CAAAwH,WAAA;MAAA,MAAA9G,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CACzCb,MAAA,CAAAiH,aAAA,CAAA9F,MAAA,EAAA0F,sBAAA,CAAyC;IAAA,EAAC;IAACvH,EAAA,CAAAM,YAAA,EAAW;IACzEN,EAAA,CAAAE,cAAA,eAAoF;IAApDF,EAAA,CAAAkB,UAAA,mBAAA0G,2DAAA/F,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAwF,IAAA;MAAA,MAAAW,sBAAA,GAAAvH,EAAA,CAAAwH,WAAA;MAAA,MAAA9G,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAiH,aAAA,CAAA9F,MAAA,EAAA0F,sBAAA,CAAyC;IAAA,EAAC;IAEjFvH,EAAA,CAAA2C,SAAA,eAAiE;IAErE3C,EADE,CAAAM,YAAA,EAAM,EACF;;;;;;IA9ENN,EAAA,CAAAsC,aAAA,IAAA5B,MAAA,CAAA2B,eAAA,YAOC;IAE6CrC,EAAA,CAAAY,SAAA,EAAiD;IAAjDZ,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAmC,eAAA,IAAA0F,GAAA,GAAAnH,MAAA,CAAAuC,UAAA,IAAiD;IAG3FjD,EAAA,CAAAY,SAAA,GAAqF;IAArFZ,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAsH,GAAA,EAAApH,MAAA,CAAA2B,eAAA,IAAA3B,MAAA,CAAA2B,eAAA,IAAqF;IAIlErC,EAAA,CAAAY,SAAA,GAA0B;IAAAZ,EAA1B,CAAAO,UAAA,SAAAG,MAAA,CAAAqH,cAAA,KAA0B,aAAAC,gBAAA,CAAiB;IAiBvChI,EAAA,CAAAY,SAAA,GAA0B;IAAAZ,EAA1B,CAAAO,UAAA,SAAAG,MAAA,CAAAqH,cAAA,KAA0B,aAAAE,aAAA,CAAc;;;;;;IAoD/DjI,EAFJ,CAAAE,cAAA,eAAoB,gBAC2B,iBAEgB;IADRF,EAAA,CAAAkB,UAAA,mBAAAgH,4DAAArG,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAA+G,IAAA;MAAA,MAAAzH,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAA0H,cAAA,CAAAvG,MAAA,CAAsB;IAAA,EAAC;IAAnF7B,EAAA,CAAAM,YAAA,EAC2D;IAC3DN,EAAA,CAAA2C,SAAA,aAAyC;IAE7C3C,EADE,CAAAM,YAAA,EAAO,EACH;;;;;IAWFN,EAAA,CAAA2C,SAAA,gBAAoC;;;;;IAItC3C,EAAA,CAAAE,cAAA,eAA+B;IAAAF,EAAA,CAAA0B,MAAA,GAA2E;;IAAA1B,EAAA,CAAAM,YAAA,EAAM;;;;;;IAAjFN,EAAA,CAAAY,SAAA,EAA2E;IAA3EZ,EAAA,CAAA4F,iBAAA,CAAAlF,MAAA,CAAA2H,qBAAA,EAAAC,QAAA,GAAAtI,EAAA,CAAAuI,WAAA,OAAA7H,MAAA,CAAA8H,aAAA,oBAAAF,QAAA,CAAAG,aAAA,CAAAC,WAAA,GAA2E;;;;;IAMxG1I,EAAA,CAAAC,kBAAA,GAA0G;;;;;IAKtGD,EAAA,CAAA2C,SAAA,aAA2B;IAAC3C,EAAA,CAAA0B,MAAA,qBAC5B;;;;;IACA1B,EAAA,CAAA2C,SAAA,aAA0B;IAAC3C,EAAA,CAAA0B,MAAA,aAC3B;;;;;IALF1B,EAAA,CAAAkG,uBAAA,GAA4G;IAC1GlG,EAAA,CAAAG,UAAA,IAAAwI,yEAAA,OAA2E;;IAEzE3I,EAAA,CAAAG,UAAA,IAAAyI,yEAAA,OAAO;;;;;;;IAFT5I,EAAA,CAAAY,SAAA,EAIC;IAJDZ,EAAA,CAAAsC,aAAA,MAAAgG,QAAA,GAAAtI,EAAA,CAAAuI,WAAA,OAAA7H,MAAA,CAAA8H,aAAA,oBAAAF,QAAA,CAAAG,aAAA,CAAAC,WAAA,mBAAAJ,QAAA,CAAAG,aAAA,CAAAC,WAAA,EAAAG,kBAAA,UAIC;;;;;IAKD7I,EAAA,CAAA2C,SAAA,aAA2B;IAAC3C,EAAA,CAAA0B,MAAA,wBAC5B;;;;;IACA1B,EAAA,CAAA2C,SAAA,gBAAyF;;;;;;;IAAnF3C,EAAA,CAAAO,UAAA,eAAAuI,QAAA,GAAA9I,EAAA,CAAAuI,WAAA,OAAA7H,MAAA,CAAA8H,aAAA,oBAAAM,QAAA,CAAAL,aAAA,CAAAC,WAAA,mBAAAI,QAAA,CAAAL,aAAA,CAAAC,WAAA,EAAAK,WAAA,EAAA/I,EAAA,CAAAgJ,cAAA,CAA2E;;;;;IAHjFhJ,EAAA,CAAAG,UAAA,IAAA8I,wEAAA,OAA2E;;IAEzEjJ,EAAA,CAAAG,UAAA,IAAA+I,wEAAA,OAAO;;;;;;IAFTlJ,EAAA,CAAAsC,aAAA,MAAAgG,QAAA,GAAAtI,EAAA,CAAAuI,WAAA,OAAA7H,MAAA,CAAA8H,aAAA,oBAAAF,QAAA,CAAAG,aAAA,CAAAC,WAAA,mBAAAJ,QAAA,CAAAG,aAAA,CAAAC,WAAA,EAAAG,kBAAA,UAIC;;;;;IAKL7I,EAAA,CAAAE,cAAA,gBAAgG;IAAAF,EAAA,CAAA0B,MAAA,GAGrF;;;IAAA1B,EAAA,CAAAM,YAAA,EAAO;;;;;;IAH8EN,EAAA,CAAAY,SAAA,EAGrF;IAHqFZ,EAAA,CAAA4F,iBAAA,CAAA5F,EAAA,CAAAmJ,WAAA,SAAAb,QAAA,GAAAtI,EAAA,CAAAuI,WAAA,OAAA7H,MAAA,CAAA8H,aAAA,oBAAAF,QAAA,CAAAG,aAAA,CAAAC,WAAA,mBAAAJ,QAAA,CAAAG,aAAA,CAAAC,WAAA,EAAAU,aAAA,kBAAAd,QAAA,CAAAG,aAAA,CAAAC,WAAA,EAAAU,aAAA,CAAAC,OAAA,mBAGrF;;;;;;IAzCfrJ,EAAA,CAAAE,cAAA,eAEuF;IADrFF,EAAA,CAAAkB,UAAA,mBAAAoI,iEAAA;MAAA,MAAAb,aAAA,GAAAzI,EAAA,CAAAoB,aAAA,CAAAmI,IAAA,EAAAC,SAAA;MAAA,MAAA9I,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAA+I,aAAA,CAAAhB,aAAA,CAAwB;IAAA,EAAC;IAGhCzI,EADF,CAAAE,cAAA,eAAmD,eAEgE;IAA/GF,EAAA,CAAAkB,UAAA,mBAAAwI,iEAAA7H,MAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAmI,IAAA;MAAA,MAAA7I,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAA0E,cAAA,CAAAC,oBAAA,CAAAxD,MAAA,CAA2C;IAAA,EAAC;IADvD7B,EAAA,CAAAM,YAAA,EACiH;IACjHN,EAAA,CAAAG,UAAA,IAAAwJ,yDAAA,oBAAsD;IAGxD3J,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,UAAA,IAAAyJ,yDAAA,mBAAmF;;IAInF5J,EAAA,CAAAE,cAAA,eAA0B;IAGxBF,EAAA,CAAAG,UAAA,IAAA0J,0DAAA,4BAA2F;IAC3F7J,EAAA,CAAAE,cAAA,gBAAwB;IAAAF,EAAA,CAAA0B,MAAA,GAAsB;IAAA1B,EAAA,CAAAM,YAAA,EAAO;IACrDN,EAAA,CAAAE,cAAA,iBAA2B;IACzBF,EAAA,CAAAG,UAAA,KAAA2J,2DAAA,2BAA4G;;IAQ5G9J,EAAA,CAAAG,UAAA,KAAA4J,0DAAA,iCAAA/J,EAAA,CAAA0G,sBAAA,CAA0B;IAQ9B1G,EADE,CAAAM,YAAA,EAAO,EACH;IACNN,EAAA,CAAAE,cAAA,gBAA4B;IAC1BF,EAAA,CAAAG,UAAA,KAAA6J,mDAAA,oBAAgG;;IAKpGhK,EADE,CAAAM,YAAA,EAAM,EACF;;;;;;;;;;IAzCJN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAmC,eAAA,KAAA8H,GAAA,EAAAxB,aAAA,CAAAC,WAAA,KAAAhI,MAAA,CAAAe,aAAA,KAAAf,MAAA,CAAAuC,UAAA,IAAoF;IAE7EjD,EAAA,CAAAY,SAAA,GAA8E;IAA9EZ,EAAA,CAAAO,UAAA,QAAAG,MAAA,CAAA0E,cAAA,CAAAG,qBAAA,CAAA7E,MAAA,CAAA8E,qBAAA,CAAAiD,aAAA,IAAAzI,EAAA,CAAAqE,aAAA,CAA8E;IAEnFrE,EAAA,CAAAY,SAAA,EAEC;IAFDZ,EAAA,CAAAsC,aAAA,IAAA5B,MAAA,CAAAgF,YAAA,CAAAhF,MAAA,CAAAiF,qBAAA,CAAA8C,aAAA,YAEC;IAEHzI,EAAA,CAAAY,SAAA,EAEC;IAFDZ,EAAA,CAAAsC,aAAA,IAAA5B,MAAA,CAAA2H,qBAAA,EAAA6B,QAAA,GAAAlK,EAAA,CAAAuI,WAAA,QAAA7H,MAAA,CAAA8H,aAAA,oBAAA0B,QAAA,CAAAzB,aAAA,CAAAC,WAAA,gBAEC;IAKgB1I,EAAA,CAAAY,SAAA,GAAyC;IAAAZ,EAAzC,CAAAO,UAAA,qBAAA4J,yBAAA,CAAyC,4BAAAnK,EAAA,CAAAmC,eAAA,KAAAiI,IAAA,EAAA3B,aAAA,EAAiC;IACjEzI,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAA4F,iBAAA,CAAA6C,aAAA,CAAA5C,QAAA,CAAsB;IAE7B7F,EAAA,CAAAY,SAAA,GAA2E;IAAAZ,EAA3E,CAAAO,UAAA,UAAA8J,QAAA,GAAArK,EAAA,CAAAuI,WAAA,SAAA7H,MAAA,CAAA8H,aAAA,oBAAA6B,QAAA,CAAA5B,aAAA,CAAAC,WAAA,mBAAA2B,QAAA,CAAA5B,aAAA,CAAAC,WAAA,EAAA4B,iBAAA,CAA2E,aAAAC,eAAA,CAAgB;IAkBrGvK,EAAA,CAAAY,SAAA,GAAmE;IAAnEZ,EAAA,CAAAO,UAAA,UAAAiK,QAAA,GAAAxK,EAAA,CAAAuI,WAAA,SAAA7H,MAAA,CAAA8H,aAAA,oBAAAgC,QAAA,CAAA/B,aAAA,CAAAC,WAAA,mBAAA8B,QAAA,CAAA/B,aAAA,CAAAC,WAAA,EAAAK,WAAA,CAAmE;;;;;IAtC9E/I,EAAA,CAAAG,UAAA,IAAAsK,2CAAA,qBAEuF;;;;IAFpBzK,EAAA,CAAAO,UAAA,YAAAG,MAAA,CAAAgK,aAAA,GAAoB;;;;;IAiDnF1K,EAFJ,CAAAE,cAAA,cAA+B,cACY,eAEF;IACnCF,EAAA,CAAA2C,SAAA,aAAiD;IACnD3C,EAAA,CAAAM,YAAA,EAAM;IAAkDN,EAAlD,CAAAE,cAAA,gBAAiD,gBAAoC;IACvFF,EAAA,CAAA0B,MAAA,GAEA;IAGR1B,EAFM,CAAAM,YAAA,EAAO,EAAO,EACb,EACF;;;;IALGN,EAAA,CAAAY,SAAA,GAEA;IAFAZ,EAAA,CAAA2K,kBAAA,gEAAAjK,MAAA,CAAA+E,UAAA,kBAAA/E,MAAA,CAAA+E,UAAA,CAAAmF,WAAA,MAEA;;;;;IAQR5K,EAAA,CAAAE,cAAA,gBAA4D;IAAAF,EAAA,CAAA0B,MAAA,YAAK;IAAA1B,EAAA,CAAAM,YAAA,EAAO;;;;;IAExEN,EAAA,CAAAE,cAAA,gBAAiE;IAAAF,EAAA,CAAA0B,MAAA,cAAO;IAAA1B,EAAA,CAAAM,YAAA,EAAO;;;;;IAE/EN,EAAA,CAAAE,cAAA,gBAAiE;IAAAF,EAAA,CAAA0B,MAAA,cAAO;IAAA1B,EAAA,CAAAM,YAAA,EAAO;;;;;IAD7EN,EAJF,CAAAG,UAAA,IAAA0K,mDAAA,oBAA6F,IAAAC,mDAAA,OAEzC,IAAAC,mDAAA,OAE3C;;;;;IAJT/K,EAAA,CAAAsC,aAAA,KAAA0I,aAAA,kBAAAA,aAAA,CAAAC,QAAA,MAAAvK,MAAA,CAAAuK,QAAA,CAAAC,SAAA,KAAAF,aAAA,kBAAAA,aAAA,CAAAG,iBAAA,CAAA3I,MAAA,aAAAwI,aAAA,kBAAAA,aAAA,CAAAC,QAAA,MAAAvK,MAAA,CAAAuK,QAAA,CAAAG,IAAA,SAMC;;;AD7TH,IAAKC,QAGJ;AAHD,WAAKA,QAAQ;EACTA,QAAA,2BAAuB;EACvBA,QAAA,iBAAa;AACjB,CAAC,EAHIA,QAAQ,KAARA,QAAQ;AA+Bb,OAAM,MAAOC,aAAa;EA1B1BC,YAAA;IA+BY,KAAAC,QAAQ,GAAG9N,MAAM,CAACF,QAAQ,CAAC;IACnC,KAAAyF,UAAU,GAAGtF,KAAK,CAAC,KAAK,CAAC;IACjB,KAAA8N,IAAI,GAAG,IAAIpN,OAAO,EAAE;IAC5B,KAAAqN,IAAI,GAAG9N,MAAM,CAAC,EAAE,CAAC;IAGjB,KAAA+N,YAAY,GAAG,CAAC,CAAC;IACjB,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAjH,WAAW,GAAGjH,MAAM,CAACQ,WAAW,CAAC;IACjC,KAAA2N,cAAc,GAAGnO,MAAM,CAACsB,cAAc,CAAC;IACvC,KAAA8M,WAAW,GAAGpO,MAAM,CAACO,WAAW,CAAC;IACjC,KAAA8N,gBAAgB,GAAGrO,MAAM,CAACS,gBAAgB,CAAC;IAC3C,KAAA6N,WAAW,GAAGtO,MAAM,CAACU,WAAW,CAAC;IACjC,KAAAgH,cAAc,GAAG1H,MAAM,CAACkB,cAAc,CAAC;IACvC,KAAAqN,aAAa,GAAGvO,MAAM,CAACoB,aAAa,CAAC;IACrC,KAAAoN,YAAY,GAAGxO,MAAM,CAAC8B,YAAY,CAAC;IACnC,KAAA2M,IAAI,GAAGzO,MAAM,CAAC0B,UAAU,CAAC;IACzB,KAAAgN,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAG,EAAmB;IAC3B,KAAAC,WAAW,GAAqB,EAAE;IAElC,KAAAC,eAAe,GAAG3O,MAAM,CAAC,KAAK,CAAC;IAE/B,KAAA4O,iBAAiB,GAAa,EAAE;IAGhC,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAApK,eAAe,GAAGzE,MAAM,CAAC,KAAK,CAAC;IAC/B,KAAA2E,QAAQ,GAAG3E,MAAM,CAAC,EAAe,CAAC;IAClC,KAAA8O,YAAY,GAAG9O,MAAM,CAAC,EAAW,CAAC;IAC1B,KAAA+O,mBAAmB,GAAG,IAAI7O,eAAe,CAAQ,EAAE,CAAC;IAC5D,KAAA0K,aAAa,GAAG,IAAI,CAACmE,mBAAmB,CAACC,YAAY,EAAE;IACvD,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,aAAa,GAAG,IAAIxN,WAAW,CAAC,EAAE,CAAC;IAEnC,KAAAyN,IAAI,GAAG,IAAI,CAAClB,WAAW,CAACmB,eAAe,EAAE;IAEzC,KAAAtM,gBAAgB,GAAG,KAAK;IACxB,KAAAuM,iBAAiB,GAAmCrP,SAAS,CAAC,UAAU,CAAC;IACzE;IACA,KAAA4D,aAAa,GAAW,EAAE;IAC1B,KAAAgB,SAAS,GAAG7E,MAAM,CAAC,EAAW,CAAC;IAC/B,KAAA8M,aAAa,GAAGjN,QAAQ,CAAC,MAAK;MAC1B,IAAI,CAAC,IAAI,CAACiO,IAAI,EAAE,EAAE;QACd,OAAO,IAAI,CAACjJ,SAAS,EAAE;MAC3B,CAAC,MAAM;QACH,OAAO,IAAI,CAACA,SAAS,EAAE,CAAC0K,MAAM,CAACC,IAAI,IAC/B,IAAI,CAACC,gBAAgB,CAACD,IAAI,CAAC,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC7B,IAAI,EAAE,CAAC4B,WAAW,EAAE,CAAC,CAChF;MACL;IACJ,CAAC,CAAC;IAEF,KAAAjK,aAAa,GAAG,KAAK;IAErB,KAAAqB,eAAe,GAAW,OAAO;IACjC,KAAAN,YAAY,GAAW,QAAQ;IAC/B,KAAAS,cAAc,GAAG,KAAK;IACtB,KAAA2I,KAAK,GAAG5P,MAAM,CAAC,EAAW,CAAC;IAC3B,KAAAmK,cAAc,GAAG,CAAC;IAClB,KAAA0F,gBAAgB,GAAG,EAAW;IAC9B,KAAAC,WAAW,GAAQC,SAAS;IAC5B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAA3C,QAAQ,GAAGI,QAAQ;IAGX,KAAAwC,kBAAkB,GAAmB,IAAI,CAAC,CAAE;IAC5C,KAAAC,YAAY,GAA6E,IAAIC,GAAG,EAAE;IAC1G,KAAAC,KAAK,GAAG,EAAE,CAAC,CAAC;IACZ,KAAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACZ,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,WAAW,GAAGvQ,MAAM,CAAC,KAAK,CAAC;;EAC3BwK,cAAcA,CAACgG,KAAU;IACrB,MAAMzQ,KAAK,GAAGyQ,KAAK,CAACC,MAA0B;IAC9C,IAAI,CAAC3C,IAAI,CAAC4C,GAAG,CAAC3Q,KAAK,CAAC4Q,KAAK,CAAC;EAC9B;EAEAC,mBAAmBA,CAAA;IACf3O,YAAY,CAAC,IAAI,CAACsO,WAAW,EAAE;MAC3B3C,QAAQ,EAAE,IAAI,CAACA;KAClB,CAAC,CAACiD,SAAS,CAAC;MACTC,IAAI,EAAGC,IAAI,IAAI;QACXC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAIA,IAAI,EAAE;UACN,IAAI,CAACG,uBAAuB,CAAC,IAAI,CAACrN,aAAa,CAAC;QACpD;MACJ;KACH,CAAC;EACN;EAEAsC,UAAUA,CAAA;IACN,IAAI,CAACW,eAAe,GAAG,IAAI,CAACA,eAAe,KAAK,OAAO,GAAG,QAAQ,GAAG,OAAO;IAC5E,IAAI,CAACN,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;IACtE,IAAI,CAACS,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAACY,UAAU,GAAGkI,SAAS;IAC3B,IAAI,CAACtK,aAAa,GAAG,KAAK;IAC1B,IAAI,IAAI,CAACwB,cAAc,EAAE;MACrB,IAAI,CAACkK,qBAAqB,EAAE;IAChC;EACJ;EAEAC,QAAQA,CAAA;IAEJ,IAAI,CAACrK,WAAW,CAACsK,eAAe,EAAE,CAACR,SAAS,CAAES,GAAG,IAAI,CACrD,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA,IAAI,CAAClC,IAAI,GAAG,IAAI,CAAClB,WAAW,CAACmB,eAAe,EAAE;IAE9C,IAAI,CAAC,IAAI,CAAChK,UAAU,EAAE,EAAE;MAEpB,IAAI,CAAC8L,qBAAqB,EAAE;IAEhC;IACA,IAAI,CAAChC,aAAa,CAACoC,YAAY,CAACV,SAAS,CAAES,GAAG,IAAI,CAClD,CAAC,CAAC;IAEF,IAAI,CAACE,gBAAgB,EAAE;IACvB,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACb,mBAAmB,EAAE;IAC1B,IAAI,CAACc,qBAAqB,EAAE;IAE5B,IAAI,CAAC7B,gBAAgB,GAAG,CACpB;MACI8B,cAAc,EAAE;QACZC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE;OACpB;MACDC,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAEA,CAAA,KAAK,CACd;KACH,EACD;MACIJ,cAAc,EAAE;QACZC,YAAY,EAAE,QAAQ;QACtBC,eAAe,EAAE;OACpB;MACDC,IAAI,EAAE,eAAe;MACrBC,OAAO,EAAEA,CAAA,KAAK,CACd;KACH,EACD;MACIJ,cAAc,EAAE;QACZC,YAAY,EAAE,QAAQ;QACtBC,eAAe,EAAE;OACpB;MACDC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAEA,CAAA,KAAK,CACd;KACH,EACD;MACID,IAAI,EAAE,cAAc;MACpBH,cAAc,EAAE;QACZC,YAAY,EAAE,QAAQ;QACtBC,eAAe,EAAE;;KAExB,EACD;MACIF,cAAc,EAAE;QACZC,YAAY,EAAE,iBAAiB;QAC/BC,eAAe,EAAE;OACpB;MACDC,IAAI,EAAE,qBAAqB;MAC3BE,GAAG,EAAE;KACR,CACJ;EAEL;EACAC,kBAAkBA,CAAA,GAClB;EAEAC,eAAeA,CAAA;IACX;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;EAAA;EAGJC,WAAWA,CAAA;IACP,IAAI,IAAI,CAACC,UAAU,EAAE;MACjBC,aAAa,CAAC,IAAI,CAACD,UAAU,CAAC;MAC9BpB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACnC;IACA,IAAI,CAACxL,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACoI,IAAI,CAACyE,WAAW,EAAE;EAC3B;EAEA7I,eAAeA,CAAA;IACXuH,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;IACpB,IAAI,CAAClK,WAAW,CAACwL,kBAAkB,CAAC,IAAI,CAAC1O,aAAa,EAAE,IAAI,CAACuL,IAAI,CAACoD,SAAS,CAAC;EAChF;EAEAnJ,kBAAkBA,CAAA;IACd;IACA,MAAMoJ,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACC,aAAa;IACrDF,OAAO,CAACG,KAAK,CAACC,MAAM,GAAG,MAAM;IAC7BJ,OAAO,CAACG,KAAK,CAACC,MAAM,GAAG,GAAGJ,OAAO,CAACK,YAAY,IAAI;EACtD;EAEAC,OAAOA,CAACvC,KAAU;IACd,IAAI,CAACwC,YAAY,CAACxC,KAAK,CAACC,MAAM,CAAC;EACnC;EAEAuC,YAAYA,CAACC,QAA6B;IACtCA,QAAQ,CAACL,KAAK,CAACC,MAAM,GAAG,MAAM,CAAC,CAAC;IAChCI,QAAQ,CAACL,KAAK,CAACC,MAAM,GAAG,GAAGI,QAAQ,CAACH,YAAY,IAAI,CAAC,CAAC;EAC1D;EAEArK,iBAAiBA,CAAC+H,KAAU,EAAE0C,aAAa,GAAG,KAAK;IAC/ClC,OAAO,CAACC,GAAG,CAACT,KAAK,CAAC;IAClB,IAAI2C,YAAY,GAAG,EAAU;IAC7B,IAAID,aAAa,EAAE;MACfC,YAAY,GAAG3C,KAAK;IACxB,CAAC,MAAM;MACH2C,YAAY,GAAG3C,KAAK,CAAC4C,YAAY,CAAC,CAAC,CAAS;IAChD;IAEA,IAAID,YAAY,EAAE;MACd,IAAIE,OAAO,GAAG,IAAI/R,WAAW,EAAE,CAAC,CAAE;MAClC,MAAMgS,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,YAAY,CAAC;MACrCE,OAAO,GAAG,IAAI/R,WAAW,CAAC;QACtB,cAAc,EAAE6R,YAAY,CAACM,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAG;QACnD,eAAe,EAAE,SAAS,GAAG,IAAI,CAACzF,WAAW,CAAC0F,QAAQ;OACzD,CAAC;MACF,MAAMC,MAAM,GAAW;QACnBC,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAEZ,YAAY;QAClBa,KAAK,EAAE;OACV;MACD,IAAI,CAACC,aAAa,CAACZ,OAAO,EAAE,EAAE,EAAEC,QAAQ,EAAEO,MAAM,CAAC;IACrD;EACJ;EAEAI,aAAaA,CAACZ,OAAoB,EAAEa,cAAsB,EAAEZ,QAAkB,EAAEO,MAAc;IAC1F,IAAI,CAAChG,IAAI,CAACsG,GAAG,CAAC,IAAI,CAAC5F,IAAI,CAAC6F,IAAI,CAAC3S,WAAW,CAAC4S,MAAM,GAAG,2BAA2B,GAAGH,cAAc,EAAEZ,QAAQ,EAAE;MACtGD,OAAO,EAAEA,OAAO;MAChBiB,cAAc,EAAE,IAAI;MACpBC,OAAO,EAAE;KACZ,CAAC,CACG1D,SAAS,CAAEL,KAAU,IAAI;MAEtB,IAAIA,KAAK,CAACgE,IAAI,KAAKjT,aAAa,CAACkT,cAAc,EAAE;QAC7CZ,MAAM,CAACC,QAAQ,GAAGY,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGnE,KAAK,CAACoE,MAAM,GAAGpE,KAAK,CAACqE,KAAM,CAAC;QAC/D,IAAI,CAAC1K,cAAc,GAAG0J,MAAM,CAACC,QAAQ;MACzC,CAAC,MAAM,IAAItD,KAAK,CAACgE,IAAI,KAAKjT,aAAa,CAACuT,QAAQ,EAAE;QAC9C9D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAET,KAAK,CAACuE,IAAI,CAAC;QAC3C,IAAI,CAACC,WAAW,CAACxE,KAAK,CAACuE,IAAI,CAACE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;QAChDC,UAAU,CAAC,MAAK;UACZ,IAAI,CAACC,cAAc,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;QACP,IAAI,CAAChL,cAAc,GAAG,CAAC;QACvB;MACJ;MAEA,IAAIqG,KAAK,CAACoE,MAAM,KAAKpE,KAAK,CAACqE,KAAK,EAAE;QAC9B7D,OAAO,CAACC,GAAG,CAAC1P,aAAa,CAAC;MAC9B;MACA,IAAIiP,KAAK,CAACgE,IAAI,KAAKjT,aAAa,CAACkT,cAAc,EAAE,CACjD,CAAC,MACI,IAAIjE,KAAK,CAACgE,IAAI,KAAKjT,aAAa,CAACuT,QAAQ,EAAE;QAE5C;QACA;QACA;QACA;QACA;QACA;MAAA,CACH,MAAM,CACP;IACJ,CAAC,EAAGM,GAAQ,IAAI;MACZpE,OAAO,CAACC,GAAG,CAACmE,GAAG,CAAC;IACpB,CAAC,CAAC,CAAC;EACX;EAEA3F,gBAAgBA,CAAC4F,aAAwB;IACrC,IAAI,CAACA,aAAa,EAAE;MAChB,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACnH,WAAW,CAACoH,SAAS,EAAE;MAC5B,OAAO,IAAI,CAACnH,gBAAgB,CAACoH,wBAAwB,CAACF,aAAa,CAAC9H,iBAAiB,CAAC;IAC1F,CAAC,MAAM;MACH,OAAO8H,aAAa,CAACG,QAAQ,GAAG,KAAK,GAAGH,aAAa,CAACI,OAAO,EAAEC,SAAS;IAC5E;EACJ;EAEAC,mBAAmBA,CAACN,aAAwB;IACxC,IAAI,CAACA,aAAa,EAAE;MAChB,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACnH,WAAW,CAACoH,SAAS,EAAE;MAC5B,OAAO,IAAI,CAACnH,gBAAgB,CAACoH,wBAAwB,CAACF,aAAa,CAAC9H,iBAAiB,CAAC;IAC1F,CAAC,MAAM;MACH,OAAO8H,aAAa,CAACI,OAAO,EAAEC,SAAS;IAC3C;EACJ;EAEA9N,qBAAqBA,CAACyN,aAAwB;IAC1C,IAAI,CAACA,aAAa,EAAE;MAChB,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACnH,WAAW,CAACoH,SAAS,EAAE;MAC5B,OAAOD,aAAa,CAAC9H,iBAAiB,CAAC,CAAC,CAAC,CAACqI,SAAS;IACvD,CAAC,MAAM;MACH,OAAOP,aAAa,CAACI,OAAO,EAAEG,SAAS;IAC3C;EACJ;EAEA7N,qBAAqBA,CAACsN,aAAwB;IAC1C,IAAI,CAACA,aAAa,EAAE;MAChB,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACnH,WAAW,CAACoH,SAAS,EAAE;MAC5B,OAAOD,aAAa,CAAC9H,iBAAiB,CAAC,CAAC,CAAC,CAACiF,SAAS;IACvD,CAAC,MAAM;MACH,OAAO6C,aAAa,CAACI,OAAO,EAAEjD,SAAS;IAC3C;EACJ;EAEArB,qBAAqBA,CAAA;IACjB,IAAI,CAACtD,IAAI,CAACgI,IAAI,GAAG,IAAI,CAAC1H,gBAAgB,CAAC2H,oBAAoB,CAAC,IAAI,CAAC1G,IAAI,CAAC2G,EAAE,CAAC,CAAClF,SAAS,CAACS,GAAG,IAAG;MACtF,IAAI,IAAI,CAACpD,WAAW,CAACoH,SAAS,EAAE;QAC5B,IAAI,CAACU,gBAAgB,CAAC1E,GAAG,CAAC;MAC9B,CAAC,MAAM,IAAI,IAAI,CAACpD,WAAW,CAAC+H,SAAS,EAAE;QACnC,IAAI,CAACC,gBAAgB,CAAC5E,GAAG,CAAC;MAC9B;MACA,IAAI,CAAC6E,aAAa,CAAC7E,GAAG,CAAC;IAE3B,CAAC,CAAC;EAEN;EAEA0E,gBAAgBA,CAACI,UAAuB;IACpC,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,EAAU;IAC1C,MAAM1G,KAAK,GAAGwG,UAAU,CAACG,GAAG,CAACC,SAAS,IAAG;MACrC,MAAMC,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAACF,SAAS,CAAC;MACzD;MACA,IAAIA,SAAS,CAACjJ,iBAAiB,CAAC3I,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAO,CAAC6R,aAAa,CAAC;MAC1B;MACA,MAAME,YAAY,GAAGH,SAAS,CAACjJ,iBAAiB,CAC3CgC,MAAM,CAACqH,OAAO,IAAI,CAACP,gBAAgB,CAACQ,GAAG,CAAC,CAACD,OAAO,CAACb,EAAE,CAAC,CAAC,CACrDQ,GAAG,CAACK,OAAO,IAAG;QACXP,gBAAgB,CAAClC,GAAG,CAAC,CAACyC,OAAO,CAACb,EAAE,CAAC;QACjC,OAAO,IAAI,CAACe,iBAAiB,CAACN,SAAS,EAAEI,OAAO,CAAC;MACrD,CAAC,CAAC;MACN,OAAO,CAACH,aAAa,EAAE,GAAGE,YAAY,CAAC;IAC3C,CAAC,CAAC;IACF,IAAI,CAAC9R,SAAS,CAAC6L,GAAG,CAACd,KAAK,CAACmH,IAAI,EAAE,CAAC;EACpC;EAEAb,gBAAgBA,CAACE,UAAuB;IACpC,MAAMY,gBAAgB,GAAG,IAAIV,GAAG,EAAU;IAC1C,MAAM1G,KAAK,GAAGwG,UAAU,CAACG,GAAG,CAACC,SAAS,IAAG;MACrC,MAAMC,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAACF,SAAS,CAAC;MACzD,MAAMS,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAACV,SAAS,CAAC;MAG3D,IAAIA,SAAS,CAACjJ,iBAAiB,CAAC3I,MAAM,GAAG,CAAC,EAAE;QACxC,OAAO,CAAC6R,aAAa,EAAEQ,cAAc,CAAC;MAC1C;MACA;MACA,IAAI,CAACD,gBAAgB,CAACH,GAAG,CAAC,CAACI,cAAc,CAACE,SAAU,CAAC,EAAE;QACnDH,gBAAgB,CAAC7C,GAAG,CAAC,CAAC8C,cAAc,CAACE,SAAU,CAAC;QAChD,OAAO,CAACV,aAAa,CAAC;MAC1B,CAAC,MAAM;QACH,OAAO,CAACA,aAAa,CAAC;MAC1B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC5R,SAAS,CAAC6L,GAAG,CAACd,KAAK,CAACmH,IAAI,EAAE,CAAC;EACpC;EAEAL,mBAAmBA,CAACF,SAAoB;IAEpC,IAAI/C,IAAI,GAAG,EAAE;IAEb,IAAI,IAAI,CAACvF,WAAW,CAACoH,SAAS,EAAE;MAC5B7B,IAAI,GAAG,IAAI,CAACtF,gBAAgB,CAACoH,wBAAwB,CAACiB,SAAS,CAACjJ,iBAAiB,CAAC;IACtF,CAAC,MAAM;MACH;MACA,IAAIiJ,SAAS,CAACjJ,iBAAiB,CAAC3I,MAAM,GAAG,CAAC,EAAE;QACxC6O,IAAI,GAAG,IAAI,CAACjM,cAAc,CAAC4P,gBAAgB,CAACZ,SAAS,CAACf,OAAO,CAAC,GAAG,KAAK,GAAG,IAAI,CAACtH,gBAAgB,CAACoH,wBAAwB,CAACiB,SAAS,CAACjJ,iBAAiB,CAAC;MACxJ,CAAC,MAAM;QACHkG,IAAI,GAAG,IAAI,CAACjM,cAAc,CAAC4P,gBAAgB,CAACZ,SAAS,CAACf,OAAO,CAAC;MAClE;IACJ;IAEA,OAAO;MACH3K,WAAW,EAAE0L,SAAS,CAACT,EAAE;MACzBsB,cAAc,EAAEb,SAAS,CAACc,WAAW;MACrCC,iBAAiB,EAAEf,SAAS,CAAChB,QAAQ;MACrCC,OAAO,EAAEe,SAAS,CAACf,OAAO;MAC1BpI,QAAQ,EAAEI,QAAQ,CAACH,SAAS;MAC5BrF,QAAQ,EAAEwL,IAAI;MACd+D,WAAW,EAAE,EAAE;MACfjK,iBAAiB,EAAEiJ,SAAS,CAACjJ,iBAAiB;MAC9CP,WAAW,EAAE,IAAI,CAACmB,gBAAgB,CAACoH,wBAAwB,CAACiB,SAAS,CAACjJ,iBAAiB;KAC1F;EACL;EAEAuJ,iBAAiBA,CAACN,SAAoB,EAAEI,OAAa;IAEjD,IAAInD,IAAI,GAAG,EAAE;IAEb,IAAI,IAAI,CAACvF,WAAW,CAACoH,SAAS,EAAE;MAC5B7B,IAAI,GAAG,IAAI,CAACtF,gBAAgB,CAACoH,wBAAwB,CAACiB,SAAS,CAACjJ,iBAAiB,CAAC;IACtF,CAAC,MAAM;MACH;MACA,IAAIiJ,SAAS,CAACjJ,iBAAiB,CAAC3I,MAAM,GAAG,CAAC,EAAE;QACxC6O,IAAI,GAAG,IAAI,CAACjM,cAAc,CAAC4P,gBAAgB,CAACZ,SAAS,CAACf,OAAO,CAAC,GAAG,KAAK,GAAG,IAAI,CAACtH,gBAAgB,CAACoH,wBAAwB,CAACiB,SAAS,CAACjJ,iBAAiB,CAAC;MACxJ,CAAC,MAAM;QACHkG,IAAI,GAAG,IAAI,CAACjM,cAAc,CAAC4P,gBAAgB,CAACZ,SAAS,CAACf,OAAO,CAAC;MAClE;IACJ;IACA,OAAO;MACH3K,WAAW,EAAE,WAAW0L,SAAS,CAACf,OAAO,EAAEM,EAAE,YAAYa,OAAO,CAACpE,SAAS,EAAE;MAC5EiF,SAAS,EAAEb,OAAO,CAACb,EAAE;MACrB/I,WAAW,EAAEyG,IAAI;MACjBpG,QAAQ,EAAEI,QAAQ,CAACD,IAAI;MACvBvF,QAAQ,EAAE,IAAI,CAACT,cAAc,CAAC4P,gBAAgB,CAACR,OAAO,CAAC;MACvDY,WAAW,EAAE,EAAE;MACf/B,OAAO,EAAEe,SAAS,CAACf,OAAO;MAC1BlI,iBAAiB,EAAEiJ,SAAS,CAACjJ;KAChC;EACL;EAEA2J,oBAAoBA,CAACV,SAAoB;IACrC,IAAI/C,IAAI,GAAG,EAAE;IAEb,IAAI,IAAI,CAACvF,WAAW,CAACoH,SAAS,EAAE;MAC5B7B,IAAI,GAAG,IAAI,CAACtF,gBAAgB,CAACoH,wBAAwB,CAACiB,SAAS,CAACjJ,iBAAiB,CAAC;IACtF,CAAC,MAAM;MACH;MACA,IAAIiJ,SAAS,CAACjJ,iBAAiB,CAAC3I,MAAM,GAAG,CAAC,EAAE;QACxC6O,IAAI,GAAG,IAAI,CAACjM,cAAc,CAAC4P,gBAAgB,CAACZ,SAAS,CAACf,OAAO,CAAC;MAClE,CAAC,MAAM;QACHhC,IAAI,GAAG,IAAI,CAACjM,cAAc,CAAC4P,gBAAgB,CAACZ,SAAS,CAACf,OAAO,CAAC;MAClE;IACJ;IAEA,OAAO;MACH3K,WAAW,EAAE,WAAW0L,SAAS,CAACf,OAAO,EAAEM,EAAE,YAAY,IAAI,CAAC3G,IAAI,CAAC2G,EAAE,EAAE;MACvEoB,SAAS,EAAEX,SAAS,CAACf,OAAO,EAAEM,EAAE;MAChC0B,SAAS,EAAE,IAAI,CAACrI,IAAI,CAAC2G,EAAE;MACvB1I,QAAQ,EAAEI,QAAQ,CAACD,IAAI;MACvBvF,QAAQ,EAAEwL,IAAI;MACd+D,WAAW,EAAE,EAAE;MACf/B,OAAO,EAAEe,SAAS,CAACf,OAAO;MAC1BlI,iBAAiB,EAAEiJ,SAAS,CAACjJ,iBAAiB;MAC9CP,WAAW,EAAE,IAAI,CAACmB,gBAAgB,CAACoH,wBAAwB,CAACiB,SAAS,CAACjJ,iBAAiB;KAC1F;EACL;EACAmK,gBAAgBA,CAACC,MAAc;IAC3B,IAAI,CAACC,2BAA2B,EAAE;IAClC,IAAI,CAAC/T,aAAa,GAAG8T,MAAM;IAC3B,IAAI,CAAC1H,kBAAkB,GAAG,EAAa,CAAC,CAAE;IAE1C;IACA,MAAM4H,UAAU,GAAG,IAAI,CAAC3H,YAAY,CAAC4H,GAAG,CAACH,MAAM,CAAC;IAChD,IAAIE,UAAU,IAAI,IAAI,CAACE,YAAY,CAACF,UAAU,CAAC,EAAE;MAC7C,IAAI,CAACG,mBAAmB,CAACH,UAAU,CAAC;MACpC7G,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACxC,CAAC,MAAM;MACH;MACA,IAAI,CAACC,uBAAuB,CAACyG,MAAM,CAAC;IACxC;EACJ;EAEQK,mBAAmBA,CAACH,UAAwE;IAChG,IAAI,CAAClT,QAAQ,CAAC+L,GAAG,CAACmH,UAAU,CAAClT,QAAQ,CAAC;IACtC,IAAI,CAACsL,kBAAkB,GAAG4H,UAAU,CAAC5H,kBAAkB;EAC3D;EAEA2H,2BAA2BA,CAAA;IACvB,IAAI,IAAI,CAACK,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAAC3F,WAAW,EAAE;MAC1C,IAAI,CAAC2F,uBAAuB,GAAG,IAAI;IACvC;IACA,IAAI,CAAC3H,YAAY,GAAG,KAAK;EAC7B;EAEAY,uBAAuBA,CAACyG,MAAc;IAClC,IAAI,IAAI,CAACrH,YAAY,EAAE;MACnBU,OAAO,CAACkH,IAAI,CAAC,+CAA+C,CAAC;MAC7D;IACJ;IAEA,IAAI,CAACD,uBAAuB,GAAG,IAAI,CAAClR,WAAW,CAACoR,eAAe,CAACR,MAAM,EAAE,IAAI,CAACvH,KAAK,CAAC,CAACS,SAAS,CAAElM,QAAmB,IAAI;MAClH,MAAMyT,gBAAgB,GAAGzT,QAAQ,CAAC0T,OAAO,EAAE;MAC3C,IAAI,CAAC1T,QAAQ,CAAC+L,GAAG,CAAC0H,gBAAgB,CAAC;MAEnCpH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACtM,QAAQ,EAAE,CAAC;MACtD,IAAI,CAACsL,kBAAkB,GAAGmI,gBAAgB,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACE,aAAa,CAACX,MAAM,EAAES,gBAAgB,EAAE,IAAI,CAACnI,kBAAkB,CAAC;MAErE,IAAI,IAAI,CAACxK,aAAa,EAAE;QACpB,IAAI,CAAC8M,kBAAkB,CAACoF,MAAM,CAAC;MACnC;MACA,IAAI,CAACxC,cAAc,EAAE;MACrB,IAAI,CAAC7E,YAAY,GAAG,IAAI;IAC5B,CAAC,CAAC;EACN;EAEA1M,gBAAgBA,CAAC+T,MAAc;IAC3B,IAAI,CAAC,IAAI,CAAC1H,kBAAkB,EAAE;IAE9B,IAAI,CAAClJ,WAAW,CAACoR,eAAe,CAACR,MAAM,EAAE,IAAI,CAACvH,KAAK,EAAE,IAAI,CAACH,kBAAkB,CAAC,CAACY,SAAS,CAAElM,QAAmB,IAAI;MAC5G,MAAM4T,gBAAgB,GAAG,IAAI,CAAC5T,QAAQ,EAAE;MACxC,MAAM6T,eAAe,GAAG,CAAC,GAAG7T,QAAQ,CAAC0T,OAAO,EAAE,EAAE,GAAGE,gBAAgB,CAAC;MACpE,IAAI,CAAC5T,QAAQ,CAAC+L,GAAG,CAAC8H,eAAe,CAAC;MAElC,IAAI7T,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACrB,IAAI,CAACqL,kBAAkB,GAAGtL,QAAQ,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC2T,aAAa,CAACX,MAAM,EAAEa,eAAe,EAAE,IAAI,CAACvI,kBAAkB,CAAC;MACxE;IACJ,CAAC,CAAC;EACN;EAEQ8H,YAAYA,CAACF,UAAwE;IACzF,OAAOA,UAAU,EAAE5H,kBAAkB,EAAE8F,EAAE,KAAK8B,UAAU,CAAClT,QAAQ,CAAC,CAAC,CAAC,EAAEoR,EAAE;EAC5E;EAEQuC,aAAaA,CAACX,MAAc,EAAEhT,QAAmB,EAAEsL,kBAA2B;IAClF,IAAI,CAACC,YAAY,CAACQ,GAAG,CAACiH,MAAM,EAAE;MAAEhT,QAAQ;MAAEsL;IAAkB,CAAE,CAAC;EACnE;EAEQsC,kBAAkBA,CAACoF,MAAc;IACrC,IAAI,CAAC5Q,WAAW,CAACwL,kBAAkB,CAACoF,MAAM,EAAE,IAAI,CAACvI,IAAI,CAACoD,SAAS,CAAC;IAChE,IAAI,CAACzL,WAAW,CAAC0R,sBAAsB,CAACd,MAAM,EAAE,IAAI,CAACvI,IAAI,CAACoD,SAAS,CAAC;EACxE;EAEAkG,mBAAmBA,CAAA;IACf,IAAI,CAACxI,YAAY,CAACyI,KAAK,EAAE;EAC7B;EAGA5O,aAAaA,CAACyG,KAAY,EAAEoI,UAA4B;IACpD,MAAM3F,QAAQ,GAAGzC,KAAK,CAACC,MAA6B;IACpD,MAAMoI,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE;IACtB;IACA,IAAI,CAACjK,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACW,MAAM,CAACwJ,SAAS,IAAIF,GAAG,GAAGE,SAAS,GAAG,KAAK,CAAC;IAE5F,IAAI,IAAI,CAACnK,iBAAiB,CAAChK,MAAM,GAAG,CAAC,EAAE;MAEnC,MAAMoU,eAAe,GAAGtE,IAAI,CAACuE,GAAG,CAAC,GAAG,IAAI,CAACrK,iBAAiB,CAAC;MAC3D,MAAMsK,QAAQ,GAAG,EAAE,GAAGxE,IAAI,CAACyE,KAAK,CAAC,CAACN,GAAG,GAAGG,eAAe,IAAI,IAAI,CAAC;MAEhE,IAAI,CAAC1K,YAAY,CAAC8K,mBAAmB,CAAC;QAClCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE,8DAA8DL,QAAQ;OACjF,CAAC;MAEF;IACJ,CAAC,MAAM;MACH,IAAI,CAACtK,iBAAiB,CAAC4K,IAAI,CAACX,GAAG,CAAC,CAAC,CAAC;IACtC;IAEA,IAAIY,GAAG,GAAGb,UAAU,CAACjI,KAAK,EAAE+I,IAAI,EAAE;IAElC,IAAID,GAAG,CAAC7U,MAAM,EAAE;MACZ;MACA6U,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MAEhC;MACA,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CF,OAAO,CAACG,SAAS,GAAGN,GAAG,CAAC,CAAC;MAEzB,IAAI,IAAI,CAAChV,eAAe,EAAE,EAAE;QACxB,IAAI,CAACuV,cAAc,CAACC,OAAO,GAAGL,OAAO,CAACG,SAAS;QAC/C,IAAI,CAAChT,WAAW,CAAC3C,WAAW,CAAC,IAAI,CAACP,aAAa,EAAE,IAAI,CAACmW,cAAc,CAACjE,EAAE,EAAE,IAAI,CAACiE,cAAc,EAAE,IAAI,CAAC5K,IAAI,EAAE,IAAI,CAACP,wBAAwB,CAAC;MAC3I,CAAC,MAAM;QAEH;QACA,IAAI,CAACmG,WAAW,CAAC4E,OAAO,CAACG,SAAS,CAAC;MACvC;MACA,IAAI,CAACtV,eAAe,CAACiM,GAAG,CAAC,KAAK,CAAC;IACnC;IAEAkI,UAAU,CAACjI,KAAK,GAAG,EAAE;IAErBuE,UAAU,CAAC,MAAK;MACZjC,QAAQ,CAACiH,IAAI,GAAG,CAAC;MACjBjH,QAAQ,CAACtC,KAAK,GAAG,EAAE;MACnB;MACA,IAAI,CAACtH,kBAAkB,EAAE;IAC7B,CAAC,EAAE,EAAE,CAAC;EACV;EAEAnF,aAAaA,CAACiW,OAA2B;IACrCnJ,OAAO,CAACC,GAAG,CAACkJ,OAAO,CAAC;IACpB,IAAIA,OAAO,CAAC,CAAC,CAAC,CAACC,YAAY,EAAE;MACzB,IAAI,CAACrT,WAAW,CAACsT,gCAAgC,CAACF,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,CAACpJ,SAAS,CAAC;QAC5EC,IAAI,EAAGQ,GAAG,IAAI;UACVN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;UAChB,IAAI,CAACvK,WAAW,CAAC7C,aAAa,CAAC,IAAI,CAACL,aAAa,EAAEsW,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC/K,IAAI,EAAE+K,OAAO,CAAC,CAAC,CAAC,CAAC;QACzF,CAAC;QACDG,KAAK,EAAEA,CAAA,KAAK;UACR,IAAI,CAACvT,WAAW,CAAC7C,aAAa,CAAC,IAAI,CAACL,aAAa,EAAEsW,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC/K,IAAI,EAAE+K,OAAO,CAAC,CAAC,CAAC,CAAC;QACzF;OACH,CAAC;IACN,CAAC,MAAM;MACH,IAAI,CAACpT,WAAW,CAAC7C,aAAa,CAAC,IAAI,CAACL,aAAa,EAAEsW,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC/K,IAAI,EAAE+K,OAAO,CAAC,CAAC,CAAC,CAAC;IACzF;EAEJ;EAEA/V,WAAWA,CAAC+V,OAAY;IACpB,IAAI,CAAC1V,eAAe,CAACiM,GAAG,CAAC,IAAI,CAAC;IAC9BM,OAAO,CAACC,GAAG,CAACkJ,OAAO,CAAC;IACpB,IAAI,CAACH,cAAc,GAAGG,OAAO,CAAC,CAAC,CAAC;IAChC,IAAI,CAACtL,wBAAwB,GAAGsL,OAAO,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,CAACzH,kBAAkB,EAAE;MACzB,MAAM6H,gBAAgB,GAAG,IAAI,CAACP,cAAc,CAACC,OAAO,CAACN,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC;MAClF,IAAI,CAACjH,kBAAkB,CAACC,aAAa,CAAChC,KAAK,GAAG4J,gBAAgB,CAAC,CAAC;MAChE,IAAI,CAAClR,kBAAkB,EAAE,CAAC,CAAC;IAC/B;EACJ;EAEA8L,cAAcA,CAAA;IACV,IAAI;MACAD,UAAU,CAAC,MAAK;QACZ,IAAI,IAAI,CAAC5F,iBAAiB,EAAE,IAAI,IAAI,CAACA,iBAAiB,EAAG,CAACqD,aAAa,EAAE;UACrE,IAAI,CAACrD,iBAAiB,EAAG,CAACqD,aAAa,CAAC6H,SAAS,GAAG,IAAI,CAAClL,iBAAiB,EAAG,CAACqD,aAAa,CAACG,YAAY;QAC5G;MACJ,CAAC,EAAE,GAAG,CAAC;IACX,CAAC,CAAC,OAAOsC,GAAG,EAAE,CAAE;EACpB;EAEAvJ,aAAaA,CAACuD,IAAS;IACnB,IAAI,CAACvH,UAAU,GAAGuH,IAAI;IACtB,IAAI,CAAC,IAAI,CAACvH,UAAU,EAAE;MAClB;IACJ;IACA,IAAI,CAACpC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACsB,WAAW,CAAC0T,WAAW,CAAC/J,GAAG,CAAC,IAAI,CAAC7I,UAAU,CAAC;IACjD,IAAI,CAAC6P,gBAAgB,CAAC,IAAI,CAAC7P,UAAU,CAACiD,WAAW,CAAC;IAClD,IAAI,CAAC/D,WAAW,CAAC0R,sBAAsB,CAAC,IAAI,CAAC5U,aAAa,EAAE,IAAI,CAACuL,IAAI,CAACoD,SAAS,CAAC;IAGhF,IAAI,CAACpK,sBAAsB,EAAE;IAC7B;EACJ;EAEAsS,WAAWA,CAAA;IACP,IAAI,CAACC,eAAe,GAAG5K,SAAS;IAChC,IAAI,CAAChC,YAAY,GAAG,CAAC,CAAC;IACtB;EACJ;EAEQ6M,iBAAiBA,CAAA;IACrB;IACA,IAAI,CAAC7T,WAAW,CAACC,WAAW,CAAC0J,GAAG,CAAC,CAAC,CAAC;IAEnC,MAAMmK,yBAAyB,GAAG,EAAS;IAE3C,IAAI,CAAChW,SAAS,EAAE,CAACiW,OAAO,CAAEtE,SAAc,IAAI;MACxC,IAAI,CAACzP,WAAW,CAACgU,iCAAiC,CAACvE,SAAS,CAAC1L,WAAW,EAAE,IAAI,CAACsE,IAAI,CAACoD,SAAS,CAAC,CACzF3B,SAAS,CAACmK,KAAK,IAAG;QACf,MAAMC,uBAAuB,GAAG;UAC5BzE,SAAS,EAAEA,SAAS;UACpBxP,WAAW,EAAEgU;SAChB;QAEDH,yBAAyB,CAACrE,SAAS,CAAC1L,WAAW,CAAC,GAAGmQ,uBAAuB;QAE1EjK,OAAO,CAACC,GAAG,CAAC,uCAAuCuF,SAAS,CAAC1L,WAAW;kBAC1E,IAAI,CAACsE,IAAI,CAACsG,SAAS,GAAG,GAAG,GAAG,IAAI,CAACtG,IAAI,CAAC8L,QAAQ,mBAAmB,IAAI,CAAC9L,IAAI,CAACoD,SAAS,KAAKwI,KAAK,EAAE,CAAC;QAC/F,MAAMG,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAACR,yBAAyB,CAAC,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAY,KAAKD,GAAG,GAAGC,OAAO,CAACxU,WAAW,EAAE,CAAC,CAAW;QACvI,IAAI,CAACD,WAAW,CAACC,WAAW,CAAC0J,GAAG,CAACyK,gBAAgB,CAAC;QAClD;QACA;MACJ,CAAC,CAAC;IAEV,CAAC,CAAC;EAEN;EAEAhF,aAAaA,CAACC,UAAiB;IAE3B;IACA,IAAI,CAACwE,iBAAiB,EAAE;IAExB;IACA,MAAM9L,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAAC0M,QAAQ,EAAE;IAExDrF,UAAU,CAAC0E,OAAO,CAACtE,SAAS,IAAG;MAC3B;MACA;MACA;MACA;MACA;MACA,IAAI,CAACzP,WAAW,CAAC2U,cAAc,CAAClF,SAAS,CAACT,EAAE,CAAC,CAAClF,SAAS,CAAC;QACpDC,IAAI,EAAGqJ,OAAY,IAAI;UACnBrL,YAAY,CAAC0H,SAAS,CAACT,EAAE,CAAC,GAAGoE,OAAO;UACpC,IAAI,CAACwB,sBAAsB,CAAC7M,YAAY,CAAC;QAC7C,CAAC;QACDwL,KAAK,EAAGlF,GAAG,IAAI;UACXpE,OAAO,CAACsJ,KAAK,CAAC,iDAAiD9D,SAAS,CAACT,EAAE,EAAE,EAAEX,GAAG,CAAC;QACvF;OACH,CAAC;MAEF;MACAoB,SAAS,CAACjJ,iBAAiB,CAACuN,OAAO,CAAElE,OAAY,IAAI;QACjD,IAAIe,MAAM,GAAG,WAAWnB,SAAS,CAACf,OAAO,CAACM,EAAE,YAAYa,OAAO,CAACpE,SAAS,EAAE;QAC3E,IAAIgE,SAAS,CAACnJ,QAAQ,KAAKI,QAAQ,CAACH,SAAS,EAAE;UAC3CqK,MAAM,GAAG,GAAGnB,SAAS,CAACT,EAAE,EAAE;QAC9B;QACA,IAAIS,SAAS,CAACnJ,QAAQ,KAAKI,QAAQ,CAACD,IAAI,EAAE;UACtC,IAAI,IAAI,CAACU,WAAW,CAAC+H,SAAS,EAAE;YAC5B0B,MAAM,GAAG,WAAWnB,SAAS,CAACf,OAAO,CAACM,EAAE,YAAY,IAAI,CAAC3G,IAAI,CAAC2G,EAAE,EAAE;UACtE;QACJ;QAEA/E,OAAO,CAACC,GAAG,CAAC,eAAe0G,MAAM,EAAE,CAAC;QACpC,IAAI,CAAC5Q,WAAW,CAAC2U,cAAc,CAAC/D,MAAM,CAAC,CAAC9G,SAAS,CAAC;UAC9CC,IAAI,EAAGqJ,OAAY,IAAI;YACnBrL,YAAY,CAAC6I,MAAa,CAAC,GAAGwC,OAAO;YACrC,IAAI,CAACwB,sBAAsB,CAAC7M,YAAY,CAAC;UAC7C,CAAC;UACDwL,KAAK,EAAGlF,GAAG,IAAI;YACXpE,OAAO,CAACsJ,KAAK,CAAC,+CAA+C1D,OAAO,CAACb,EAAE,iBAAiBS,SAAS,CAACT,EAAE,EAAE,EAAEX,GAAG,CAAC;UAChH;SACH,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAuG,sBAAsBA,CAAC7M,YAAiB;IACpC,IAAI,CAACjK,SAAS,CAAC6L,GAAG,CAAC,IAAI,CAAC7L,SAAS,EAAE,CAAC+W,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MACxD,MAAMC,YAAY,GAAGjN,YAAY,CAAC+M,CAAC,CAAC/Q,WAAW,CAAC,EAAEU,aAAa,EAAEC,OAAO,IAAI,CAAC;MAC7E,MAAMuQ,YAAY,GAAGlN,YAAY,CAACgN,CAAC,CAAChR,WAAW,CAAC,EAAEU,aAAa,EAAEC,OAAO,IAAI,CAAC;MAC7E,OAAOuQ,YAAY,GAAGD,YAAY;IACtC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAChN,mBAAmB,CAAC+B,IAAI,CAAChC,YAAY,CAAC;IAC3C,IAAI,CAACH,eAAe,CAAC+B,GAAG,CAAC,IAAI,CAAC;EAClC;EAEQe,qBAAqBA,CAAA;IACzBxP,YAAY,CAAC,IAAI,CAAC0M,eAAe,EAAE;MAC/Bf,QAAQ,EAAE,IAAI,CAACA;KAClB,CAAC,CAACiD,SAAS,CAAC;MACTC,IAAI,EAAGnC,eAAe,IAAI;QACtBqC,OAAO,CAACC,GAAG,CAACtC,eAAe,CAAC;QAC5B,IAAIA,eAAe,EAAE;UACjBuG,UAAU,CAAC,MAAK;YACZ,IAAI,CAAC,IAAI,CAAC7P,UAAU,EAAE,IAAI,CAAC,IAAI,CAACtC,gBAAgB,EAAE;cAC9C,IAAI,CAAC8I,aAAa,CAAC,IAAI,CAACiB,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/C;UACJ,CAAC,EAAE,GAAG,CAAC;QAEX;MACJ;KACH,CAAC;EACN;EAEQmP,mBAAmBA,CAACrM,KAAU;IAElC;IACA,MAAMsM,gBAAgB,GAA8B,EAAE;IAEtDtM,KAAK,CAACkL,OAAO,CAAEtL,IAAS,IAAI;MACxBA,IAAI,CAAC2M,YAAY,CAACrB,OAAO,CAAEsB,WAAmB,IAAI;QAC9C;QACA,IAAI,CAACF,gBAAgB,CAACE,WAAW,CAAC,IAAIF,gBAAgB,CAACE,WAAW,CAAC,GAAG5M,IAAI,CAAChE,aAAa,EAAE;UACtF0Q,gBAAgB,CAACE,WAAW,CAAC,GAAG5M,IAAI,CAAChE,aAAa;QACtD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IAEF;EAEJ;EAEA6Q,aAAaA,CAAC7J,SAAiB;IAC3B,KAAK,IAAIgE,SAAS,IAAI,IAAI,CAAC3R,SAAS,EAAE,EAAE;MACpC;MACA,IAAI2R,SAAS,CAACf,OAAO,IAAIe,SAAS,CAACf,OAAO,CAACjD,SAAS,KAAKA,SAAS,EAAE;QAChE,OAAOgE,SAAS,CAACf,OAAO;MAC5B;MAEA;MACA,KAAK,IAAImB,OAAO,IAAIJ,SAAS,CAACjJ,iBAAiB,EAAE;QAC7C,IAAIqJ,OAAO,CAACpE,SAAS,KAAKA,SAAS,EAAE;UACjC,OAAOoE,OAAO;QAClB;MACJ;IACJ;IACA,OAAO,IAAI,CAAC,CAAC;EACjB;EAGA0F,0BAA0BA,CAAC9J,SAAiB;IACxC,KAAK,IAAIgE,SAAS,IAAI,IAAI,CAAC3R,SAAS,EAAE,EAAE;MACpC;MACA,IAAI2R,SAAS,CAACf,OAAO,IAAIe,SAAS,CAACf,OAAO,CAACjD,SAAS,KAAKA,SAAS,EAAE;QAChE,MAAM+J,QAAQ,GAAG/F,SAAS,CAACf,OAAO,CAACC,SAAS,GAAG,GAAG,GAAGc,SAAS,CAACf,OAAO,CAACyF,QAAQ;QAC/E,MAAMsB,QAAQ,GAAGhG,SAAS,CAACf,OAAO,CAACC,SAAS,CAAC+G,MAAM,CAAC,CAAC,CAAC,GAAGjG,SAAS,CAACf,OAAO,CAACyF,QAAQ,CAACuB,MAAM,CAAC,CAAC,CAAC;QAC7F,OAAO;UAAEF,QAAQ;UAAEC;QAAQ,CAAE;MACjC;MAEA;MACA,KAAK,IAAI5F,OAAO,IAAIJ,SAAS,CAACjJ,iBAAiB,EAAE;QAC7C,IAAIqJ,OAAO,CAACpE,SAAS,KAAKA,SAAS,EAAE;UACjC,MAAM+J,QAAQ,GAAG3F,OAAO,CAAClB,SAAS,GAAG,GAAG,GAAGkB,OAAO,CAACsE,QAAQ;UAC3D,MAAMsB,QAAQ,GAAG5F,OAAO,CAAClB,SAAS,CAAC+G,MAAM,CAAC,CAAC,CAAC,GAAG7F,OAAO,CAACsE,QAAQ,CAACuB,MAAM,CAAC,CAAC,CAAC;UACzE,OAAO;YAAEF,QAAQ;YAAEC;UAAQ,CAAE;QACjC;MACJ;IACJ;IACA,OAAO,IAAI,CAAC,CAAC;EACjB;EAEAxH,WAAWA,CAACiF,OAAe,EAAEyC,iBAAA,GAA6B,KAAK,EAAEtC,YAAA,GAAwB,KAAK;IAC1F,MAAMD,OAAO,GAAY;MACrBwC,QAAQ,EAAE,IAAI,CAACvN,IAAI,CAACoD,SAAS;MAC7ByH,OAAO,EAAEA,OAAO;MAChBlB,SAAS,EAAErY,eAAe,EAAE;MAC5Bkc,MAAM,EAAE,CAAC,IAAI,CAACxN,IAAI,CAACoD,SAAS,CAAC;MAC7BkK,iBAAiB,EAAEA,iBAAiB;MACpCtC,YAAY,EAAEA,YAAY;MAC1ByC,UAAU,EAAE;QAAE,CAAC,IAAI,CAACzN,IAAI,CAACoD,SAAS,GAAG;MAAI;KAE5C;IAEDxB,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7B,IAAI,CAAC;IACtB,IAAI,CAACrI,WAAW,CAAC+V,UAAU,CAAC,IAAI,CAACjZ,aAAa,EAAEsW,OAAO,EAAE,IAAI,CAAC/K,IAAI,CAAC;IACnE,IAAI,CAACrI,WAAW,CAACwL,kBAAkB,CAAC,IAAI,CAAC1O,aAAa,EAAE,IAAI,CAACuL,IAAI,CAACoD,SAAS,CAAC;EAChF;EAEAuK,OAAOA,CAACC,QAAgB;IACpB,MAAMC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;IACrH,MAAMC,aAAa,GAAGF,QAAQ,CAACtJ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEjE,WAAW,EAAE;IAC9D,OAAOuN,eAAe,CAACtN,QAAQ,CAAC,IAAIuN,aAAa,EAAE,CAAC;EACxD;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAAC1X,aAAa,GAAG,IAAI;EAC7B;EAEAwD,oBAAoBA,CAAA;IAEhB,MAAMmU,eAAe,GAAG,IAAI,CAAC/X,UAAU,EAAE,GAAG,4BAA4B,GAAG,iBAAiB;IAC5F,IAAI,CAAC4I,cAAc,CAACoP,oBAAoB,CAAC3M,GAAG,CAAC,IAAI,CAAC;IAClD,IAAI,CAACtC,WAAW,CAACkP,gBAAgB,CAAC,EAAE,CAAC;IACrCpI,UAAU,CAAC,MAAK;MACZ,MAAMqI,SAAS,GAAG1D,QAAQ,CAAC2D,cAAc,CAACJ,eAAe,CAAC;MAC1D,MAAMK,WAAW,GAAGF,SAAU,CAACG,qBAAqB,EAAE,CAACC,KAAK,GAAG,EAAE;MACjE,IAAI,CAACnW,cAAc,CAACoW,uBAAuB,CAAC,IAAI,CAACvP,aAAa,EAAElN,6BAA6B,EAAEsc,WAAW,CAACI,OAAO,EAAE,EAChH;QACIC,MAAM,EAAE,QAAQ;QAChBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE;OAChB,EAAEZ,eAAe,EAAGa,MAAW,IAAI;QAChCjN,OAAO,CAACC,GAAG,CAACgN,MAAM,CAAC;QACnB,IAAI,CAAClX,WAAW,CAACmX,cAAc,CAACxN,GAAG,CAAC,KAAK,CAAC;QAC1C,IAAI,CAACzC,cAAc,CAACkQ,mBAAmB,CAACzN,GAAG,CAAC,EAAE,CAAC;QAC/C,IAAI,CAACzC,cAAc,CAACoP,oBAAoB,CAAC3M,GAAG,CAAC,KAAK,CAAC;QACnD;QACA;QACA,IAAIuN,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;UACjCA,MAAM,CAACnD,OAAO,CAAEwD,IAAS,IAAI;YACzB,IAAI,CAACtJ,WAAW,CAACsJ,IAAI,CAACC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UAC3C,CAAC,CAAC;UACFrJ,UAAU,CAAC,MAAK;YACZ,IAAI,CAACC,cAAc,EAAE;UACzB,CAAC,EAAE,IAAI,CAAC;UACRnE,OAAO,CAACC,GAAG,CAACgN,MAAM,CAAC;UACnB;QACJ;MACJ,CAAC,EAAEV,SAAU,CAACG,qBAAqB,EAAE,CAAC7K,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC;IAClE,CAAC,EAAE,GAAG,CAAC;EAEX;EAGAtJ,OAAOA,CAACiH,KAAU;IACd,MAAMgO,KAAK,GAAG,CAAChO,KAAK,CAACiO,aAAa,IAAIjO,KAAK,CAACkO,aAAa,CAACD,aAAa,EAAED,KAAK;IAC9E,IAAIG,IAAI,GAAG,IAAI;IACf,KAAK,MAAML,IAAI,IAAIE,KAAK,EAAE;MACtB,IAAIF,IAAI,CAAC9J,IAAI,CAACoK,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAClCD,IAAI,GAAGL,IAAI,CAACO,SAAS,EAAE;QACvB,IAAI,CAACpW,iBAAiB,CAACkW,IAAI,EAAE,IAAI,CAAC;QAClC3N,OAAO,CAACC,GAAG,CAAC0N,IAAI,CAAC,CAAC,CAAC;MACvB;IACJ;EACJ;EAEAG,cAAcA,CAACC,SAAsC;IACjD;IACA,IAAI,CAAC/O,SAAS,GAAG,4CAA4C,CAAC,CAAC;IAE/D;IACA;;;;;EAKJ;EAEAwB,gBAAgBA,CAAA;IACZ,IAAI,CAACzK,WAAW,CAACiY,aAAa,CAAC,IAAI,CAAC5P,IAAI,CAACoD,SAAS,EAAE,QAAQ,CAAC;IAC7D,IAAI,CAACyM,sBAAsB,EAAE;IAC7B,IAAI,CAACC,qBAAqB,EAAE;IAC5B;IACA,IAAI,CAACrR,IAAI,CAACsG,GAAG,CAAC,IAAI,CAACpN,WAAW,CAACoY,sCAAsC,EAAE,CAACtO,SAAS,CAACuO,KAAK,IAAG;MACtF,IAAI,CAAC1Q,WAAW,GAAG0Q,KAAK;IAC5B,CAAC,CAAC,CAAC;EACP;EAEAF,qBAAqBA,CAAA;IACjB,IAAI,CAACnY,WAAW,CAACmY,qBAAqB,CAAC,IAAI,CAAC9P,IAAI,CAACoD,SAAS,EAAE9R,eAAe,EAAE,CAAC;EAClF;EAEAue,sBAAsBA,CAAA;IAClB,IAAI,CAAC7M,UAAU,GAAGiN,WAAW,CAAC,MAAK;MAC/B,IAAI,CAACH,qBAAqB,EAAE;IAChC,CAAC,EAAE,IAAI,CAACnY,WAAW,CAACuY,0BAA0B,GAAG,IAAI,CAAC;EAC1D;EAEAxX,YAAYA,CAACyX,MAAc;IACvB,MAAMC,WAAW,GAAG7e,SAAS,CAACkY,GAAG,EAAE;IACnC,OAAO,IAAI,CAACnK,WAAW,CAAC+Q,IAAI,CAACrQ,IAAI,IAAG;MAChC,IAAIA,IAAI,CAAC2G,EAAE,KAAKwJ,MAAM,EAAE;QACpB,MAAMG,cAAc,GAAGtQ,IAAI,CAACsQ,cAA2B;QACvD,MAAMC,cAAc,GAAGH,WAAW,CAAC/T,OAAO,GAAGiU,cAAc,CAACjU,OAAO;QACnE,OAAOkU,cAAc,IAAI,IAAI,CAAC5Y,WAAW,CAACuY,0BAA0B;MACxE;MACA,OAAO,KAAK;IAChB,CAAC,CAAC;EACN;EAEA5N,qBAAqBA,CAAA;IACjB,IAAI,CAAC7D,IAAI,CAACgI,IAAI,GAAG,IAAI,CAACrO,cAAc,CAACoY,UAAU,CAACC,IAAI,CAChDzf,SAAS,CAACkR,GAAG,IAAG;MACZ,IAAI,CAACvO,gBAAgB,GAAG,CAACuO,GAAG,CAACwO,MAAM;MACnC,IAAI,CAACxO,GAAG,CAACyO,OAAO,EAAE;QACd,OAAO5f,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACH,OAAOA,EAAE,CAAC,IAAI,CAAC;MACnB;IACJ,CAAC,CAAC,CACL,CAAC0Q,SAAS,CAAES,GAAG,IAAI;MAChB,IAAIA,GAAG,KAAK,CAAC,EAAE,CACf,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;QAClB,MAAM0O,UAAU,GAAG1O,GAAG;MAC1B;IACJ,CAAC,CAAC;EACN;EAEApM,kBAAkBA,CAAA;IACd,IAAI,IAAI,CAACnC,gBAAgB,IAAI,IAAI,CAAC0C,aAAa,EAAE;MAC7C,OAAO,KAAK;IAChB,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EAEAN,mBAAmBA,CAAA;IACf,IAAI,IAAI,CAACpC,gBAAgB,IAAI,CAAC,IAAI,CAAC0C,aAAa,EAAE;MAC9C,OAAO,KAAK;IAChB,CAAC,MAAM;MACH,OAAO,IAAI;IACf;EACJ;EAEA2B,aAAaA,CAAA;IACT,IAAI,CAAC3B,aAAa,GAAG,KAAK;EAC9B;EAEAwa,UAAUA,CAACC,QAA6B;IACpCA,QAAQ,CAACtN,KAAK,CAACC,MAAM,GAAG,MAAM,CAAC,CAAE;IACjCqN,QAAQ,CAACtN,KAAK,CAACC,MAAM,GAAGqN,QAAQ,CAACpN,YAAY,GAAG,IAAI,CAAC,CAAE;EAC3D;EAEA1K,sBAAsBA,CAAA;IAClB,IAAI,CAAC3D,eAAe,CAACiM,GAAG,CAAC,KAAK,CAAC;EACnC;EAEArI,aAAaA,CAAA;IACT,IAAI,CAACqK,kBAAkB,CAACC,aAAa,CAAChC,KAAK,GAAG,EAAE;IAChD;IACA,IAAI,CAACtH,kBAAkB,EAAE;EAC7B;EAEAoB,qBAAqBA,CAACU,WAAgB;IAClC,IAAI,CAACA,WAAW,EAAE;MACd;IACJ;IACA,IAAIA,WAAW,CAACgV,YAAY,EAAE;MAC1B,OAAOhV,WAAW,CAACgV,YAAY,CAAC,IAAI,CAAC/Q,IAAI,CAACoD,SAAS,CAAC;IACxD,CAAC,MAAM;MACH,OAAO,CAAC;IACZ;EACJ;EAAC,QAAA4N,CAAA,G;qBA5/BQ1S,aAAa;EAAA;EAAA,QAAA2S,EAAA,G;UAAb3S,aAAa;IAAA4S,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;qCAFX,CAACvf,aAAa,CAAC,GAAAkB,EAAA,CAAAue,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb7Bre,EAjED,CAAAG,UAAA,IAAA0e,8BAAA,OAmEC,IAAAC,yCAAA;QAnED9e,EAAA,CAAA+e,OAAA,OAAAC,4BAAA,UAmEC;QAnEMhf,EAAA,CAAAif,iBAAA,OAAW;QAuWlBjf,EAhSA,CAAAG,UAAA,IAAA+e,oCAAA,OAAoB,IAAAC,oCAAA,iCAAAnf,EAAA,CAAA0G,sBAAA,CA4Ga,IAAA0Y,oCAAA,kCAAApf,EAAA,CAAA0G,sBAAA,CAyBD,IAAA2Y,oCAAA,gCAAArf,EAAA,CAAA0G,sBAAA,CAmFG,KAAA4Y,qCAAA,gCAAAtf,EAAA,CAAA0G,sBAAA,CAUH,KAAA6Y,qCAAA,gCAAAvf,EAAA,CAAA0G,sBAAA,CA+CI,KAAA8Y,qCAAA,gCAAAxf,EAAA,CAAA0G,sBAAA,CAe0B;;;QAhS9D1G,EAAA,CAAAY,SAAA,GAyGC;QAzGDZ,EAAA,CAAAsC,aAAA,IAAAgc,GAAA,CAAArb,UAAA,YAyGC;;;mBDtHO1F,YAAY,EAAAkiB,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,gBAAA,EAAAJ,EAAA,CAAAK,SAAA,EAAAL,EAAA,CAAAM,QAAA,EACZphB,YAAY,EAAAqhB,EAAA,CAAAC,MAAA,EAAAC,EAAA,CAAAC,aAAA,EACZthB,YAAY,EAAAuhB,EAAA,CAAAC,OAAA,EACZphB,gBAAgB,EAAAqhB,EAAA,CAAAC,UAAA,EAChBjhB,eAAe,EAAAkhB,EAAA,CAAAC,SAAA,EACf9gB,mBAAmB,EACnBC,eAAe,EACfF,WAAW,EAAAghB,EAAA,CAAAC,KAAA,EACX7gB,aAAa,EACbC,aAAa,EACbN,4BAA4B;IAAAmhB,MAAA;IAAAjS,IAAA;MAAAkS,SAAA,EAKpB,CACRniB,OAAO,CAAC,aAAa,EAAE,CACnBD,UAAU,CAAC,kBAAkB,EAAE,CAACD,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAChE,CAAC;IACL;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}