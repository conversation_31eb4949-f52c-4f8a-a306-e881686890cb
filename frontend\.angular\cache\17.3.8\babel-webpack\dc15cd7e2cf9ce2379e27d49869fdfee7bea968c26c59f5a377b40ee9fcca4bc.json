{"ast": null, "code": "import { catchError, map, switchMap } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { of, throwError } from 'rxjs';\nimport * as moment from 'moment';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/calendar.service\";\nimport * as i6 from \"src/app/core/services/user.service\";\nimport * as i7 from \"src/app/core/services/toast.service\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/router\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"primeng/scrollpanel\";\nimport * as i13 from \"./weekly-availability-calendar/weekly-availability-calendar.component\";\nimport * as i14 from \"./overlay-panel-block/overlay-panel-block.component\";\nimport * as i15 from \"./overlay-panel-block/awaiting-approval-notification/awaiting-approval-notification.component\";\nimport * as i16 from \"./overlay-panel-block/recurring-lessons-block/recurring-lessons-block.component\";\nimport * as i17 from \"./overlay-panel-block/teacher-days-off-panel/teacher-days-off-panel.component\";\nconst _c0 = [\"calendarWrapper\"];\nconst _c1 = [\"calendarContainer\"];\nconst CalendarComponent_Defer_4_DepsFn = () => [i8.NgControlStatus, i8.NgModel, i9.NgClass, i9.NgForOf, i9.NgIf, i10.RouterLink, i11.Dropdown, i12.ScrollPanel, i13.WeeklyAvailabilityCalendarComponent, i14.OverlayPanelBlockComponent, i15.AwaitingApprovalNotificationComponent, i16.RecurringLessonsBlockComponent, i17.TeacherDaysOffPanelComponent];\nconst _c2 = a0 => ({\n  \"pointer-events-none opacity-40\": a0\n});\nconst _c3 = () => ({\n  width: \"100%\"\n});\nfunction CalendarComponent_Defer_2_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-awaiting-approval-notification\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const request_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"index\", i_r4 + 1)(\"lesson\", request_r3.lesson)(\"lessonId\", request_r3.id)(\"avatar\", request_r3.avatar)(\"requestedName\", request_r3.requestedName)(\"date\", request_r3.date)(\"time\", request_r3.time)(\"duration\", ctx_r1.generalService.convertHoursToMinutesWithSuffix(request_r3.duration));\n  }\n}\nfunction CalendarComponent_Defer_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"button\", 13)(3, \"div\", 14);\n    i0.ɵɵelement(4, \"img\", 15);\n    i0.ɵɵelementStart(5, \"span\", 16);\n    i0.ɵɵtext(6, \"Connect with \");\n    i0.ɵɵelementStart(7, \"b\");\n    i0.ɵɵtext(8, \"Google Calendar\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(9, \"div\", 17);\n    i0.ɵɵtext(10, \"Availability of \");\n    i0.ɵɵelementStart(11, \"p-dropdown\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CalendarComponent_Defer_2_div_1_Template_p_dropdown_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedTeacher, $event) || (ctx_r1.selectedTeacher = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function CalendarComponent_Defer_2_div_1_Template_p_dropdown_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTeacherChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 19)(13, \"div\", 20)(14, \"div\", 21)(15, \"app-overlay-panel-block\", 22)(16, \"div\", 23);\n    i0.ɵɵtemplate(17, CalendarComponent_Defer_2_div_1_div_17_Template, 2, 8, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 25);\n    i0.ɵɵelement(19, \"img\", 26);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"options\", ctx_r1.availableTeachers);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedTeacher);\n    i0.ɵɵproperty(\"optionLabel\", ctx_r1.getTeacherLabel);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"notificationsNumber\", ctx_r1.notificationsNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pendingRequests);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c2, !ctx_r1.showBookingSystemLink));\n  }\n}\nfunction CalendarComponent_Defer_2_div_2_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-awaiting-approval-notification\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const request_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"lesson\", request_r5.lesson)(\"lessonId\", request_r5.id)(\"index\", i_r6 + 1)(\"date\", request_r5.date)(\"time\", request_r5.time)(\"avatar\", request_r5.avatar)(\"requestedName\", request_r5.requestedName)(\"duration\", ctx_r1.generalService.convertHoursToMinutesWithSuffix(request_r5.duration));\n  }\n}\nfunction CalendarComponent_Defer_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"button\", 13)(4, \"div\", 14);\n    i0.ɵɵelement(5, \"img\", 31);\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7, \"Connect with \");\n    i0.ɵɵelementStart(8, \"b\");\n    i0.ɵɵtext(9, \"Google Calendar\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"div\", 30)(12, \"app-overlay-panel-block\", 33);\n    i0.ɵɵelement(13, \"app-teacher-days-off-panel\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"div\", 30)(16, \"app-overlay-panel-block\", 34);\n    i0.ɵɵelement(17, \"app-recurring-lessons-block\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"div\", 30)(20, \"app-overlay-panel-block\", 36)(21, \"p-scrollPanel\", 37, 1);\n    i0.ɵɵelement(23, \"app-availability\", 38);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 21)(25, \"div\", 30)(26, \"app-overlay-panel-block\", 39)(27, \"div\", 23);\n    i0.ɵɵtemplate(28, CalendarComponent_Defer_2_div_2_div_28_Template, 2, 9, \"div\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"div\", 25);\n    i0.ɵɵelement(30, \"img\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"hidden\", true)(\"role\", ctx_r1.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"role\", ctx_r1.role);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classrooms\", ctx_r1.classrooms);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"panelWidth\", ctx_r1.isTabletOrMobile ? \"100%\" : \"26rem\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(18, _c3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHours\", false)(\"isDialog\", true)(\"user\", ctx_r1.user);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"notificationsNumber\", ctx_r1.notificationsNumber)(\"panelWidth\", ctx_r1.isTabletOrMobile ? \"90%\" : \"22rem\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pendingRequests);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c2, !ctx_r1.showBookingSystemLink));\n  }\n}\nfunction CalendarComponent_Defer_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 41, 2)(3, \"app-weekly-availability-calendar\", 42);\n    i0.ɵɵlistener(\"availableTeachersChanged\", function CalendarComponent_Defer_2_ng_container_3_Template_app_weekly_availability_calendar_availableTeachersChanged_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAvailableTeacherChange($event));\n    })(\"calendarRangeDatesChanged\", function CalendarComponent_Defer_2_ng_container_3_Template_app_weekly_availability_calendar_calendarRangeDatesChanged_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCalendarRangeDatesChanged($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 43)(5, \"div\", 44)(6, \"div\", 14);\n    i0.ɵɵelement(7, \"div\", 45);\n    i0.ɵɵtext(8, \" Availability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 14)(10, \"div\", 46);\n    i0.ɵɵelement(11, \"img\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Arranged \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 14)(14, \"div\", 48);\n    i0.ɵɵelement(15, \"img\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Completed \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"div\", 50);\n    i0.ɵɵelement(19, \"img\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Trial Arranged \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 51);\n    i0.ɵɵelement(23, \"img\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Trial Completed \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 14)(26, \"div\", 52);\n    i0.ɵɵelement(27, \"img\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Requested \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"calendarHeight\", ctx_r1.containerHeight)(\"businessHours\", ctx_r1.businessHours)(\"dayOffEvents\", ctx_r1.dayOffEvents)(\"lessonEvents\", ctx_r1.lessonEvents)(\"selectedTeacher\", ctx_r1.selectedTeacher)(\"isTabletOrMobile\", ctx_r1.isTabletOrMobile);\n  }\n}\nfunction CalendarComponent_Defer_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-block-viewer\", 5);\n    i0.ɵɵtemplate(1, CalendarComponent_Defer_2_div_1_Template, 20, 10, \"div\", 6)(2, CalendarComponent_Defer_2_div_2_Template, 31, 21, \"div\", 7)(3, CalendarComponent_Defer_2_ng_container_3_Template, 29, 6, \"ng-container\", 8);\n    i0.ɵɵelementStart(4, \"div\", 9);\n    i0.ɵɵelement(5, \"div\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-2\")(\"headerTextClass\", \"font-xl font-semibold justify-content-center\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == \"Student\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == \"Teacher\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCalendar);\n  }\n}\nfunction CalendarComponent_DeferPlaceholder_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class CalendarComponent {\n  constructor(layoutService, authService, generalService, classroomService, calendarService, userService, toastService, cdr) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.calendarService = calendarService;\n    this.userService = userService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.subs = new SubSink();\n    this.calendarWrapper = {};\n    this.role = \"\";\n    this.teacher = [];\n    this.user = {};\n    this.selectedTeacher = null;\n    this.availableTeachers = [];\n    this.containerHeight = '';\n    this.businessHours = [];\n    this.dayOffEvents = [];\n    this.lessonEvents = [];\n    this.filteredLessonEvents = [];\n    this.showBookingSystemLink = false;\n    this.classrooms = [];\n    this.showCalendar = false;\n    this.calendarStartDate = new Date();\n    this.calendarEndDate = new Date();\n    this.UserRoles = UserRole;\n    this.pendingRequests = [];\n    this.lessonStatus = LessonStatus;\n    this.isTabletOrMobile = false;\n  }\n  ngOnInit() {\n    this.teacher = [];\n    this.role = this.authService.getLoggedInUser().role;\n    this.user = this.authService.getLoggedInUser();\n    this.notificationsNumber = 0;\n    this.subs.sink = this.generalService.deviceKind.pipe(switchMap(res => {\n      this.isTabletOrMobile = !res.w992up;\n      if (!res.w1024up) {\n        return of(0);\n      } else {\n        return this.layoutService.sideMenuHeight;\n      }\n    })).subscribe(res => {\n      if (res === 0) {\n        this.containerHeight = '100vh';\n      } else if (res !== 0) {\n        const menuHeight = res;\n        this.calendarWrapper.nativeElement.style.height = res + 'px';\n        this.containerHeight = menuHeight - 110 + 'px';\n        this.cdr.detectChanges();\n      }\n    });\n    this.fetchClassrooms();\n    this.initUpdateListener();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  getTeacherAvailability(teacher) {\n    if (!this.createAvailabilityRequestObject()) {\n      return of(null);\n    }\n    return this.calendarService.retrieveCalendarAvailability(this.createAvailabilityRequestObject()).pipe(map(res => {\n      const daysOff = res.daysOff;\n      // Set business hours and select constraint\n      this.businessHours = this.calendarService.getBusinessHours(res.calendarAvailability);\n      // Create events for each day off\n      this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\n      return null;\n    }), catchError(error => {\n      if (error.status === 400) {\n        // Handle the 400 error here\n        console.log('Bad Request - Invalid input');\n      }\n      return throwError(error);\n    }));\n  }\n  filterLessonsByTeacherId(lessons, teacherId) {\n    return lessons.filter(lesson => lesson.teacher.id === teacherId);\n  }\n  onTeacherChange(event) {\n    if (!this.selectedTeacher) {\n      return;\n    }\n    console.log('Selected teacher:', this.selectedTeacher);\n    // Do something else here\n    this.businessHours = [];\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).pipe(switchMap(teacherAvailability => {\n      // Use teacherAvailability to make another request\n      return this.classroomService.getAvailableHoursWithTeacher(this.selectedTeacher.aspUserId);\n    })).subscribe(availableHours => {\n      this.filteredLessonEvents = this.lessonEvents;\n      this.calendarService.setTeacherChangeListener(true);\n    }));\n  }\n  onAvailableTeacherChange(event) {\n    console.log(event);\n    this.teacher = event;\n  }\n  onCalendarRangeDatesChanged(event) {\n    this.businessHours = [];\n    console.log(event);\n    this.calendarStartDate = event.fromDate;\n    this.calendarEndDate = event.toDate;\n    let userId = null;\n    if (this.role === UserRole.STUDENT) {\n      userId = this.selectedTeacher.aspUserId;\n    } else {\n      userId = this.user.aspUserId;\n    }\n    event.userId = userId;\n    if (!userId) {\n      return;\n    }\n    const headers = new HttpHeaders({\n      'X-Bypass-Error-Interceptor': 'true'\n    });\n    this.subs.sink = this.calendarService.retrieveCalendarAvailability(event, headers).pipe(catchError(error => {\n      // Handle the error here\n      console.error('An error occurred:', error);\n      if (error.status === 400) {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Check if you have set your availability, if the problem persists contact us.'\n        });\n      }\n      // You can also throw a new error if needed\n      return throwError('Something went wrong');\n    })).subscribe(res => {\n      this.businessHours = this.calendarService.getBusinessHours(res.calendarAvailability);\n      this.dayOffEvents = this.calendarService.createDayOffEvents(res.daysOff);\n    });\n  }\n  getTeacherLabel(teacher) {\n    return `${teacher.firstName} ${teacher.lastName}`;\n  }\n  /**\n   * Creates an availability request object.\n   * @returns The availability request object.\n   */\n  createAvailabilityRequestObject() {\n    // Determine the ASP user ID based on the role\n    const aspUserId = this.role === UserRole.STUDENT ? this.selectedTeacher?.aspUserId ?? null : this.user?.aspUserId ?? null;\n    // If the ASP user ID is not available, return null\n    if (!aspUserId) {\n      return null;\n    }\n    // Create and return the availability request object\n    return {\n      fromDate: this.calendarStartDate,\n      toDate: this.calendarEndDate,\n      userId: aspUserId\n    };\n  }\n  getPendingLessons(classroom) {\n    return this.classroomService.gePendingLessons(classroom.id).pipe(switchMap(res => {\n      return of(res);\n    }), catchError(error => {\n      console.error('Error getting pending lessons:', error);\n      // Handle the error or rethrow it if necessary\n      return throwError(error);\n    }));\n  }\n  fetchClassrooms() {\n    this.subs.add(this.classroomService.getLMSUserClassrooms(this.user.id).pipe(switchMap(classrooms => {\n      this.pendingRequests = [];\n      this.lessonEvents = [];\n      const availableTeachers = [];\n      this.notificationsNumber = 0;\n      classrooms.forEach(classroom => {\n        this.fetchPendingLessons(classroom, this.pendingRequests);\n        availableTeachers.push(classroom.teacher);\n        classroom.lessons.forEach(lesson => {\n          if (lesson.status.toLowerCase().includes(LessonStatus.CANCELED.toLowerCase()) || lesson.status.toLowerCase() === LessonStatus.NO_SHOW.toLowerCase()) {\n            return;\n          }\n          const start = new Date(lesson.startingDate);\n          const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\n          const teacherName = this.generalService.getShortFullName(classroom.teacher);\n          this.lessonEvents.push({\n            id: lesson.id,\n            title: teacherName,\n            start: lesson.startingDate,\n            end: end,\n            allDay: false,\n            backgroundColor: '#378006',\n            borderColor: '#378006',\n            textColor: '#ffffff',\n            extendedProps: {\n              type: 'lesson',\n              lesson: lesson,\n              classroom: classroom\n            },\n            editable: false\n          });\n        });\n      });\n      const uniqueTeachers = availableTeachers.filter((value, index, self) => {\n        return self.map(x => x.id).indexOf(value.id) === index;\n      });\n      this.classrooms = classrooms;\n      this.availableTeachers = uniqueTeachers;\n      this.selectedTeacher = this.availableTeachers[0];\n      this.showCalendar = true;\n      return of(classrooms);\n    }), switchMap(classrooms => {\n      if (this.role === UserRole.STUDENT) {\n        return this.classroomService.getAvailableHoursWithTeacher(this.selectedTeacher.aspUserId);\n      } else {\n        return of(classrooms);\n      }\n    })).subscribe(res => {\n      if (this.role === UserRole.STUDENT) {\n        this.showBookingSystemLink = res;\n      } else {\n        this.showBookingSystemLink = this.classroomService.filterAvailableClassrooms(res).length > 0;\n      }\n    }));\n  }\n  fetchPendingLessons(classroom, pendingRequests) {\n    this.subs.add(this.getPendingLessons(classroom).subscribe(response => {\n      if (response.length === 0) return;\n      response.forEach(pendingLesson => {\n        let avatar = '';\n        console.log(pendingLesson);\n        if (pendingLesson.result === 'Pending') {\n          let studentRequestedLesson = classroom.classroomStudents.find(student => student.aspUserId === pendingLesson.requestedByAspUserId);\n          console.log(pendingLesson.duration);\n          if (studentRequestedLesson) {\n            avatar = this.role === UserRole.STUDENT ? classroom.teacher.avatarUrl : studentRequestedLesson.avatarUrl;\n          }\n          let name = this.role === UserRole.STUDENT ? `${classroom.teacher.firstName} ${classroom.teacher.lastName}` : pendingLesson.requestedByName;\n          pendingRequests.push({\n            id: pendingLesson.id,\n            lesson: pendingLesson,\n            requestedName: name,\n            avatar: avatar,\n            date: moment(pendingLesson.startingDate).format('DD/MM/YYYY'),\n            time: moment(pendingLesson.startingDate).format('HH:mm'),\n            duration: pendingLesson.duration\n          });\n          this.notificationsNumber++;\n        }\n      });\n    }, error => {\n      console.log(error);\n    }));\n  }\n  initUpdateListener() {\n    this.subs.add(this.calendarService.updateListener.subscribe(res => {\n      console.log(res);\n      if (res) {\n        this.onTeacherChange('');\n        this.fetchClassrooms();\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function CalendarComponent_Factory(t) {\n    return new (t || CalendarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i5.CalendarService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CalendarComponent,\n    selectors: [[\"app-calendar\"]],\n    viewQuery: function CalendarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarWrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarContainer = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 1,\n    consts: [[\"calendarWrapper\", \"\"], [\"scrollPanel\", \"\"], [\"calendarContainer\", \"\"], [1, \"classroom-cards-space\", \"py-8\"], [3, \"wrapperScrollHeight\"], [\"header\", \"Calendar\", \"headerBackgroundImage\", \"/assets/images/dashboard/calendar-banner.svg\", \"blockClass\", \"border-radius-bottom-10\", \"containerClass\", \"bg-white px-3 py-2 border-round-2xl \", 3, \"headerClass\", \"headerTextClass\"], [\"class\", \"grid justify-content-center lg:justify-content-end font-sm lg:h-3rem\", 4, \"ngIf\"], [\"class\", \"grid justify-content-end align-items-center gap-2 font-sm h-2rem\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"grid\"], [1, \"col\"], [1, \"grid\", \"justify-content-center\", \"lg:justify-content-end\", \"font-sm\", \"lg:h-3rem\"], [1, \"col\", \"pb-1\", \"col-fixed\", 3, \"hidden\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-rounded\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-1\", \"btn-gradient\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"src\", \"/assets/icons/google-calendar.svg\", \"height\", \"22\"], [1, \"font-sm\"], [1, \"col-fixed\", \"flex\", \"text-center\", \"pb-1\", \"font-sm\", \"align-items-center\", \"justify-content-center\"], [\"placeholder\", \"Select Teacher \", 1, \"inline-block\", \"ml-1\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"optionLabel\"], [1, \"col-fixed\", \"text-right\", \"pb-1\", \"align-items-center\"], [1, \"grid\", \"grid-nogutter\", \"gap-3\", \"left-grid\", \"awaitingApproval\", \"align-items-center\"], [1, \"col-fixed\", \"text-right\", \"pb-1\"], [\"selector\", \"awaitingApproval\", \"buttonIcon\", \"awaiting-approval\", \"buttonText\", \"Requested Lessons\", \"headIcon\", \"awaiting-approval-white\", \"headText\", \"Requested Lessons\", \"headBackgroundImage\", \"/assets/images/dashboard/awaiting-aproval-bg.png\", 3, \"role\", \"notificationsNumber\"], [1, \"overflow-y-scroll\", \"px-2\", 2, \"max-height\", \"300px\"], [4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/dashboard/calendar/booking-system\", 1, \"col-fixed\", \"add-icon\", \"cursor-pointer\", 3, \"ngClass\"], [\"src\", \"/assets/icons/plus-icon-svg-cropped.svg\", \"alt\", \"\"], [3, \"index\", \"lesson\", \"lessonId\", \"avatar\", \"requestedName\", \"date\", \"time\", \"duration\"], [1, \"grid\", \"justify-content-end\", \"align-items-center\", \"gap-2\", \"font-sm\", \"h-2rem\"], [1, \"col-fixed\", \"text-right\", \"pb-1\", 3, \"hidden\"], [1, \"grid\", \"left-grid\", \"awaitingApproval\"], [\"src\", \"/assets/icons/google-calendar.svg\", \"height\", \"16\"], [1, \"col-fixed\", \"text-right\", \"pb-1\", \"p-0\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Days off\", \"headText\", \"Add Days-Off for...\", \"headBackgroundImage\", \"/assets/images/dashboard/awaiting-aproval-bg.png\", 3, \"hidden\", \"role\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Reccurring Lessons\", \"headText\", \"Reccurring Lessons\", \"headBackgroundImage\", \"/assets/images/dashboard/calendar/recurring-bg.png\", 3, \"role\"], [3, \"classrooms\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Availability\", \"headText\", \"Availability\", \"headBackgroundImage\", \"/assets/images/dashboard/calendar/availability-dialog-bg.png\", 3, \"role\", \"panelWidth\"], [\"styleClass\", \"custombar1 h-20rem lg:max-h-30rem lg:min-h-22rem\"], [3, \"showHours\", \"isDialog\", \"user\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Lesson Requests\", \"headIcon\", \"awaiting-approval-white\", \"headText\", \"Lesson Requests\", \"headBackgroundImage\", \"/assets/images/dashboard/awaiting-aproval-bg.png\", 3, \"role\", \"notificationsNumber\", \"panelWidth\"], [3, \"role\", \"lesson\", \"lessonId\", \"index\", \"date\", \"time\", \"avatar\", \"requestedName\", \"duration\"], [1, \"relative\", \"mt-2\"], [3, \"availableTeachersChanged\", \"calendarRangeDatesChanged\", \"calendarHeight\", \"businessHours\", \"dayOffEvents\", \"lessonEvents\", \"selectedTeacher\", \"isTabletOrMobile\"], [1, \"border-round-xl\", \"p-1\", \"font-xs\"], [1, \"p-2\", \"flex\", \"flex-wrap\", \"gap-2\", \"flex-row\", \"align-items-start\", \"sm:align-items-center\", \"justify-content-start\", \"sm:justify-content-between\"], [1, \"circle-legend\", \"availability-bg-color\"], [1, \"circle-legend\", \"arranged-gradient-lesson-bg\"], [\"src\", \"/assets/icons/lessons/arranged.svg\", \"width\", \"12\"], [1, \"circle-legend\", \"completed-gradient-lesson-bg\"], [\"src\", \"/assets/icons/lessons/completed.svg\", \"width\", \"12\"], [1, \"circle-legend\", \"trial-arranged-gradient-lesson-bg\"], [1, \"circle-legend\", \"trial-completed-gradient-lesson-bg\"], [1, \"circle-legend\", \"requested-gradient-lesson-bg\"], [\"src\", \"/assets/icons/lessons/requested-white.svg\", \"width\", \"12\"]],\n    template: function CalendarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", null, 0);\n        i0.ɵɵtemplate(2, CalendarComponent_Defer_2_Template, 6, 5)(3, CalendarComponent_DeferPlaceholder_3_Template, 2, 0);\n        i0.ɵɵdefer(4, 2, CalendarComponent_Defer_4_DepsFn, null, 3);\n        i0.ɵɵdeferOnViewport(0, -1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 3);\n        i0.ɵɵelement(7, \"app-lessons-history\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"wrapperScrollHeight\", ctx.containerHeight);\n      }\n    },\n    styles: [\"p-dropdown .p-dropdown {\\n  width: 100%;\\n  background-image: linear-gradient(to bottom, #ffffff, rgba(180, 180, 250, 0.2));\\n  border: none;\\n}\\np-dropdown .p-dropdown .p-inputtext {\\n  padding: 0.3rem 1rem;\\n  font-size: 0.85rem;\\n  font-family: \\\"Proxima Nova Regular\\\";\\n}\\np-dropdown .p-dropdown-panel {\\n  box-shadow: none;\\n}\\np-dropdown .p-dropdown-panel .p-dropdown-items {\\n  background-image: linear-gradient(to bottom, rgba(180, 180, 250, 0.2), rgba(132, 146, 247, 0.2), rgba(56, 115, 244, 0.2));\\n  color: #2E3D90;\\n}\\np-dropdown .p-dropdown:not(.p-disabled).p-focus {\\n  border: 0;\\n  box-shadow: none;\\n}\\n\\n.btn-gradient {\\n  background: transparent;\\n  background-image: linear-gradient(to bottom, #ffffff, rgba(180, 180, 250, 0.2));\\n  color: #2D2A4B;\\n}\\n\\n.left-grid .add-icon {\\n  padding-top: 0.4rem;\\n}\\n\\napp-overlay-panel-block {\\n  padding-bottom: 0.4rem;\\n}\\n\\n.circle-legend {\\n  height: 1.3rem;\\n  width: 1.3rem;\\n  border-radius: 200px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.circle-legend.availability-bg-color {\\n  background: #eaedfb;\\n}\\n.circle-legend.days-off-bg-color {\\n  background: linear-gradient(#8497ff 0%, #2e3d90 100%);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["catchError", "map", "switchMap", "SubSink", "UserRole", "of", "throwError", "moment", "LessonStatus", "HttpHeaders", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "i_r4", "request_r3", "lesson", "id", "avatar", "requested<PERSON><PERSON>", "date", "time", "ctx_r1", "generalService", "convertHoursToMinutesWithSuffix", "duration", "ɵɵtext", "ɵɵtwoWayListener", "CalendarComponent_Defer_2_div_1_Template_p_dropdown_ngModelChange_11_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵresetView", "ɵɵlistener", "onTeacher<PERSON><PERSON>e", "ɵɵtemplate", "CalendarComponent_Defer_2_div_1_div_17_Template", "availableTeachers", "ɵɵtwoWayProperty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role", "notificationsNumber", "pendingRequests", "ɵɵpureFunction1", "_c2", "showBookingSystemLink", "request_r5", "i_r6", "CalendarComponent_Defer_2_div_2_div_28_Template", "classrooms", "isTabletOrMobile", "ɵɵstyleMap", "ɵɵpureFunction0", "_c3", "user", "ɵɵelementContainerStart", "CalendarComponent_Defer_2_ng_container_3_Template_app_weekly_availability_calendar_availableTeachersChanged_3_listener", "_r7", "onAvailableTeacherChange", "CalendarComponent_Defer_2_ng_container_3_Template_app_weekly_availability_calendar_calendarRangeDatesChanged_3_listener", "onCalendarRangeDatesChanged", "containerHeight", "businessHours", "dayOffEvents", "lessonEvents", "CalendarComponent_Defer_2_div_1_Template", "CalendarComponent_Defer_2_div_2_Template", "CalendarComponent_Defer_2_ng_container_3_Template", "showCalendar", "CalendarComponent", "constructor", "layoutService", "authService", "classroomService", "calendarService", "userService", "toastService", "cdr", "subs", "calendarWrapper", "teacher", "filteredLessonEvents", "calendarStartDate", "Date", "calendarEndDate", "UserRoles", "lessonStatus", "ngOnInit", "getLoggedInUser", "sink", "deviceKind", "pipe", "res", "w992up", "w1024up", "sideMenuHeight", "subscribe", "menuHeight", "nativeElement", "style", "height", "detectChanges", "fetchClassrooms", "initUpdateListener", "ngOnDestroy", "unsubscribe", "getTeacherAvailability", "createAvailabilityRequestObject", "retrieveCalendarAvailability", "daysOff", "getBusinessHours", "calendarAvailability", "createDayOffEvents", "error", "status", "console", "log", "filterLessonsByTeacherId", "lessons", "teacherId", "filter", "event", "add", "teacherAvailability", "getAvailableHoursWithTeacher", "aspUserId", "availableHours", "setTeacherChangeListener", "fromDate", "toDate", "userId", "STUDENT", "headers", "setShowToastmessage", "severity", "summary", "detail", "firstName", "lastName", "getPendingLessons", "classroom", "gePendingLessons", "getLMSUserClassrooms", "for<PERSON>ach", "fetchPendingLessons", "push", "toLowerCase", "includes", "CANCELED", "NO_SHOW", "start", "startingDate", "end", "getTime", "<PERSON><PERSON><PERSON>", "getShortFullName", "title", "allDay", "backgroundColor", "borderColor", "textColor", "extendedProps", "type", "editable", "uniqueTeachers", "value", "index", "self", "x", "indexOf", "filterAvailableClassrooms", "length", "response", "<PERSON><PERSON><PERSON><PERSON>", "result", "studentRequestedLesson", "classroomStudents", "find", "student", "requestedByAspUserId", "avatarUrl", "name", "requestedByName", "format", "updateListener", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "i3", "GeneralService", "i4", "ClassroomService", "i5", "CalendarService", "i6", "UserService", "i7", "ToastService", "ChangeDetectorRef", "_2", "selectors", "viewQuery", "CalendarComponent_Query", "rf", "ctx", "CalendarComponent_Defer_2_Template", "CalendarComponent_DeferPlaceholder_3_Template", "ɵɵdefer", "CalendarComponent_Defer_4_DepsFn", "ɵɵdeferOnViewport"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\calendar.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\calendar.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, ViewEncapsulation, AfterViewInit, AfterContentChecked, AfterContentInit, ChangeDetectorRef, OnDestroy } from '@angular/core';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { catchError, map, switchMap} from 'rxjs/operators';\r\nimport { SubSink } from 'subsink';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { Observable, of, throwError } from 'rxjs';\r\nimport * as moment from 'moment';\r\nimport { Lesson, LessonStatus } from 'src/app/core/models/lesson.model';\r\nimport { HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\n\r\n@Component({\r\n  selector: 'app-calendar',\r\n  templateUrl: './calendar.component.html',\r\n  styleUrls: ['./calendar.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class CalendarComponent implements OnInit, OnDestroy {\r\n  private subs = new SubSink();\r\n  @ViewChild('calendarWrapper', { static: true }) calendarWrapper: ElementRef = {} as ElementRef;\r\n  @ViewChild('calendarContainer', { static: true }) calendarContainer!: ElementRef<HTMLDivElement>;\r\n\r\n  role: string = \"\"\r\n  teacher: any[] = [];\r\n  user = {} as User;\r\n  selectedTeacher: any = null;\r\n  availableTeachers: any[] = [];\r\n  containerHeight = '';\r\n  businessHours: any[] = [];\r\n  dayOffEvents: any[] = [];\r\n  lessonEvents: any[] = [];\r\n  filteredLessonEvents: any[] = [];\r\n  showBookingSystemLink = false;\r\n  classrooms: any[] = [];\r\n  showCalendar = false;\r\n  calendarStartDate = new Date();\r\n  calendarEndDate = new Date();\r\n  UserRoles = UserRole;\r\n  pendingRequests: any[] = [];\r\n  notificationsNumber: any;\r\n  lessonStatus = LessonStatus;\r\n  isTabletOrMobile = false;\r\n  \r\n  constructor(\r\n    private layoutService: LayoutService,\r\n    private authService: AuthService,\r\n    public generalService: GeneralService,\r\n    private classroomService: ClassroomService,\r\n    private calendarService: CalendarService,\r\n    private userService: UserService,\r\n    public toastService: ToastService,\r\n    private cdr: ChangeDetectorRef,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.teacher = [];\r\n    this.role = this.authService.getLoggedInUser().role;\r\n    this.user = this.authService.getLoggedInUser();\r\n\r\n    this.notificationsNumber = 0;\r\n    \r\n    this.subs.sink = this.generalService.deviceKind.pipe(\r\n      switchMap(res => {\r\n        this.isTabletOrMobile = !res.w992up;\r\n        if (!res.w1024up) {\r\n          return of(0);\r\n        } else {\r\n          return this.layoutService.sideMenuHeight;\r\n        }\r\n      })\r\n    ).subscribe((res) => {\r\n      if (res === 0) {\r\n        this.containerHeight =  '100vh';\r\n      } else if (res !== 0) {\r\n        const menuHeight = res;\r\n        this.calendarWrapper.nativeElement.style.height = res + 'px';\r\n        this.containerHeight = (menuHeight - 110) + 'px';\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n\r\n    this.fetchClassrooms();\r\n\r\n    this.initUpdateListener();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  private getTeacherAvailability(teacher: any): Observable<any> {\r\n    if (!this.createAvailabilityRequestObject()) {\r\n      return of(null);\r\n    }\r\n    return this.calendarService.retrieveCalendarAvailability(this.createAvailabilityRequestObject()!).pipe(\r\n      map((res: any) => {\r\n        const daysOff = res.daysOff;\r\n\r\n        // Set business hours and select constraint\r\n        this.businessHours = this.calendarService.getBusinessHours(res.calendarAvailability);\r\n\r\n        // Create events for each day off\r\n        this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\r\n\r\n        return null;\r\n      }),\r\n      catchError((error: any) => {\r\n        if (error.status === 400) {\r\n          // Handle the 400 error here\r\n          console.log('Bad Request - Invalid input');\r\n        }\r\n    \r\n        return throwError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  filterLessonsByTeacherId(lessons: any[], teacherId: number): any[] {\r\n    return lessons.filter((lesson: any) => lesson.teacher.id === teacherId);\r\n  }\r\n\r\n  onTeacherChange(event: any) {\r\n    if (!this.selectedTeacher) {\r\n      return;\r\n    }\r\n    console.log('Selected teacher:', this.selectedTeacher);\r\n    // Do something else here\r\n    this.businessHours = [];\r\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).pipe(\r\n      switchMap((teacherAvailability) => {\r\n        // Use teacherAvailability to make another request\r\n        return this.classroomService.getAvailableHoursWithTeacher(this.selectedTeacher.aspUserId);\r\n      })\r\n    ).subscribe((availableHours) => {\r\n      this.filteredLessonEvents = this.lessonEvents;\r\n      this.calendarService.setTeacherChangeListener(true);\r\n    }));\r\n\r\n  }\r\n\r\n  onAvailableTeacherChange(event: any) {\r\n\r\n    console.log(event);\r\n    this.teacher = event\r\n  }\r\n\r\n  onCalendarRangeDatesChanged(event: any) {\r\n    this.businessHours = [];\r\n    console.log(event);\r\n    this.calendarStartDate = event.fromDate;\r\n    this.calendarEndDate = event.toDate;\r\n    let userId = null;\r\n    if (this.role === UserRole.STUDENT) {\r\n      userId = this.selectedTeacher.aspUserId;\r\n    } else {\r\n      userId = this.user.aspUserId as string;\r\n    }\r\n    event.userId = userId;\r\n\r\n    if (!userId) {\r\n      return;\r\n    }\r\n\r\n    const headers = new HttpHeaders({\r\n      'X-Bypass-Error-Interceptor': 'true'\r\n    });\r\n    this.subs.sink = this.calendarService.retrieveCalendarAvailability(event, headers).pipe(\r\n      catchError((error) => {\r\n        // Handle the error here\r\n        console.error('An error occurred:', error);\r\n\r\n        if (error.status === 400) {\r\n          this.toastService.setShowToastmessage({\r\n            severity: 'info',\r\n            summary: '',\r\n            detail: 'Check if you have set your availability, if the problem persists contact us.'\r\n          });\r\n        }\r\n        // You can also throw a new error if needed\r\n        return throwError('Something went wrong');\r\n      })\r\n    ).subscribe((res: any) => {\r\n      this.businessHours = this.calendarService.getBusinessHours(res.calendarAvailability);\r\n      this.dayOffEvents = this.calendarService.createDayOffEvents(res.daysOff);\r\n    });\r\n  }\r\n\r\n  getTeacherLabel(teacher: any): string {\r\n    return `${teacher.firstName} ${teacher.lastName}`;\r\n  }\r\n\r\n  /**\r\n   * Creates an availability request object.\r\n   * @returns The availability request object.\r\n   */\r\n  private createAvailabilityRequestObject() {\r\n    // Determine the ASP user ID based on the role\r\n    const aspUserId = this.role === UserRole.STUDENT ? this.selectedTeacher?.aspUserId ?? null : this.user?.aspUserId ?? null;\r\n\r\n    // If the ASP user ID is not available, return null\r\n    if (!aspUserId) {\r\n      return null;\r\n    }\r\n\r\n    // Create and return the availability request object\r\n    return {\r\n      fromDate: this.calendarStartDate,\r\n      toDate: this.calendarEndDate,\r\n      userId: aspUserId\r\n    };\r\n  }\r\n\r\n  private getPendingLessons(classroom: any): Observable<any> {\r\n    return this.classroomService.gePendingLessons(classroom.id).pipe(\r\n      switchMap((res: any) => {\r\n        return of(res); \r\n      }),\r\n      catchError((error: any) => {\r\n        console.error('Error getting pending lessons:', error);\r\n        // Handle the error or rethrow it if necessary\r\n        return throwError(error);\r\n      })\r\n    )\r\n  }\r\n\r\n  private fetchClassrooms(): void {\r\n    this.subs.add(\r\n    this.classroomService.getLMSUserClassrooms(this.user.id).pipe(\r\n    switchMap((classrooms: Classroom[]) => {\r\n    this.pendingRequests = [];\r\n    this.lessonEvents = [];\r\n    const availableTeachers: User[] = [];\r\n    this.notificationsNumber = 0;\r\n    \r\n    classrooms.forEach((classroom: Classroom) => {\r\n          this.fetchPendingLessons(classroom, this.pendingRequests);\r\n          availableTeachers.push(classroom.teacher!);\r\n    \r\n          classroom.lessons!.forEach((lesson: Lesson) => {\r\n            if (\r\n              lesson.status.toLowerCase().includes(LessonStatus.CANCELED.toLowerCase()) ||\r\n              lesson.status.toLowerCase() === LessonStatus.NO_SHOW.toLowerCase()\r\n            ) {\r\n              return;\r\n            }\r\n            const start = new Date(lesson.startingDate);\r\n            const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\r\n    \r\n            const teacherName = this.generalService.getShortFullName(classroom.teacher);\r\n            this.lessonEvents.push({\r\n              id: lesson.id,\r\n              title: teacherName,\r\n              start: lesson.startingDate,\r\n              end: end,\r\n              allDay: false,\r\n              backgroundColor: '#378006',\r\n              borderColor: '#378006',\r\n              textColor: '#ffffff',\r\n              extendedProps: {\r\n                type: 'lesson',\r\n                lesson: lesson,\r\n                classroom: classroom,\r\n              },\r\n              editable: false,\r\n            });\r\n          });\r\n        });\r\n    \r\n        const uniqueTeachers = availableTeachers.filter((value, index, self) => {\r\n          return self.map((x) => x.id).indexOf(value.id) === index;\r\n        });\r\n    \r\n        this.classrooms = classrooms;\r\n        this.availableTeachers = uniqueTeachers;\r\n        this.selectedTeacher = this.availableTeachers[0];\r\n        this.showCalendar = true;\r\n        return of(classrooms);\r\n      }),\r\n    \r\n      switchMap((classrooms: Classroom[]) => {\r\n        if (this.role === UserRole.STUDENT) {\r\n          return this.classroomService.getAvailableHoursWithTeacher(this.selectedTeacher.aspUserId);\r\n        } else {\r\n          return of(classrooms);\r\n        }\r\n      })\r\n    ).subscribe((res: any) => {\r\n      if (this.role === UserRole.STUDENT) {\r\n        this.showBookingSystemLink = res;\r\n      } else {\r\n        this.showBookingSystemLink = this.classroomService.filterAvailableClassrooms(res).length > 0;\r\n      }\r\n    }));\r\n    }\r\n  \r\n  private fetchPendingLessons(classroom: any, pendingRequests: any[]): void {\r\n    this.subs.add(this.getPendingLessons(classroom).subscribe(\r\n      (response: any) => {\r\n        if (response.length === 0) return;\r\n  \r\n        response.forEach((pendingLesson: any) => {\r\n          let avatar = '';\r\n          console.log(pendingLesson);\r\n          if (pendingLesson.result === 'Pending') {\r\n            let studentRequestedLesson = classroom.classroomStudents.find((student: any) => student.aspUserId === pendingLesson.requestedByAspUserId);\r\n            console.log(pendingLesson.duration);\r\n            if (studentRequestedLesson) {\r\n              avatar = this.role === UserRole.STUDENT ? classroom.teacher.avatarUrl : studentRequestedLesson.avatarUrl;\r\n            }\r\n            let name = this.role === UserRole.STUDENT ? `${classroom.teacher.firstName} ${classroom.teacher.lastName}` : pendingLesson.requestedByName;\r\n            pendingRequests.push({ \r\n              id:pendingLesson.id, \r\n              lesson: pendingLesson, \r\n              requestedName: name, \r\n              avatar:avatar, \r\n              date: moment(pendingLesson.startingDate).format('DD/MM/YYYY'), \r\n              time:  moment(pendingLesson.startingDate).format('HH:mm'), \r\n              duration: pendingLesson.duration });\r\n            this.notificationsNumber++;\r\n          }\r\n        });\r\n      },\r\n      (error: any) => {\r\n        console.log(error);\r\n      }\r\n    ));\r\n  }\r\n\r\n  private initUpdateListener() {\r\n    this.subs.add(this.calendarService.updateListener.subscribe((res: any) => {\r\n      console.log(res);\r\n      if (res) {\r\n        this.onTeacherChange('');\r\n        this.fetchClassrooms();\r\n      }\r\n    }));\r\n  }\r\n}\r\n", "<!-- <app-scheduler></app-scheduler> -->\r\n<div #calendarWrapper>\r\n    @defer(on viewport) {\r\n    <app-block-viewer header=\"Calendar\" headerBackgroundImage=\"/assets/images/dashboard/calendar-banner.svg\"\r\n        blockClass=\"border-radius-bottom-10\" containerClass=\"bg-white px-3 py-2 border-round-2xl \"\r\n        [headerClass]=\"'justify-content-center my-2'\"\r\n        [headerTextClass]=\"'font-xl font-semibold justify-content-center'\">\r\n        <div *ngIf=\"role=='Student'\" class=\"grid justify-content-center lg:justify-content-end font-sm lg:h-3rem\">\r\n            <div class=\"col pb-1 col-fixed\" [hidden]=\"true\">\r\n                <button pbutton=\"\" type=\"button\"\r\n                    class=\"p-element p-button-rounded p-button-sm p-button p-component py-1 btn-gradient\">\r\n                    <div class=\"flex align-items-center gap-1\">\r\n                        <img src=\"/assets/icons/google-calendar.svg\" height=\"22\">\r\n                        <span class=\"font-sm\">Connect with <b>Google Calendar</b></span>\r\n                    </div>\r\n                </button>\r\n            </div>\r\n            <div class=\"col-fixed flex text-center pb-1 font-sm align-items-center justify-content-center\">Availability\r\n                of\r\n                <p-dropdown [options]=\"availableTeachers\" [(ngModel)]=\"selectedTeacher\" placeholder=\"Select Teacher \"\r\n                    [optionLabel]=\"getTeacherLabel\" (ngModelChange)=\"onTeacherChange($event)\"\r\n                    class=\"inline-block ml-1\"></p-dropdown>\r\n            </div>\r\n            <div class=\"col-fixed text-right pb-1 align-items-center\">\r\n                <div class=\"grid grid-nogutter gap-3 left-grid awaitingApproval align-items-center\">\r\n                    <div class=\"col-fixed text-right pb-1\">\r\n                        <app-overlay-panel-block [role]=\"role\" selector=\"awaitingApproval\"\r\n                            [notificationsNumber]=\"notificationsNumber\" buttonIcon=\"awaiting-approval\"\r\n                            buttonText=\"Requested Lessons\" headIcon=\"awaiting-approval-white\"\r\n                            headText=\"Requested Lessons\"\r\n                            headBackgroundImage=\"/assets/images/dashboard/awaiting-aproval-bg.png\">\r\n\r\n                            <div class=\"overflow-y-scroll px-2\" style=\"max-height: 300px;\">\r\n                                <div *ngFor=\"let request of pendingRequests;  let i = index\">\r\n                                    <app-awaiting-approval-notification [index]=\"i+1\" [lesson]=\"request.lesson\"\r\n                                        [lessonId]=\"request.id\" [avatar]=\"request.avatar\"\r\n                                        [requestedName]=\"request.requestedName\" [date]=\"request.date\"\r\n                                        [time]=\"request.time\"\r\n                                        [duration]=\"generalService.convertHoursToMinutesWithSuffix(request.duration)\">\r\n                                    </app-awaiting-approval-notification>\r\n                                </div>\r\n                            </div>\r\n                        </app-overlay-panel-block>\r\n                    </div>\r\n                    <div class=\"col-fixed add-icon cursor-pointer\"\r\n                        [ngClass]=\"{'pointer-events-none opacity-40': !showBookingSystemLink }\"\r\n                        routerLink=\"/dashboard/calendar/booking-system\">\r\n                        <img src=\"/assets/icons/plus-icon-svg-cropped.svg\" alt=\"\">\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- <div class=\"grid justify-content-center flex\">\r\n        <div  class=\"col-5 justify-content-between align-items-center\">\r\n    </div>\r\n    </div> -->\r\n        <div *ngIf=\"role=='Teacher'\" class=\"grid justify-content-end align-items-center gap-2 font-sm h-2rem\">\r\n\r\n            <div class=\"col-fixed text-right pb-1\" [hidden]=\"true\">\r\n                <div class=\"grid left-grid awaitingApproval\">\r\n                    <button pbutton=\"\" type=\"button\"\r\n                        class=\"p-element p-button-rounded p-button-sm p-button p-component py-1 btn-gradient\">\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <img src=\"/assets/icons/google-calendar.svg\" height=\"16\">\r\n                            <span class=\"font-sm\">Connect with <b>Google Calendar</b></span>\r\n                        </div>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-fixed text-right pb-1 p-0\">\r\n                <div class=\"grid left-grid awaitingApproval\">\r\n                    <app-overlay-panel-block [hidden]=\"true\" [role]=\"role\" selector=\"awaitingApproval\"\r\n                        buttonText=\"Days off\" headText=\"Add Days-Off for...\"\r\n                        headBackgroundImage=\"/assets/images/dashboard/awaiting-aproval-bg.png\">\r\n                        <app-teacher-days-off-panel></app-teacher-days-off-panel>\r\n                    </app-overlay-panel-block>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-fixed text-right pb-1\" [hidden]=\"true\">\r\n                <div class=\"grid left-grid awaitingApproval\">\r\n                    <app-overlay-panel-block [role]=\"role\" selector=\"awaitingApproval\" buttonText=\"Reccurring Lessons\"\r\n                        headText=\"Reccurring Lessons\"\r\n                        headBackgroundImage=\"/assets/images/dashboard/calendar/recurring-bg.png\">\r\n                        <app-recurring-lessons-block [classrooms]=\"classrooms\"></app-recurring-lessons-block>\r\n                    </app-overlay-panel-block>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-fixed text-right pb-1\">\r\n                <div class=\"grid left-grid awaitingApproval\">\r\n                    <app-overlay-panel-block [role]=\"role\" selector=\"awaitingApproval\" buttonText=\"Availability\"\r\n                        headText=\"Availability\"\r\n                        headBackgroundImage=\"/assets/images/dashboard/calendar/availability-dialog-bg.png\"\r\n                        [panelWidth]=\"isTabletOrMobile ? '100%' : '26rem'\">\r\n                        <p-scrollPanel #scrollPanel [style]=\"{width: '100%'}\"\r\n                            styleClass=\"custombar1 h-20rem lg:max-h-30rem lg:min-h-22rem\">\r\n                            <app-availability [showHours]=\"false\" [isDialog]=\"true\" [user]=\"user\"></app-availability>\r\n                        </p-scrollPanel>\r\n                    </app-overlay-panel-block>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-fixed text-right pb-1\">\r\n                <div class=\"grid left-grid awaitingApproval\">\r\n                    <app-overlay-panel-block [role]=\"role\" selector=\"awaitingApproval\"\r\n                        [notificationsNumber]=\"notificationsNumber\" buttonText=\"Lesson Requests\"\r\n                        headIcon=\"awaiting-approval-white\" headText=\"Lesson Requests\"\r\n                        headBackgroundImage=\"/assets/images/dashboard/awaiting-aproval-bg.png\"\r\n                        [panelWidth]=\"isTabletOrMobile ? '90%' : '22rem'\">\r\n                        <div class=\"overflow-y-scroll px-2\" style=\"max-height: 300px;\">\r\n                            <div *ngFor=\"let request of pendingRequests;  let i = index\">\r\n                                <app-awaiting-approval-notification [role]=\"role\" [lesson]=\"request.lesson\"\r\n                                    [lessonId]=\"request.id\" [index]=\"i+1\" [date]=\"request.date\" [time]=\"request.time\"\r\n                                    [avatar]=\"request.avatar\" [requestedName]=\"request.requestedName\"\r\n                                    [duration]=\"generalService.convertHoursToMinutesWithSuffix(request.duration)\"></app-awaiting-approval-notification>\r\n                            </div>\r\n                        </div>\r\n                    </app-overlay-panel-block>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-fixed add-icon cursor-pointer\"\r\n                [ngClass]=\"{'pointer-events-none opacity-40': !showBookingSystemLink }\"\r\n                routerLink=\"/dashboard/calendar/booking-system\">\r\n                <img src=\"/assets/icons/plus-icon-svg-cropped.svg\" alt=\"\">\r\n            </div>\r\n\r\n        </div>\r\n        <ng-container *ngIf=\"showCalendar\">\r\n            <div class=\"relative mt-2\" #calendarContainer>\r\n                <app-weekly-availability-calendar [calendarHeight]=\"containerHeight\"\r\n                    (availableTeachersChanged)=\"onAvailableTeacherChange($event)\"\r\n                    (calendarRangeDatesChanged)=\"onCalendarRangeDatesChanged($event)\" [businessHours]=\"businessHours\"\r\n                    [dayOffEvents]=\"dayOffEvents\" [lessonEvents]=\"lessonEvents\" [selectedTeacher]=\"selectedTeacher\"\r\n                    [isTabletOrMobile]=\"isTabletOrMobile\"></app-weekly-availability-calendar>\r\n                <div class=\"border-round-xl p-1 font-xs\">\r\n                    <div\r\n                        class=\"p-2 flex flex-wrap gap-2 flex-row align-items-start sm:align-items-center justify-content-start sm:justify-content-between\">\r\n\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <div class=\"circle-legend availability-bg-color\">\r\n                            </div>\r\n                            Availability\r\n                        </div>\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <div class=\"circle-legend arranged-gradient-lesson-bg\">\r\n                                <img src=\"/assets/icons/lessons/arranged.svg\" width=\"12\" />\r\n                            </div>\r\n                            Arranged\r\n                        </div>\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <div class=\"circle-legend completed-gradient-lesson-bg\">\r\n                                <img src=\"/assets/icons/lessons/completed.svg\" width=\"12\" />\r\n                            </div>\r\n                            Completed\r\n                        </div>\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <div class=\"circle-legend trial-arranged-gradient-lesson-bg\">\r\n                                <img src=\"/assets/icons/lessons/arranged.svg\" width=\"12\" />\r\n                            </div>\r\n                            Trial Arranged\r\n                        </div>\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <div class=\"circle-legend trial-completed-gradient-lesson-bg\">\r\n                                <img src=\"/assets/icons/lessons/completed.svg\" width=\"12\" />\r\n                            </div>\r\n                            Trial Completed\r\n                        </div>\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <div class=\"circle-legend requested-gradient-lesson-bg\">\r\n                                <img src=\"/assets/icons/lessons/requested-white.svg\" width=\"12\" />\r\n                            </div>\r\n                            Requested\r\n                        </div>\r\n\r\n                        <!-- <div class=\"flex align-items-center gap-1\">\r\n                    <div class=\"circle-legend days-off-bg-color\">\r\n                    </div>\r\n                    Days Off\r\n                </div> -->\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n        <div class=\"grid\">\r\n            <div class=\"col\"></div>\r\n        </div>\r\n    </app-block-viewer>\r\n    } @placeholder {\r\n    <span>loading...</span>\r\n    }\r\n</div>\r\n<!-- lessons-history -->\r\n<div class=\"classroom-cards-space py-8\">\r\n    <!-- <app-lesson-rating [mode]=\"'create'\" [lesson]='{\r\n        \"id\": 664,\r\n        \"classroomId\": 130,\r\n        \"status\": \"completed\",\r\n        \"startingDate\": \"2023-06-06T10:00:00\",\r\n        \"duration\": 0.5,\r\n        \"isRecccuring\": false,\r\n        \"recccuringGroupId\": \"c4a625d3-0b55-4765-ad17-ede0ef93be6e\",\r\n        \"bbbLink\": \"\",\r\n        \"answered\": true,\r\n        \"result\": \"By Teacher\",\r\n        \"requestedByAspUserId\": \"c281abee-8f31-4fbf-bfe2-e04d31b91790\",\r\n        \"requestedByName\": \"Nick Melbaa\",\r\n        \"classroom\": null,\r\n        \"errorMessage\": \"\"\r\n    }'></app-lesson-rating> -->\r\n    <app-lessons-history [wrapperScrollHeight]=\"containerHeight\"></app-lessons-history>\r\n</div>"], "mappings": "AAGA,SAASA,UAAU,EAAEC,GAAG,EAAEC,SAAS,QAAO,gBAAgB;AAC1D,SAASC,OAAO,QAAQ,SAAS;AAKjC,SAAeC,QAAQ,QAAQ,gCAAgC;AAC/D,SAAqBC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACjD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAAiBC,YAAY,QAAQ,kCAAkC;AACvE,SAA4BC,WAAW,QAAQ,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICoBrCC,EAAA,CAAAC,cAAA,UAA6D;IACzDD,EAAA,CAAAE,SAAA,6CAKqC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IANkCH,EAAA,CAAAI,SAAA,EAAa;IAI7CJ,EAJgC,CAAAK,UAAA,UAAAC,IAAA,KAAa,WAAAC,UAAA,CAAAC,MAAA,CAA0B,aAAAD,UAAA,CAAAE,EAAA,CAChD,WAAAF,UAAA,CAAAG,MAAA,CAA0B,kBAAAH,UAAA,CAAAI,aAAA,CACV,SAAAJ,UAAA,CAAAK,IAAA,CAAsB,SAAAL,UAAA,CAAAM,IAAA,CACxC,aAAAC,MAAA,CAAAC,cAAA,CAAAC,+BAAA,CAAAT,UAAA,CAAAU,QAAA,EACwD;;;;;;IA3BjGjB,EAJZ,CAAAC,cAAA,cAA0G,cACtD,iBAE8C,cAC3C;IACvCD,EAAA,CAAAE,SAAA,cAAyD;IACzDF,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAkB,MAAA,oBAAa;IAAAlB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAkB,MAAA,sBAAe;IAGjElB,EAHiE,CAAAG,YAAA,EAAI,EAAO,EAC9D,EACD,EACP;IACNH,EAAA,CAAAC,cAAA,cAA+F;IAAAD,EAAA,CAAAkB,MAAA,wBAE3F;IAAAlB,EAAA,CAAAC,cAAA,sBAE8B;IAFYD,EAAA,CAAAmB,gBAAA,2BAAAC,8EAAAC,MAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAwB,aAAA;MAAAxB,EAAA,CAAAyB,kBAAA,CAAAX,MAAA,CAAAY,eAAA,EAAAL,MAAA,MAAAP,MAAA,CAAAY,eAAA,GAAAL,MAAA;MAAA,OAAArB,EAAA,CAAA2B,WAAA,CAAAN,MAAA;IAAA,EAA6B;IACnCrB,EAAA,CAAA4B,UAAA,2BAAAR,8EAAAC,MAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAiBb,MAAA,CAAAe,eAAA,CAAAR,MAAA,CAAuB;IAAA,EAAC;IAEjFrB,EADkC,CAAAG,YAAA,EAAa,EACzC;IAUUH,EAThB,CAAAC,cAAA,eAA0D,eAC8B,eACzC,mCAKwC,eAER;IAC3DD,EAAA,CAAA8B,UAAA,KAAAC,+CAAA,kBAA6D;IAUzE/B,EAFQ,CAAAG,YAAA,EAAM,EACgB,EACxB;IACNH,EAAA,CAAAC,cAAA,eAEoD;IAChDD,EAAA,CAAAE,SAAA,eAA0D;IAI1EF,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IA3C8BH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAK,UAAA,gBAAe;IAW/BL,EAAA,CAAAI,SAAA,IAA6B;IAA7BJ,EAAA,CAAAK,UAAA,YAAAS,MAAA,CAAAkB,iBAAA,CAA6B;IAAChC,EAAA,CAAAiC,gBAAA,YAAAnB,MAAA,CAAAY,eAAA,CAA6B;IACnE1B,EAAA,CAAAK,UAAA,gBAAAS,MAAA,CAAAoB,eAAA,CAA+B;IAMFlC,EAAA,CAAAI,SAAA,GAAa;IAClCJ,EADqB,CAAAK,UAAA,SAAAS,MAAA,CAAAqB,IAAA,CAAa,wBAAArB,MAAA,CAAAsB,mBAAA,CACS;IAMdpC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,YAAAS,MAAA,CAAAuB,eAAA,CAAqB;IAYtDrC,EAAA,CAAAI,SAAA,EAAuE;IAAvEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAsC,eAAA,IAAAC,GAAA,GAAAzB,MAAA,CAAA0B,qBAAA,EAAuE;;;;;IAiEnExC,EAAA,CAAAC,cAAA,UAA6D;IACzDD,EAAA,CAAAE,SAAA,6CAGuH;IAC3HF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJkCH,EAAA,CAAAI,SAAA,EAAa;IAG7CJ,EAHgC,CAAAK,UAAA,SAAAS,MAAA,CAAAqB,IAAA,CAAa,WAAAM,UAAA,CAAAjC,MAAA,CAA0B,aAAAiC,UAAA,CAAAhC,EAAA,CAChD,UAAAiC,IAAA,KAAc,SAAAD,UAAA,CAAA7B,IAAA,CAAsB,SAAA6B,UAAA,CAAA5B,IAAA,CAAsB,WAAA4B,UAAA,CAAA/B,MAAA,CACxD,kBAAA+B,UAAA,CAAA9B,aAAA,CAAwC,aAAAG,MAAA,CAAAC,cAAA,CAAAC,+BAAA,CAAAyB,UAAA,CAAAxB,QAAA,EACY;;;;;IAnDzFjB,EANhB,CAAAC,cAAA,cAAsG,cAE3C,cACN,iBAEiD,cAC3C;IACvCD,EAAA,CAAAE,SAAA,cAAyD;IACzDF,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAkB,MAAA,oBAAa;IAAAlB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAkB,MAAA,sBAAe;IAIrElB,EAJqE,CAAAG,YAAA,EAAI,EAAO,EAC9D,EACD,EACP,EACJ;IAIEH,EAFR,CAAAC,cAAA,eAA2C,eACM,mCAGkC;IACvED,EAAA,CAAAE,SAAA,kCAAyD;IAGrEF,EAFQ,CAAAG,YAAA,EAA0B,EACxB,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAuD,eACN,mCAGoC;IACzED,EAAA,CAAAE,SAAA,uCAAqF;IAGjGF,EAFQ,CAAAG,YAAA,EAA0B,EACxB,EACJ;IAOMH,EANZ,CAAAC,cAAA,eAAuC,eACU,mCAIc,4BAEe;IAC9DD,EAAA,CAAAE,SAAA,4BAAyF;IAIzGF,EAHY,CAAAG,YAAA,EAAgB,EACM,EACxB,EACJ;IAQMH,EAPZ,CAAAC,cAAA,eAAuC,eACU,mCAKa,eACa;IAC3DD,EAAA,CAAA8B,UAAA,KAAAa,+CAAA,kBAA6D;IAS7E3C,EAHY,CAAAG,YAAA,EAAM,EACgB,EACxB,EACJ;IAENH,EAAA,CAAAC,cAAA,eAEoD;IAChDD,EAAA,CAAAE,SAAA,eAA0D;IAGlEF,EAFI,CAAAG,YAAA,EAAM,EAEJ;;;;IApEqCH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAK,UAAA,gBAAe;IAcrBL,EAAA,CAAAI,SAAA,IAAe;IAACJ,EAAhB,CAAAK,UAAA,gBAAe,SAAAS,MAAA,CAAAqB,IAAA,CAAc;IAOvBnC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,gBAAe;IAErBL,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAqB,IAAA,CAAa;IAGLnC,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,UAAA,eAAAS,MAAA,CAAA8B,UAAA,CAAyB;IAMjC5C,EAAA,CAAAI,SAAA,GAAa;IAGlCJ,EAHqB,CAAAK,UAAA,SAAAS,MAAA,CAAAqB,IAAA,CAAa,eAAArB,MAAA,CAAA+B,gBAAA,oBAGgB;IACtB7C,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAA8C,UAAA,CAAA9C,EAAA,CAAA+C,eAAA,KAAAC,GAAA,EAAyB;IAE/BhD,EAAA,CAAAI,SAAA,GAAmB;IAAmBJ,EAAtC,CAAAK,UAAA,oBAAmB,kBAAkB,SAAAS,MAAA,CAAAmC,IAAA,CAAc;IAOpDjD,EAAA,CAAAI,SAAA,GAAa;IAIlCJ,EAJqB,CAAAK,UAAA,SAAAS,MAAA,CAAAqB,IAAA,CAAa,wBAAArB,MAAA,CAAAsB,mBAAA,CACS,eAAAtB,MAAA,CAAA+B,gBAAA,mBAGM;IAEpB7C,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,YAAAS,MAAA,CAAAuB,eAAA,CAAqB;IAY1DrC,EAAA,CAAAI,SAAA,EAAuE;IAAvEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAsC,eAAA,KAAAC,GAAA,GAAAzB,MAAA,CAAA0B,qBAAA,EAAuE;;;;;;IAM/ExC,EAAA,CAAAkD,uBAAA,GAAmC;IAE3BlD,EADJ,CAAAC,cAAA,iBAA8C,2CAKA;IAFtCD,EADA,CAAA4B,UAAA,sCAAAuB,uHAAA9B,MAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAA8B,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAA4Bb,MAAA,CAAAuC,wBAAA,CAAAhC,MAAA,CAAgC;IAAA,EAAC,uCAAAiC,wHAAAjC,MAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAA8B,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAChCb,MAAA,CAAAyC,2BAAA,CAAAlC,MAAA,CAAmC;IAAA,EAAC;IAE3BrB,EAAA,CAAAG,YAAA,EAAmC;IAKrEH,EAJR,CAAAC,cAAA,cAAyC,cAEkG,cAExF;IACvCD,EAAA,CAAAE,SAAA,cACM;IACNF,EAAA,CAAAkB,MAAA,qBACJ;IAAAlB,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,cAA2C,eACgB;IACnDD,EAAA,CAAAE,SAAA,eAA2D;IAC/DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAkB,MAAA,kBACJ;IAAAlB,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAA2C,eACiB;IACpDD,EAAA,CAAAE,SAAA,eAA4D;IAChEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAkB,MAAA,mBACJ;IAAAlB,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAA2C,eACsB;IACzDD,EAAA,CAAAE,SAAA,eAA2D;IAC/DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAkB,MAAA,wBACJ;IAAAlB,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAA2C,eACuB;IAC1DD,EAAA,CAAAE,SAAA,eAA4D;IAChEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAkB,MAAA,yBACJ;IAAAlB,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAA2C,eACiB;IACpDD,EAAA,CAAAE,SAAA,eAAkE;IACtEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAkB,MAAA,mBACJ;IASZlB,EATY,CAAAG,YAAA,EAAM,EAOJ,EACJ,EACJ;;;;;IApDgCH,EAAA,CAAAI,SAAA,GAAkC;IAIhEJ,EAJ8B,CAAAK,UAAA,mBAAAS,MAAA,CAAA0C,eAAA,CAAkC,kBAAA1C,MAAA,CAAA2C,aAAA,CAEiC,iBAAA3C,MAAA,CAAA4C,YAAA,CACpE,iBAAA5C,MAAA,CAAA6C,YAAA,CAA8B,oBAAA7C,MAAA,CAAAY,eAAA,CAAoC,qBAAAZ,MAAA,CAAA+B,gBAAA,CAC1D;;;;;IAnIrD7C,EAAA,CAAAC,cAAA,0BAGuE;IA0HnED,EAzHA,CAAA8B,UAAA,IAAA8B,wCAAA,mBAA0G,IAAAC,wCAAA,mBAkDJ,IAAAC,iDAAA,2BAuEnE;IAwDnC9D,EAAA,CAAAC,cAAA,aAAkB;IACdD,EAAA,CAAAE,SAAA,cAAuB;IAE/BF,EADI,CAAAG,YAAA,EAAM,EACS;;;;IArLfH,EADA,CAAAK,UAAA,8CAA6C,mEACqB;IAC5DL,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAqB,IAAA,cAAqB;IAkDrBnC,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAqB,IAAA,cAAqB;IAuEZnC,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAiD,YAAA,CAAkB;;;;;IA6DrC/D,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAkB,MAAA,iBAAU;IAAAlB,EAAA,CAAAG,YAAA,EAAO;;;ADtK3B,OAAM,MAAO6D,iBAAiB;EA0B5BC,YACUC,aAA4B,EAC5BC,WAAwB,EACzBpD,cAA8B,EAC7BqD,gBAAkC,EAClCC,eAAgC,EAChCC,WAAwB,EACzBC,YAA0B,EACzBC,GAAsB;IAPtB,KAAAN,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAApD,cAAc,GAAdA,cAAc;IACb,KAAAqD,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAC,GAAG,GAAHA,GAAG;IAjCL,KAAAC,IAAI,GAAG,IAAIhF,OAAO,EAAE;IACoB,KAAAiF,eAAe,GAAe,EAAgB;IAG9F,KAAAvC,IAAI,GAAW,EAAE;IACjB,KAAAwC,OAAO,GAAU,EAAE;IACnB,KAAA1B,IAAI,GAAG,EAAU;IACjB,KAAAvB,eAAe,GAAQ,IAAI;IAC3B,KAAAM,iBAAiB,GAAU,EAAE;IAC7B,KAAAwB,eAAe,GAAG,EAAE;IACpB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAiB,oBAAoB,GAAU,EAAE;IAChC,KAAApC,qBAAqB,GAAG,KAAK;IAC7B,KAAAI,UAAU,GAAU,EAAE;IACtB,KAAAmB,YAAY,GAAG,KAAK;IACpB,KAAAc,iBAAiB,GAAG,IAAIC,IAAI,EAAE;IAC9B,KAAAC,eAAe,GAAG,IAAID,IAAI,EAAE;IAC5B,KAAAE,SAAS,GAAGtF,QAAQ;IACpB,KAAA2C,eAAe,GAAU,EAAE;IAE3B,KAAA4C,YAAY,GAAGnF,YAAY;IAC3B,KAAA+C,gBAAgB,GAAG,KAAK;EAWpB;EAEJqC,QAAQA,CAAA;IACN,IAAI,CAACP,OAAO,GAAG,EAAE;IACjB,IAAI,CAACxC,IAAI,GAAG,IAAI,CAACgC,WAAW,CAACgB,eAAe,EAAE,CAAChD,IAAI;IACnD,IAAI,CAACc,IAAI,GAAG,IAAI,CAACkB,WAAW,CAACgB,eAAe,EAAE;IAE9C,IAAI,CAAC/C,mBAAmB,GAAG,CAAC;IAE5B,IAAI,CAACqC,IAAI,CAACW,IAAI,GAAG,IAAI,CAACrE,cAAc,CAACsE,UAAU,CAACC,IAAI,CAClD9F,SAAS,CAAC+F,GAAG,IAAG;MACd,IAAI,CAAC1C,gBAAgB,GAAG,CAAC0C,GAAG,CAACC,MAAM;MACnC,IAAI,CAACD,GAAG,CAACE,OAAO,EAAE;QAChB,OAAO9F,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,MAAM;QACL,OAAO,IAAI,CAACuE,aAAa,CAACwB,cAAc;MAC1C;IACF,CAAC,CAAC,CACH,CAACC,SAAS,CAAEJ,GAAG,IAAI;MAClB,IAAIA,GAAG,KAAK,CAAC,EAAE;QACb,IAAI,CAAC/B,eAAe,GAAI,OAAO;MACjC,CAAC,MAAM,IAAI+B,GAAG,KAAK,CAAC,EAAE;QACpB,MAAMK,UAAU,GAAGL,GAAG;QACtB,IAAI,CAACb,eAAe,CAACmB,aAAa,CAACC,KAAK,CAACC,MAAM,GAAGR,GAAG,GAAG,IAAI;QAC5D,IAAI,CAAC/B,eAAe,GAAIoC,UAAU,GAAG,GAAG,GAAI,IAAI;QAChD,IAAI,CAACpB,GAAG,CAACwB,aAAa,EAAE;MAC1B;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,eAAe,EAAE;IAEtB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC1B,IAAI,CAAC2B,WAAW,EAAE;EACzB;EAEQC,sBAAsBA,CAAC1B,OAAY;IACzC,IAAI,CAAC,IAAI,CAAC2B,+BAA+B,EAAE,EAAE;MAC3C,OAAO3G,EAAE,CAAC,IAAI,CAAC;IACjB;IACA,OAAO,IAAI,CAAC0E,eAAe,CAACkC,4BAA4B,CAAC,IAAI,CAACD,+BAA+B,EAAG,CAAC,CAAChB,IAAI,CACpG/F,GAAG,CAAEgG,GAAQ,IAAI;MACf,MAAMiB,OAAO,GAAGjB,GAAG,CAACiB,OAAO;MAE3B;MACA,IAAI,CAAC/C,aAAa,GAAG,IAAI,CAACY,eAAe,CAACoC,gBAAgB,CAAClB,GAAG,CAACmB,oBAAoB,CAAC;MAEpF;MACA,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACW,eAAe,CAACsC,kBAAkB,CAACH,OAAO,CAAC;MAEpE,OAAO,IAAI;IACb,CAAC,CAAC,EACFlH,UAAU,CAAEsH,KAAU,IAAI;MACxB,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB;QACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;MAEA,OAAOnH,UAAU,CAACgH,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAEAI,wBAAwBA,CAACC,OAAc,EAAEC,SAAiB;IACxD,OAAOD,OAAO,CAACE,MAAM,CAAE3G,MAAW,IAAKA,MAAM,CAACmE,OAAO,CAAClE,EAAE,KAAKyG,SAAS,CAAC;EACzE;EAEArF,eAAeA,CAACuF,KAAU;IACxB,IAAI,CAAC,IAAI,CAAC1F,eAAe,EAAE;MACzB;IACF;IACAoF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACrF,eAAe,CAAC;IACtD;IACA,IAAI,CAAC+B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACgB,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAAChB,sBAAsB,CAAC,IAAI,CAAC3E,eAAe,CAAC,CAAC4D,IAAI,CAClE9F,SAAS,CAAE8H,mBAAmB,IAAI;MAChC;MACA,OAAO,IAAI,CAAClD,gBAAgB,CAACmD,4BAA4B,CAAC,IAAI,CAAC7F,eAAe,CAAC8F,SAAS,CAAC;IAC3F,CAAC,CAAC,CACH,CAAC7B,SAAS,CAAE8B,cAAc,IAAI;MAC7B,IAAI,CAAC7C,oBAAoB,GAAG,IAAI,CAACjB,YAAY;MAC7C,IAAI,CAACU,eAAe,CAACqD,wBAAwB,CAAC,IAAI,CAAC;IACrD,CAAC,CAAC,CAAC;EAEL;EAEArE,wBAAwBA,CAAC+D,KAAU;IAEjCN,OAAO,CAACC,GAAG,CAACK,KAAK,CAAC;IAClB,IAAI,CAACzC,OAAO,GAAGyC,KAAK;EACtB;EAEA7D,2BAA2BA,CAAC6D,KAAU;IACpC,IAAI,CAAC3D,aAAa,GAAG,EAAE;IACvBqD,OAAO,CAACC,GAAG,CAACK,KAAK,CAAC;IAClB,IAAI,CAACvC,iBAAiB,GAAGuC,KAAK,CAACO,QAAQ;IACvC,IAAI,CAAC5C,eAAe,GAAGqC,KAAK,CAACQ,MAAM;IACnC,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAI,IAAI,CAAC1F,IAAI,KAAKzC,QAAQ,CAACoI,OAAO,EAAE;MAClCD,MAAM,GAAG,IAAI,CAACnG,eAAe,CAAC8F,SAAS;IACzC,CAAC,MAAM;MACLK,MAAM,GAAG,IAAI,CAAC5E,IAAI,CAACuE,SAAmB;IACxC;IACAJ,KAAK,CAACS,MAAM,GAAGA,MAAM;IAErB,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA,MAAME,OAAO,GAAG,IAAIhI,WAAW,CAAC;MAC9B,4BAA4B,EAAE;KAC/B,CAAC;IACF,IAAI,CAAC0E,IAAI,CAACW,IAAI,GAAG,IAAI,CAACf,eAAe,CAACkC,4BAA4B,CAACa,KAAK,EAAEW,OAAO,CAAC,CAACzC,IAAI,CACrFhG,UAAU,CAAEsH,KAAK,IAAI;MACnB;MACAE,OAAO,CAACF,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAE1C,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB,IAAI,CAACtC,YAAY,CAACyD,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ;MACA;MACA,OAAOvI,UAAU,CAAC,sBAAsB,CAAC;IAC3C,CAAC,CAAC,CACH,CAAC+F,SAAS,CAAEJ,GAAQ,IAAI;MACvB,IAAI,CAAC9B,aAAa,GAAG,IAAI,CAACY,eAAe,CAACoC,gBAAgB,CAAClB,GAAG,CAACmB,oBAAoB,CAAC;MACpF,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACW,eAAe,CAACsC,kBAAkB,CAACpB,GAAG,CAACiB,OAAO,CAAC;IAC1E,CAAC,CAAC;EACJ;EAEAtE,eAAeA,CAACyC,OAAY;IAC1B,OAAO,GAAGA,OAAO,CAACyD,SAAS,IAAIzD,OAAO,CAAC0D,QAAQ,EAAE;EACnD;EAEA;;;;EAIQ/B,+BAA+BA,CAAA;IACrC;IACA,MAAMkB,SAAS,GAAG,IAAI,CAACrF,IAAI,KAAKzC,QAAQ,CAACoI,OAAO,GAAG,IAAI,CAACpG,eAAe,EAAE8F,SAAS,IAAI,IAAI,GAAG,IAAI,CAACvE,IAAI,EAAEuE,SAAS,IAAI,IAAI;IAEzH;IACA,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IAEA;IACA,OAAO;MACLG,QAAQ,EAAE,IAAI,CAAC9C,iBAAiB;MAChC+C,MAAM,EAAE,IAAI,CAAC7C,eAAe;MAC5B8C,MAAM,EAAEL;KACT;EACH;EAEQc,iBAAiBA,CAACC,SAAc;IACtC,OAAO,IAAI,CAACnE,gBAAgB,CAACoE,gBAAgB,CAACD,SAAS,CAAC9H,EAAE,CAAC,CAAC6E,IAAI,CAC9D9F,SAAS,CAAE+F,GAAQ,IAAI;MACrB,OAAO5F,EAAE,CAAC4F,GAAG,CAAC;IAChB,CAAC,CAAC,EACFjG,UAAU,CAAEsH,KAAU,IAAI;MACxBE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACA,OAAOhH,UAAU,CAACgH,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAEQX,eAAeA,CAAA;IACrB,IAAI,CAACxB,IAAI,CAAC4C,GAAG,CACb,IAAI,CAACjD,gBAAgB,CAACqE,oBAAoB,CAAC,IAAI,CAACxF,IAAI,CAACxC,EAAE,CAAC,CAAC6E,IAAI,CAC7D9F,SAAS,CAAEoD,UAAuB,IAAI;MACtC,IAAI,CAACP,eAAe,GAAG,EAAE;MACzB,IAAI,CAACsB,YAAY,GAAG,EAAE;MACtB,MAAM3B,iBAAiB,GAAW,EAAE;MACpC,IAAI,CAACI,mBAAmB,GAAG,CAAC;MAE5BQ,UAAU,CAAC8F,OAAO,CAAEH,SAAoB,IAAI;QACtC,IAAI,CAACI,mBAAmB,CAACJ,SAAS,EAAE,IAAI,CAAClG,eAAe,CAAC;QACzDL,iBAAiB,CAAC4G,IAAI,CAACL,SAAS,CAAC5D,OAAQ,CAAC;QAE1C4D,SAAS,CAACtB,OAAQ,CAACyB,OAAO,CAAElI,MAAc,IAAI;UAC5C,IACEA,MAAM,CAACqG,MAAM,CAACgC,WAAW,EAAE,CAACC,QAAQ,CAAChJ,YAAY,CAACiJ,QAAQ,CAACF,WAAW,EAAE,CAAC,IACzErI,MAAM,CAACqG,MAAM,CAACgC,WAAW,EAAE,KAAK/I,YAAY,CAACkJ,OAAO,CAACH,WAAW,EAAE,EAClE;YACA;UACF;UACA,MAAMI,KAAK,GAAG,IAAInE,IAAI,CAACtE,MAAM,CAAC0I,YAAY,CAAC;UAC3C,MAAMC,GAAG,GAAG,IAAIrE,IAAI,CAACmE,KAAK,CAACG,OAAO,EAAE,GAAG5I,MAAM,CAACS,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;UAExE,MAAMoI,WAAW,GAAG,IAAI,CAACtI,cAAc,CAACuI,gBAAgB,CAACf,SAAS,CAAC5D,OAAO,CAAC;UAC3E,IAAI,CAAChB,YAAY,CAACiF,IAAI,CAAC;YACrBnI,EAAE,EAAED,MAAM,CAACC,EAAE;YACb8I,KAAK,EAAEF,WAAW;YAClBJ,KAAK,EAAEzI,MAAM,CAAC0I,YAAY;YAC1BC,GAAG,EAAEA,GAAG;YACRK,MAAM,EAAE,KAAK;YACbC,eAAe,EAAE,SAAS;YAC1BC,WAAW,EAAE,SAAS;YACtBC,SAAS,EAAE,SAAS;YACpBC,aAAa,EAAE;cACbC,IAAI,EAAE,QAAQ;cACdrJ,MAAM,EAAEA,MAAM;cACd+H,SAAS,EAAEA;aACZ;YACDuB,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG/H,iBAAiB,CAACmF,MAAM,CAAC,CAAC6C,KAAK,EAAEC,KAAK,EAAEC,IAAI,KAAI;QACrE,OAAOA,IAAI,CAAC3K,GAAG,CAAE4K,CAAC,IAAKA,CAAC,CAAC1J,EAAE,CAAC,CAAC2J,OAAO,CAACJ,KAAK,CAACvJ,EAAE,CAAC,KAAKwJ,KAAK;MAC1D,CAAC,CAAC;MAEF,IAAI,CAACrH,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACZ,iBAAiB,GAAG+H,cAAc;MACvC,IAAI,CAACrI,eAAe,GAAG,IAAI,CAACM,iBAAiB,CAAC,CAAC,CAAC;MAChD,IAAI,CAAC+B,YAAY,GAAG,IAAI;MACxB,OAAOpE,EAAE,CAACiD,UAAU,CAAC;IACvB,CAAC,CAAC,EAEFpD,SAAS,CAAEoD,UAAuB,IAAI;MACpC,IAAI,IAAI,CAACT,IAAI,KAAKzC,QAAQ,CAACoI,OAAO,EAAE;QAClC,OAAO,IAAI,CAAC1D,gBAAgB,CAACmD,4BAA4B,CAAC,IAAI,CAAC7F,eAAe,CAAC8F,SAAS,CAAC;MAC3F,CAAC,MAAM;QACL,OAAO7H,EAAE,CAACiD,UAAU,CAAC;MACvB;IACF,CAAC,CAAC,CACH,CAAC+C,SAAS,CAAEJ,GAAQ,IAAI;MACvB,IAAI,IAAI,CAACpD,IAAI,KAAKzC,QAAQ,CAACoI,OAAO,EAAE;QAClC,IAAI,CAACtF,qBAAqB,GAAG+C,GAAG;MAClC,CAAC,MAAM;QACL,IAAI,CAAC/C,qBAAqB,GAAG,IAAI,CAAC4B,gBAAgB,CAACiG,yBAAyB,CAAC9E,GAAG,CAAC,CAAC+E,MAAM,GAAG,CAAC;MAC9F;IACF,CAAC,CAAC,CAAC;EACH;EAEM3B,mBAAmBA,CAACJ,SAAc,EAAElG,eAAsB;IAChE,IAAI,CAACoC,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACiB,iBAAiB,CAACC,SAAS,CAAC,CAAC5C,SAAS,CACtD4E,QAAa,IAAI;MAChB,IAAIA,QAAQ,CAACD,MAAM,KAAK,CAAC,EAAE;MAE3BC,QAAQ,CAAC7B,OAAO,CAAE8B,aAAkB,IAAI;QACtC,IAAI9J,MAAM,GAAG,EAAE;QACfoG,OAAO,CAACC,GAAG,CAACyD,aAAa,CAAC;QAC1B,IAAIA,aAAa,CAACC,MAAM,KAAK,SAAS,EAAE;UACtC,IAAIC,sBAAsB,GAAGnC,SAAS,CAACoC,iBAAiB,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACrD,SAAS,KAAKgD,aAAa,CAACM,oBAAoB,CAAC;UACzIhE,OAAO,CAACC,GAAG,CAACyD,aAAa,CAACvJ,QAAQ,CAAC;UACnC,IAAIyJ,sBAAsB,EAAE;YAC1BhK,MAAM,GAAG,IAAI,CAACyB,IAAI,KAAKzC,QAAQ,CAACoI,OAAO,GAAGS,SAAS,CAAC5D,OAAO,CAACoG,SAAS,GAAGL,sBAAsB,CAACK,SAAS;UAC1G;UACA,IAAIC,IAAI,GAAG,IAAI,CAAC7I,IAAI,KAAKzC,QAAQ,CAACoI,OAAO,GAAG,GAAGS,SAAS,CAAC5D,OAAO,CAACyD,SAAS,IAAIG,SAAS,CAAC5D,OAAO,CAAC0D,QAAQ,EAAE,GAAGmC,aAAa,CAACS,eAAe;UAC1I5I,eAAe,CAACuG,IAAI,CAAC;YACnBnI,EAAE,EAAC+J,aAAa,CAAC/J,EAAE;YACnBD,MAAM,EAAEgK,aAAa;YACrB7J,aAAa,EAAEqK,IAAI;YACnBtK,MAAM,EAACA,MAAM;YACbE,IAAI,EAAEf,MAAM,CAAC2K,aAAa,CAACtB,YAAY,CAAC,CAACgC,MAAM,CAAC,YAAY,CAAC;YAC7DrK,IAAI,EAAGhB,MAAM,CAAC2K,aAAa,CAACtB,YAAY,CAAC,CAACgC,MAAM,CAAC,OAAO,CAAC;YACzDjK,QAAQ,EAAEuJ,aAAa,CAACvJ;WAAU,CAAC;UACrC,IAAI,CAACmB,mBAAmB,EAAE;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC,EACAwE,KAAU,IAAI;MACbE,OAAO,CAACC,GAAG,CAACH,KAAK,CAAC;IACpB,CAAC,CACF,CAAC;EACJ;EAEQV,kBAAkBA,CAAA;IACxB,IAAI,CAACzB,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAAChD,eAAe,CAAC8G,cAAc,CAACxF,SAAS,CAAEJ,GAAQ,IAAI;MACvEuB,OAAO,CAACC,GAAG,CAACxB,GAAG,CAAC;MAChB,IAAIA,GAAG,EAAE;QACP,IAAI,CAAC1D,eAAe,CAAC,EAAE,CAAC;QACxB,IAAI,CAACoE,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CAAC;EACL;EAAC,QAAAmF,CAAA,G;qBA/TUpH,iBAAiB,EAAAhE,EAAA,CAAAqL,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAvL,EAAA,CAAAqL,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzL,EAAA,CAAAqL,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3L,EAAA,CAAAqL,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA7L,EAAA,CAAAqL,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAA/L,EAAA,CAAAqL,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAjM,EAAA,CAAAqL,iBAAA,CAAAa,EAAA,CAAAC,YAAA,GAAAnM,EAAA,CAAAqL,iBAAA,CAAArL,EAAA,CAAAoM,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBrI,iBAAiB;IAAAsI,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCtB9BzM,EAAA,CAAAC,cAAA,mBAAsB;QA2LhBD,EA1LF,CAAA8B,UAAA,IAAA6K,kCAAA,OA4LC,IAAAC,6CAAA;QA5LD5M,EAAA,CAAA6M,OAAA,OAAAC,gCAAA,UA4LC;QA5LM9M,EAAA,CAAA+M,iBAAA,OAAW;QA6LtB/M,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAAwC;QAiBpCD,EAAA,CAAAE,SAAA,6BAAmF;QACvFF,EAAA,CAAAG,YAAA,EAAM;;;QADmBH,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAAK,UAAA,wBAAAqM,GAAA,CAAAlJ,eAAA,CAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}